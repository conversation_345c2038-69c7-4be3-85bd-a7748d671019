import braintrust from "braintrust";
import { z } from "zod";

const project = braintrust.projects.create({ name: "Your Project Name" }); // Replace with your actual project name

project.scorers.create({
  name: "Debug Test Scorer TS",
  slug: "debug-test-scorer-ts",
  description: "Simple test scorer for debugging",
  parameters: z.object({
    output: z.string(),
    expected: z.string(),
  }),
  handler: async ({ output, expected }) => {
    return output === expected ? 1 : 0;
  },
});
