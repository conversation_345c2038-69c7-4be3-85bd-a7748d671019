import braintrust
from pydantic import BaseModel

# Simple test scorer
class Input(BaseModel):
    output: str
    expected: str

def simple_scorer(output: str, expected: str) -> float:
    return 1.0 if output == expected else 0.0

project = braintrust.projects.create("Your Project Name")  # Replace with your actual project name
project.scorers.create(
    name="Debug Test Scorer",
    slug="debug-test-scorer",
    description="Simple test scorer for debugging",
    parameters=Input,
    handler=simple_scorer,
)
