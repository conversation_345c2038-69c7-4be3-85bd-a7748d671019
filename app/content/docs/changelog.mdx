---
title: "Changelog"
---

import { <PERSON><PERSON><PERSON>s, PYTab, TSTab } from "#/ui/docs/code-tabs";
import { LoomVideo } from "#/ui/docs/loom";
import Link from "fumadocs-core/link";
import { Callout } from "fumadocs-ui/components/callout";
import { Step, Steps } from "fumadocs-ui/components/steps";
import Image from 'next/image';

# Changelog

## Week of 2025-10-05

### SDK Integrations: LangChain / LangGraph (TypeScript) (version 0.1.0)

- **breaking change** Braintrust is now a peer dependency. Please add a direct dependency to braintrust starting v0.1.0. This ensures that node package managers will install one version and the langchain-js library uses the installed version.

## Week of 2025-09-29

- Added Anthropic Claude 4.5 Sonnet support
- Fixed Gemini schema support to enable proper function calling and structured outputs when using Google's Gemini models through Braintrust and the AI proxy
- Added Claude Agent SDK Integration support
- Added Gemini Flash and Lite Preview (Sept 2025) support
- Improved prompt detail chat logging and added link to corresponding trace
- Fixed bugs with parallel tool calling in Loop
- Enabled Loop to write BTQL queries against arbitrary data sources on non-BTQL-sandbox pages

### Python SDK version 0.3.1

- Ensure experiments use SpanComponentsV3 by default.

### Python SDK version 0.3.0

- Added OpenTelemetry compatibility mode for seamless integration between Braintrust and OTEL tracing
- Added `setup_claude_agent_sdk` for automatic tracing of Claude Agent SDK applications
- Improved Anthropic wrapper to log consistent input/output format
- Added `strict` parameter to `Prompt.build` for strict schema validation
- Added SpanComponentsV4 support

### TypeScript SDK version 0.4.0

- Added `wrapClaudeAgentSDK` for automatic tracing of Claude Agent SDK applications
- Improved Anthropic wrapper to log consistent input/output format
- Fixed AI SDK model detection in `wrapGenerate` callback
- Added SpanComponentsV4 support

### Data plane (1.1.23)

- BTQL enhancements:
	- Sampling operator
	- Much faster score and metric aggregations
	- Traces with no root span now show up in the summary table, which allows you to filter to only AI spans in traces that contain only AI spans **without** sending all root spans.
- Faster brainstore indexing
	- Improve indexing performance by reducing conflicts between compactions and merges while indexing hot data.
	- Fix compaction for comments
	- Improve vacuuming and retention performance
- Disable parallel tool calls for Azure models

### SDK Integrations: Google ADK (version v0.2.1)

- Simplified SDK setup with new `setup_adk` replacing `setup_braintrust`

## Week of 2025-09-22

- Added support for creating datasets and scorers with Loop from the experiment, dataset, and logs pages
- Resolved excessive `localStorage` usage in Loop and BTQL sandbox
- Improved Loop's `from` clause handling in the BTQL sandbox
- Fixed cross-tab syncing and session restoration bugs in Loop
- Prompt/scorer activity view UI updates
  - Before: selecting a version showed a diff vs. the current editor content, where the selected version is the base of the diff.
  - After: prompt versions can be viewed without diffing vs. editor. When diff is enabled, version is shown as incoming, to indicate what would occur when reverting to that version.

## SDK Integrations: LangChain / LangGraph (Python) (version 0.0.5)

- Fixed Anthropic and other model's metrics reporting correctly. Credit: eilonmor.
- Removes Python 3.8 (past EOL) support.
- Fixed LLM span type to default to task.

## SDK Integrations: LangChain / LangGraph (TypeScript) (version 0.0.9)

- Fixed Anthropic and other model's metrics reporting correctly.
- Fixed LLM span type to default to task.

## Week of 2025-09-15

- Added support for updating the email associated with billing data
- Added support for iterating on logs in playgrounds
- Added support for scoring existing logs

### SDK Integrations: Google ADK (version v0.2.0)

- Enhanced automatic tracing for runners, agents, and flows - captures complete input/output data and metadata at every step without configuring OpenTelemetry

## Week of 2025-09-08

- Trace tree is now visible in human review mode
- BTQL sandbox improvements
  - Loop is now on the page and can write queries, debug errors and answer syntax questions
  - Tabs
  - Simple charts
  - Improved auto-complete
- Updated UI color palette
- Custom charts added to the monitor page (requires data plane 1.1.22)
- View state changes for non-saved views
  - Before: We would attempt to restore any previous edited view state to the URL
  - After: With a few exceptions, edited view state for non-saved views is only represented in the URL

### SDK Integrations: LangChain (JS) (version 0.0.7)

* Added `parent` parameter to organize LangChain traces within evaluation hierarchies, enabling better debugging and trace analysis when using LangChain in your evaluations

### SDK Integrations: LangChain (JS) (version 0.0.7)

* Fixed dependency issue that prevented the integration from using the latest braintrust SDK

### Python SDK version 0.2.7

* Fixed an OpenAI Agents concurrency bug that incorrectly handled root propagation of input/output
* Fixed parent span precedence issues for better trace hierarchy
* Support locking down remote evals via `--dev-org-name` to only accept users from your org
* Added `update-stack-url` CLI option to explicitly change the URL of the data plane

### TypeScript SDK version 0.3.8

- Prevents logging Braintrust API keys when logging Span objects
- Improved error messages when we fail to find evaluators or code definitions

## Data plane (1.1.22)

- Added ability to create and edit custom charts in the monitor dashboard
- Added support for more Grok models and improved model refresh handling in `/invoke` endpoint
- Added support for `IN` clause in BTQL queries
- Improved processing of pydantic-ai OpenTelemetry spans with tool names in span names and proper input/output field mapping
- Added OpenAI Agents logs formatter for better span rendering in the UI
- Added retention support for Postgres WAL and object WAL (write-ahead logs)
- Add S3 lifecycle policies to reclaim additional space from bucket
- Added authentication support for remote evaluation endpoints
- Improved ability to fetch all datasets efficiently
- New `MAX_LIMIT_FOR_QUERIES` parameter to set the maximum allowable limit for BTQL queries. Larger result sets can still be queried through pagination

### Autoevals PY (version 0.0.130)

- Fold the `braintrust_core` external package into the `autoevals` package,
since it is the only user of `braintrust_core`. Future braintrust packages will
not depend on the `braintrust_core` py package

## Week of 2025-09-01

- Loop can search through Braintrust's docs and blog posts to help you answer questions about how to use Braintrust, including generating sample code

## Week of 2025-08-25

- Traces in the trace viewer on the logs page can now show all associated traces based on a metadata field or tag

### TypeScript SDK version 0.3.7

* Support locking down remote evals via `--dev-org-name` to only accept users from your org
* Fixed parent span precedence issues for better trace hierarchy
* Improved propagation of parentSpanId into parentSpanContext for OpenTelemetry JS v2 compatibility
* Fold the `@braintrust/core` package into `braintrust`. This package consists
of a small set of utility functions that is more easily-managed as part of the
main `braintrust` package. After version `0.3.7`, you should no longer need a
dependency on `@braintrust/core`

### Python SDK version 0.2.6

- Python SDK now correctly nests spans logged from inside tool calls in OpenAI Agents

### TypeScript SDK version 0.3.6

- OpenAI responses wrapper no longer filters out span data fields when logging
- Fixed `withResponse` and `wrapOpenAI` interaction to not hide response data

## Data plane (1.1.21)

- Process pydantic-ai OTel spans
- AI proxy now supports temperature > 1 for models which allow it
- Preview of data retention on logs, datasets, and experiments

## Week of 2025-08-18

- Monitor page layout changed to be more responsive to screen size
- Various UX improvements to prompt dialog
- Improved onboarding experience
- Trace timeline layout improvements

## Data plane (1.1.20)

- Brainstore vacuum is enabled by default. This will reclaim space from object storage. As a bonus, vacuum also cleans up more data (segment-level write-ahead logs)
- AI proxy now dynamically fetches updates to the model registry
- Performance improvements to summary, `IS NOT NULL`, and `!= NULL` queries
- Handle cancelled BTQL queries earlier and optimize schema inference queries
- Added a REST API for managing service tokens. See [docs](/docs/reference/api/ServiceTokens)
- Support custom columns on the experiments page
- Aggregate custom metrics and include more built-in agent metrics in experiments and logs
- Preview of data retention on logs. You can define a per-project policy on logs which will be deleted on a schedule and no longer available in the UI and API

## Week of 2025-08-18

### Python SDK version 0.2.5

- Support data masking (see [docs](/docs/guides/traces/customize#masking-sensitive-data))
- Remote evals in Python SDK
- Support tags in Eval hooks
- Validate attachment file readability at creation time

### TypeScript SDK version 0.2.5

- Support data masking (see [docs](/docs/guides/traces/customize#masking-sensitive-data))
- Support tags in Eval hooks
- Validate attachment file readability at creation time

### SDK Integrations: Google ADK (Python) (version 0.1.1)

- Added integration with [Google Agent Development Kit (ADK)](/docs/guides/integrations#google-adk-agent-development-kit)

### Python SDK version 0.2.4

- Allow non-batch span processors in `BraintrustSpanProcessor`


## Week of 2025-08-11

- Pro plan organizations can now downgrade to the Free plan via the settings page without contacting support
- Prevent read-only users from downloading data from the UI

### Python SDK version 0.2.3

- Fix openai-agents to inherit the right tracing context

### TypeScript SDK version 0.2.4

- Support OpenAI Agents SDK

### SDK Integrations: OpenAI Agents (TS) (version 0.0.2)

- Fix openai-agents to inherit the right tracing context

## Data plane (1.1.19)

- Add support for GPT-5 models
- OTel tracing support for Google Agent Development Kit
- OTel support for deleting fields
- Fix binder error handling for malformed BTQL queries
- Enable environment tags on prompt versions

## Week of 2025-08-04

- @mention team members in comments to notify them via email. To mention someone, type "@" and a team member's name or email in any comment input
- You can now assign users to rows in experiments, logs, and datasets. Once assigned, you can filter rows by a specific user or a group of users
- View configuration has been changed to no longer auto-save changes. It now shows a dirty state and you have the option of saving or resetting those changes back to the base view

## Python SDK version 0.2.2

- Added `environment` parameter to `load_prompt`
- The Otel SpanProcessor now keeps `traceloop.*` spans by default
- Experiments can now be run without sending results to the server
- Span creation is significantly faster in Python

## TypeScript SDK version 0.2.3

- Added `environment` parameter to `load_prompt`
- The Otel SpanProcessor now keeps `traceloop.*` spans by default
- Experiments can now be run without sending results to the server
- Fix `npx braintrust pull` for large prompts


## TypeScript SDK version 0.2.2

- Fix ai-sdk tool call formatting in output
- Log OpenAI Agents input and output to root span
- Wrap OpenAI responses.parse
- Add wrapTraced support for generator functions

## Python SDK version 0.2.1

- Fix langchain-py integration tracing when users use a @traced method
- Wrap OpenAI responses.parse
- Add @traced support for generator functions

## Week of 2025-07-28

- New improved UI for trace tree
- Token and cost metrics are computed per sub-tree in the trace viewer
- Download BTQL sandbox results as JSON or CSV

## Data plane (1.1.18)

This is our largest data plane release in a while, and it includes several
significant performance improvements, bug fixes, and new features:

- Improve performance for non-selective searches. Eg make `foo != 'bar'` faster
- Improve performance for score filters. Eg make `scores.correctness = 0` faster
- Improve group by performance. This should make the monitor page and project summary page significantly faster
- Add syntax for explicit casting. You can now use explicit casting functions to cast data to any datatype. e.g. `to_number(input.foo)`, `to_datetime(input.foo)`, etc
- Fix ILIKE queries on nested json: ILIKE queries previously returned incorrect results on nested json objects. ILIKE now works as expected for all json objects
- Improve backfill performance. New objects should get picked up faster
- Improve compaction latency. Indexing should kick in much faster, and in particular, this means data gets indexed a lot faster
- Improved support for OTel mappings, including the new [GenAI
Agent](https://opentelemetry.io/docs/specs/semconv/gen-ai/gen-ai-agent-spans/)
conventions and [strands
framework](https://aws.amazon.com/blogs/opensource/introducing-strands-agents-an-open-source-ai-agents-sdk/)
- Add Gemini 2.5 Flash-Lite GA, GPT-OSS models on several providers, and Claude Opus 4.1

## Week of 2025-07-21

- Moved monitor chart legends to the bottom and increased chart heights
- Fixed a monitor chart issue where the series toggle selector would filter the incorrect series
- Improved monitor fullscreen experience: charts now open faster and retain their series filter state
- Loop is now available in the experiments page and has a new ability to render interactive components inside the chat that will help you find the UI element that Loop is referencing
- You can now use remote evals with the "+Experiment" button to create a new experiment. Previously, they were only available in the playground

## TypeScript SDK version 0.2.1

- Fix support for the `openai.chat.completions.parse` method when used with `wrapOpenAI`
- Added support for ai-sdk@beta with new `BraintrustMiddleware`
- Support running remote evals as full experiments

## TypeScript SDK version 0.2.0

- When running multiple trials per input (`trial_count > 1`), you can now access the current trial index (0-based) via `hooks.trialIndex` in your task function
- Added `BraintrustExporter` in addition to `BraintrustSpanProcessor`
- Bound max ancestors in git to 1,000

## Python SDK version 0.2.0

- When running multiple trials per input (`trial_count > 1`), you can now access the current trial index (0-based) via `hooks.trial_index` in your task function
- New LiteLLM `wrap_litellm` wrapper
- Increase max ancestors in git to 1,000

## Data plane (1.1.15)

- Add ability to run scorers as tasks in the playground
- You can now use object storage, instead of Redis, as a locks manager
- Support async python in inline code functions
- Don't re-trigger online scoring on existing traces if only metadata fields like `tags` change

## Week of 2025-07-14

- Add monitor page UTC timezone toggle
- Improved trace view loading performance for large traces

## Python SDK version 0.1.8

- Added `BraintrustSpanProcessor` to simplify Braintrust's integration with OpenTelemetry

## TypeScript SDK version 0.1.1

- Added `BraintrustSpanProcessor` to simplify integration with OpenTelemetry

## Data plane (1.1.14)

- Switch the default query shape from `traces` to `spans` in the API. This means that btql queries will now return 1
row per span, rather than per trace. This change also applies to the REST API
- Service tokens with scoped, user-independent credentials for system integrations
- Fix a bug where very large experiments (run through the API) would drop spans if they could not flush data fast enough
- Support built-in OTel metrics (contact your account team for more details)
- New parallel backfiller improves performance of loading data into Brainstore across many projects

## Python SDK version 0.1.7

- Added support for loading prompts by ID via the `load_prompt` function. You can now load prompts directly by their unique identifier:

```python
prompt = braintrust.load_prompt(id="prompt_id_123")
```

## TypeScript SDK version 0.1.0

- Fix a bug where large experiments would drop spans if they could not flush data fast enough
- Fix bug in attachment uploading in evals executed with `npx braintrust eval`
- Upgrading zod dependency from `^3.22.4` to `^3.25.3`
- Added support for loading prompts by ID via the `loadPrompt` function. You can now load prompts directly by their unique identifier:

```typescript #skip-compile
const prompt = await loadPrompt({ id: "prompt_id_123" });
```


## Week of 2025-07-07

- Loop can now create custom code scorers in playgrounds
- Schema builder UI for structured outputs
- Sort datasets when the `Faster tables` feature flag is enabled
- Change LLM duration to be the sum, not average, of LLM duration across spans
- Add support for Grok 4 and Mistral's Devstral Small Latest

## Data plane (1.1.13)

- Fix support for `COALESCE` with variadic arguments
- Add option to select logs for online scoring with a BTQL filter
- Add ability to test online scoring configuration on existing logs
- Mmap based indexing optimization enabled by default for Brainstore

## Data plane (1.1.12) [skipped]


## Week of 2025-06-30

- Time range filters on the logs page

## Data plane (1.1.11)

- Add support for LLaMa 4 Scout for Cerebras
- Turn on index validation (which enables self-healing failed compactions) in the Cloudformation by default

## Week of 2025-06-23

- Add support for multi-factor authentication
- Fix a bug with Vertex AI calls when the request includes the anthropic-beta header
- Add Zapier integration to trigger Zaps when there's a new automation event or a new project

## Data plane (1.1.7)

- Improve performance of error count queries in Brainstore
- Automatically heal segments that fail to compact
- Add support for new models including o3 pro
- Improve error messages for LLM-originated errors in the proxy

## Autoevals.js v0.0.130

- Remove dependency on `@braintrust/core`

## TypeScript SDK version 0.0.209

- Ensure SpanComponentsV3 encoding works in the browser

## TypeScript SDK version 0.0.208

- Ensure running remote evals (i.e. `runDevServer`) works without the CLI wrapper
- Add span + parent ids to `StartSpanArgs`

## Week of 2025-06-16

- Add OpenAI's [o3-pro](https://platform.openai.com/docs/models/o3-pro) model to the playground and AI proxy
- View parameters are now present in the url when viewing a default view
- Experiments charting controls have been added into views
- Experiment objects now support tags through the API and on the experiments view
- Add support for Gemini 2.5 Pro, Gemini 2.5 Flash, and Gemini 2.5 Flash Lite

### Python SDK version 0.1.5

- The SDK's under-the-hood log queue will not block when full
  and has a default size of 25000 logs. You can configure the max size by setting
  `BRAINTRUST_LOG_QUEUE_MAX_SIZE` in your environment. The environment variable
  `BRAINTRUST_QUEUE_DROP_WHEN_FULL` is no longer used
- Improvements to the logging of parallel tool calls
- Attachments are now converted to base64 data URLs, making it easier to work with image attachments in prompts

### TypeScript SDK version 0.0.207

- The SDK's under-the-hood queue for sending logs now has a default size of 5000 logs
  You can configure the max size by setting `BRAINTRUST_LOG_QUEUE_MAX_SIZE` in your environment
- Improvements to the logging of parallel tool calls
- Attachments are now converted to base64 data URLs, making it easier to work with image attachments in prompts

## Data plane (1.1.6)

- Patch a bug in 1.1.5 related to the `realtime_state` field in the API response

## Data plane (1.1.5)

- Default query timeout in Brainstore is now 32 seconds
- Auto-recompact segments which have been rendered unusable due to an S3-related issue
- Gemini 2.5 models

## Data plane (1.1.4)

- Optimize "Activity" (audit log) queries, which reduces the query workload on Postgres for
  large traces (even if you are using Brainstore)
- Automatically convert base64 payloads to attachments in the data plane. This
  reduces the amount of data that needs to be stored in the data plane and
  improves page load times. You can disable this by setting `DISABLE_ATTACHMENT_OPTIMIZATION=true`
  or `DisableAttachmentOptimization=true` in your stack
- Improve AI proxy errors for status codes 401->409
- Increase real-time query memory limit to 10GB in Brainstore

## Week of 2025-06-09

- Correctly propagate `expected` and `metadata` values to function calls when running `invoke`. This means
  that if you provide `expected` or `metadata`, `input` refers to the top-level input argument. If you are
  passing in a value like `{input: "a"}`, then you must now use `{{input.input}}` to refer to the string "a",
  if you pass in `expected` or `metadata`. This should have no effect on the playground or scorers
- Chat-like thread layout that simplifies thread display to LLM and score data
- Enable all agent nodes to access dataset variables with the mustache variable `{{dataset}}`. For example, to
access `metadata.foo` in the third prompt in an agent, you can use `{{dataset.metadata.foo}}`
- Improve reliability of online scoring when logging high volumes of data to a project
- Tags can now be sorted in the project configuration page which will change their display order in other parts of the UI
- System-only messages are now supported in Anthropic and Bedrock models
- Logs page UI can now filter nested data fields in `metadata`, `input`, `output`, and `expected`

### Python SDK version 0.1.4

- Add `project.publish()` to directly `push` prompts to Braintrust (without running `braintrust push`)
- `@traced` now works correctly with async generator functions
- The OpenAI and Anthropic wrappers set `provider` metadata


### TypeScript SDK version 0.0.206

- Add support for `project.publish()` to directly `push` prompts to Braintrust (without running `braintrust push`)
- The OpenAI and Anthropic wrappers set `provider` metadata


## Week of 2025-06-02

- Support reasoning params and reasoning tokens in streaming and non-streaming responses in the [AI proxy](/docs/guides/proxy) and across the product (requires a stack update to 0.0.74)
- New [braintrust-proxy](https://pypi.org/project/braintrust-proxy/) Python library to help developers integrate with their IDEs to support new reasoning input and output types
- New `@braintrust/proxy/types` module to augment OpenAI libraries with reasoning input and output types
- New streaming protocol between Brainstore and the API server speeds up queries
- Time brushing interaction enabled on Monitor page charts
- Can create user-defined views in the monitoring page
- Live updating time mode added to the monitoring page
- The `anthropic` package is now included by default in Python functions
- Audit log queries must now specify an `id` filter for the set of rows to
fetch. These queries will only return the audit log for the specified rows,
rather than the whole trace
- (Beta) continuously export logs, experiments, and datasets to S3
- Enable passing `metadata` and `expected` as arguments to the first agent prompt node

### Python SDK version 0.1.3

- Improve retry logic in the control plane connection (used to create new experiments and datasets)

## Week of 2025-05-26

- The "Faster tables" flag is now the default (you may need to update your data plane if you are self-hosted). You should
  notice experiments, datasets, and the logs page load much faster
- Add Claude 4 models in Bedrock and Vertex to the AI proxy and playground
- Braintrust now incorporates cached tokens into the cost calculations for experiments and logs. The monitor page also now
  includes separate lines so you can track costs and counts for uncached, cached, and cache creation tokens
- Native support for thinking parameters in the playground

<video src="https://duwdm5zff5xmbmwp.public.blob.vercel-storage.com/thinking-LcpOE6omIbsNW9wBFgASCDJbnAbs3i.mp4" autoPlay muted loop />

### Python SDK version 0.1.2

- Added support for `metadata` and `tags` arguments to `invoke`
- The SDK now gracefully handles OpenAI's `NotGiven` parameter
- Added `span.link()` to synchronously generate permalinks

### TypeScript SDK version 0.0.206 [upcoming]

- Add support for `metadata` and `tags` arguments to `invoke`


## Week of 2025-05-19

- Improved playground prompt editor stability and performance
- Capture cached tokens from OpenAI and Anthropic models in a unified format and surface them in the UI
- Create experiments from the experiments list page using saved prompts/agents
- New BTQL sandbox page and editor with autocomplete
![BTQL sandbox](./reference/release-notes/btql-sandbox.gif)
- Fullscreen-able monitor charts
- Added a 'Copy page' button to the top of every docs page
- Brainstore now supports vacuuming data from object storage to reclaim space. If you are self-hosted, please
  reach out to Braintrust support to learn more about the feature
- Organization owners can manage API keys for all users in their organization in the UI
- Add endpoint for admins to list all ACLs within an org

### TypeScript SDK version 0.0.205

- Make the `_xact_id` field in `origin` optional
- Added `span.link()` as a synchronous means of generating permalinks

### Python SDK version 0.1.1

- Update cached token accounting in `wrap_anthropic` to correctly capture cached tokens
- Pull additional metadata in `braintrust pull` for prompts and functions to improve tracing

### TypeScript SDK version 0.0.204

- Update cached token accounting in `wrapAnthropic` to correctly capture cached tokens

## Week of 2025-05-12

- Collapsible sidebar navigation
- Command bar (CMD/CTRL+K) to quickly navigate and between pages and projects
- View monitor page logs across all projects in an organization

### SDK (version 0.1.0)

- Allow custom model descriptions in Braintrust
- Improve support for PDF attachments to multimodal OpenAI models

The Python library no longer has a dependency on `braintrust_core`
`braintrust_core` will be deprecated in the near future, but the package will
remain on PyPI. If you wrote code that directly imports from `braintrust_core`, you can
either:

- Change your imports to `from braintrust.score import Score, Scorer` (preferred)
- or, add `braintrust_core` to your project's dependencies

The TypeScript SDK also includes a small packaging bugfix

### SDK (version 0.0.203)

- Add new reasoning to OpenAI messages

## Week of 2025-05-05

- Added Mistral Medium 3 and Gemini 2.5 Pro Preview to the AI proxy and playground
- Self-hosted builds now log in a structured JSON format that is easier to parse

### SDK (version 0.0.202)

- Gracefully handle experiment summarization failures in Eval()
- Fix a bug where `wrap_openai` was breaking `pydantic_ai run_stream` func
- Add tracing to the `client.beta.messages` calls in the TypeScript Anthropic
library
- Fix some deprecation warnings in the Python SDK


## Week of 2025-04-28

- Permission groups settings page now allows admins to set group-level permissions
(i.e. which users can read, delete, and add/remove members from a particular group)
- Automations alpha: trigger webhooks based on log events


## Week of 2025-04-21

- Preview attachments in playground input cells
- Playground now support list mode which includes score and metric summaries
- Handle structured outputs from OpenAI's responses API in the "Try prompt" experience

### SDK (version 0.0.201)

- Support OpenAI `client.beta.chat.completions.parse` in the Python wrapper


### SDK (version 0.0.200)

- Ensure the prompt cache properly handles any manner of prompt names
- Ensure the output of `anthropic.messages.create` is properly traced when called with `stream=True` in an async program

## Week of 2025-04-14

- Allow users to remove themselves from any organization they are part of using
the `/v1/organization/members` REST endpoint
- Group monitor page charts by metadata path
- Download playground contents as CSV
- Add pending and streaming state indicators to playground cells
- Distinguish per-row and global playground progress
- Added GPT-4.1, o4-mini and o3 to the AI proxy and playground
- On the monitor page, add aggregate values to chart legends
- Add Gemini 2.5 Flash Preview model to the AI proxy and playground
- Add support for audio and video inputs for Gemini models in the AI proxy and playground
- Add support for PDF files for OpenAI models
- Native tracing support in the proxy has finally arrived! Read more in [the docs](/docs/guides/proxy#tracing)
- Upload attachments directly in the UI in datasets, playgrounds, and prompts (requires a stack update to 0.0.67)

### SDK (version 0.0.199)

- Fix a bug that broke async calls to the Python version of
`anthropic.messages.create`
- Store detailed metrics from OpenAI's `chat.completion` TypeScript API


### SDK (version 0.0.198)

- Trace the `openai.responses` endpoint in the Typescript SDK
- Store the `token_details` metrics return by the `openai/responses` API

## Week of 2025-04-07

- Playground option to append messages from a dataset to the end of a prompt
- A new toggle that lets you skip tracing scoring info for online scoring. This is useful when you are scoring
  old logs and don't want to hurt search performance as a result
- GIF and image support in comments
- Add embedded view and download action for inline attachments of supported file types

### API (version 0.0.65)

- Improve error messages when trying to insert invalid unicode

### SDK (version 0.0.197)

- Fix a bug in `init_function` in the Python SDK which prevented the `input` argument from being passed to the function correctly when it was used as a scorer
- Support setting `description` and `summarizeScores`/`summarize_scores` in `Eval(...)`

### API (version 0.0.65)

- Backend support for appending messages

## Week of 2025-03-31

- Many improvements to the playground experience:
  - Fixed many crashes and infinite loading spinner states
  - Improved performance across large datasets
  - Better support for running single rows for the first time
  - Fixed re-ordering prompts
  - Fixed adding and removing dataset rows
  - You can now re-run specific prompts for individual cells and columns
- You can now do "does not contain" filters for tags in experiments and datasets. Coming soon to logs!
- When you `invoke()` a function, inline base64 payloads will be automatically logged as attachments
- Add a strict mode to evals and functions which allows you to fail test cases when a variable is not present in a prompt. Without strict mode,
  prompts will always render (and sometimes miss variables). With strict mode on, these variables show clearly as errors in the playground and experiments
- Add Fireworks' DeepSeek V3 03-24 and DeepSeek R1 (Basic), along with Qwen QwQ 32B in Fireworks and Together.ai, to the playground and AI proxy
- Fix bug that prevented Databricks custom provider form from being submitted without toggling authentication types
- Unify Vertex AI, Azure, and Databricks custom provider authentication inputs
- Add Llama 4 Maverick and Llama 4 Scout models to Together.ai, Fireworks, and Groq providers in the playground and AI proxy
- Add Mistral Saba and Qwen QwQ 32B models to the Groq provider in the playground and AI proxy
- Add Gemini 2.5 Pro Experimental and Gemini 2.0 Flash Thinking Mode models to the Vertex provider in the playground and AI proxy

### API (version 0.0.64)

- Brainstore is now set as the default storage option
- Improved backfilling performance and overall database load
- Enabled relaxed search mode for ClickHouse to improve query flexibility
- Added strict mode option to prompts that fails when required template arguments are missing
- Enhanced error reporting for missing functions and eval failures
- Fixed streaming errors that previously resulted in missing cells instead of visible error states
- Abort evaluations on server when stopped from playground
- Added support for external bucket attachments
- Improved handling of large base64 images by converting them to attachments
- Fixed proper handling of UTF-8 characters in attachment filenames
- Added the ability to set telemetry URL through admin settings


### SDK (version 0.0.196)

- Adding Anthropic tracing for our TypeScript SDK. See `braintrust.wrapAnthropic`
- The SDK now paginates datasets and experiments, which should improve performance for large datasets and experiments
- Add `strict` flag to `invoke` which implements the strict mode described above
- Raise if a Python tool is pushed without without defined parameters,
  instead of silently not showing the tool in the UI
- Fix Python OpenAI wrapper to work for older versions of the OpenAI library without `responses`
- Set time_to_first_token correctly from AI SDK wrapper

## Week of 2025-03-24

- Add OpenAI's [o1-pro](https://platform.openai.com/docs/models/o1-pro) model to the playground and AI proxy
- Support OpenAI Responses API in the AI proxy
- Add support for the Gemini 2.5 Pro Experimental model in the playground and AI proxy
- Option to disable the experiment comparison auto-select behavior
- Add support for Databricks custom provider as a default cloud provider in the playground and AI proxy
- Allow supplying a base API URL for Mistral custom providers in the playground and AI proxy
- Support pushed code bundles larger than 50MB

### SDK (version 0.0.195)

 - Improve the metadata collected by the Anthropic client
 - Anthropic client can now be run with `braintrust.wrap_anthropic`
 - Fix a bug when `messages.create` was called with `stream=True`

### SDK (version 0.0.194)

- Add Anthropic tracing to the Python SDK with `wrap_anthropic_client`
- Fix a bug calling `braintrust.permalink` with `NoopSpan`

### SDK (version 0.0.193)

- Fix retry bug when downloading large datasets/experiments from the SDK
- Background logger will load environment variables upon first use rather than
when module is imported

## Week of 2025-03-17

- The OTEL endpoint now understands structured output calls from the Vercel AI
  SDK. Logging via `generateObject` and `streamObject` will populate the schema
  in Braintrust, allowing the full prompt to be run
- Added support for `concat`, `lower`, and `upper` string functions in BTQL
- Correctly propagate Bedrock streaming errors through the AI proxy and playground
- Online scoring supports sampling rates with decimal precision

### SDK (version 0.0.192)

- Improve default retry handler in the python SDK to cover more network-related
exceptions

### Autoevals (version 0.0.124)

- Added `init` to set a global default client for all evaluators (Python and Node.js)
- Added `client` argument to all evaluators to specify the client to use
- Improved the Autoevals docs with more examples, and Python reference docs now include moderation, ragas, and other evaluators that were missing from the initial release

## Week of 2025-03-10

- Added support for OpenAI GPT-4o Search Preview and GPT-4o mini Search Preview
  in the playground and AI proxy
- Add support for making Anthropic and Google-format requests to corresponding models in the AI proxy
- Fix bug in model provider key modal that prevents submitting a Vertex provider with an empty base URL
- Add column menu in grid layout with sort and visibility options
- Enable logging the `origin` field through the REST API

### Autoevals (version 0.0.123)

- Swapped `polyleven` for `levenshtein` for faster string matching

### SDK Integrations: LangChain (Python) (version 0.0.2)

- Add a new `braintrust-langchain` integration with an improved `BraintrustCallbackHandler` and `set_global_handler` to set the handler globally for all LangChain components

### SDK Integrations: LangChain.js (version 0.0.6)

- Small improvement to avoid logging unhelpful LangGraph spans
- Updated peer dependencies with LangChain core that fixes the global handler for LangGraph runs

### SDK Integrations: Val Town

- New `val.town` integration with example vals to quickly get started with Braintrust

### SDK (version 0.0.190)

- Fix `prompt pull` for long prompts
- Fix a bug in the Python SDK which would not retry requests that were severed after a connection timeout

### SDK (version 0.0.189)

- Added integration with [OpenAI Agents SDK](/docs/guides/traces/integrations#openai-agents-sdk)

### SDK (version 0.0.188)

- Deprecated `braintrust.wrapper.langchain` in favor of the new `braintrust-langchain` package


## Week of 2025-03-03

- Add support for "image" pdfs in the AI proxy
  See the [proxy docs](/docs/guides/proxy#pdf-input) for more details
- Fix issue in which code function executions could hang indefinitely
- Add support for custom base URLs for Vertex AI providers
- Add dataset column to experiments table
- Add python3.13 support to user-defined functions
- Fix bug that prevented calling Python functions from the new unified playground

### SDK (version 0.0.187)

- Always bundle default python packages when pushing code with `braintrust push`
- Fix bug in the TypeScript SDK where `asyncFlush` was not correctly defaulted to false
- Fix a bug where `span_attributes` failed to propagate to child spans through propagated events

## Week of 2025-02-24

- Add support for removing all permissions for a group/user on an object with a single click
- Add support for Claude 3.7 Sonnet model
- Add [llms.txt](/docs/llms.txt) for docs content
- Enable spellcheck for prompt message editors
- Add support for Anthropic Claude models in Vertex AI
- Add support for Claude 3.7 Sonnet in Bedrock and Vertex AI
- Add support for Perplexity R1 1776, Mistral Saba, Gemini LearnLM, and more Groq models
- Support system instructions in Gemini models
- Add support for Gemini 2.0 Flash-Lite, and remove preview model,
  which no longer serves requests
- Add support for default Bedrock cross-region inference profiles in the playground and AI proxy
- Move score distribution charts to the experiment sidebar
- Add support for OpenAI GPT-4.5 model in the playground and AI proxy
- Add deprecation warning for `_parent_id` field in the REST API
([docs](/docs/reference/api/Logs#request-body)). This field will be removed in a
future release

### API (version 0.0.63)

- Support for Claude 3.7 Sonnet, Gemini 2.0 Flash-Lite, and several other models in the proxy
- Stability and performance improvements for ETL processes
- A new `/status` endpoint to check the health of Braintrust services

### SDK (version 0.0.187)

- Added support for handling score values when an Eval has errored

## Week of 2025-02-17

- Add support for stop sequences in Anthropic, Bedrock, and Google models
- Resolve JSON Schema references when translating structured outputs
  to Gemini format
- Add button to copy table cell contents to clipboard
- Add support for basic Cache-Control headers in the AI proxy
- Add support for selecting all or none in the categories of permission dialogs
- Respect Bedrock providers not supporting streaming in the AI proxy


### SDK (version 0.0.187)

- Improve support for binary packages in `npx braintrust eval`
- Support templated structured outputs
- Fix dataset summary types in Typescript

## Week of 2025-02-10

- Store table grouping, row height, and layout options in the view configuration
- Add the ability to set a default table view
- Add support for Google Cloud Vertex AI in the playground and proxy
  Google Cloud auth is supported for principals and service accounts
  via either OAuth 2.0 token or service account key
- Add default cloud providers section to the organization AI providers page
- Support streaming responses from OpenAI o1 models in the playground and AI proxy

## Week of 2025-02-03

- Add complete support for Bedrock models in the playground and AI proxy;
  this includes support for system prompts, tool calls, and multimodal inputs
- Fix model provider configuration issues in which custom models could clobber
  default models, and different providers of the same type could clobber each other
- Fix bug in streaming JSON responses from non-OpenAI providers
- Supported templated structured outputs in experiments run from the playground
- Support structured outputs in the playground and AI proxy for Anthropic models, Bedrock models,
  and any OpenAI-flavored models that support tool calls, e.g. LLaMa on Together.ai
- Support templated custom headers for custom AI providers
  See the [proxy docs](/docs/guides/proxy#custom-models) for more details
- Added and updated models across all providers in the playground and AI proxy
- Support tool usage and structured outputs for Gemini models in the playground and AI proxy
- Simplify playground model dropdown by showing model variations in a nested dropdown

## Week of 2025-01-27

- Add support for duplicating prompts, scorers, and tools
- Fix pagination for the `/v1/prompt` REST API endpoint
- "Unreviewed" default view on experiment and logs tables to filter out rows that have been human reviewed
- Add o3-mini to the AI proxy and playground
- Scorer dropdown now supports using custom scoring functions across projects

### SDK Integrations: LangChain.js (version 0.0.5)

- Less noisy logging from the LangChain.js integration
- You can now pass a `NOOP_SPAN` to the `BraintrustCallbackHandler` to disable logging
- Fixes a bug where the LangChain.js integration could not handle null/undefined values in chain inputs/outputs

### SDK (version 0.0.184)

- `span.export()` will no longer throw if braintrust is down
- Improvement to the Python prompt rendering to correctly render formatted messages, LLM tool calls, and other structured outputs


## Week of 2025-01-20

- Drag and drop to reorder span fields in experiment/log traces and dataset rows. On wider screens,
  fields can also be arranged side-by-side
- Small convenience improvement to the BTQL Sandbox to avoid having to add include `filter:` to an advanced filter clause
- Add an attachments browser to view all attachments for a span in a sidebar. To open the attachments browser, expand the
  trace and click the arrow icon in the attachments section. It will only be visible when the trace panel is wide enough
![Attachments browser](./reference/release-notes/open-attachments-browser.png)

### SDK (version 0.0.183)

- Fix a bug related to `initDataset()` in the Typescript SDK creating links in `Eval()` calls
- Fix a few type checking issues in the Python SDK

## Week of 2025-01-13

- Add support for setting a baseline experiment for experiment comparisons. If a baseline experiment is set, it will be chosen by default as the comparison when clicking on an experiment
- UI updates to experiment and log tables
  - Trace audit log now displays granular changes to span data
  - Start/end columns shown as dates/times
  - Non-existent trace records display an error message instead of loading indefinitely

### SDK Integrations: LangChain.js (version 0.0.4)

- Support logging spans from inside evals in the LangChain.js integration

### SDK (version 0.0.182)

- Improved logging for moderation models from the SDK wrappers

## Week of 2025-01-06

- Creating an experiment from a playground now correctly renders prompts with `input`, `metadata`, `expected`, and `output` mapped fields
- Fixes small bug where `input.output` data could pollute the dataset's `output` when rendering the prompts
- The [AI proxy](/docs/guides/proxy) now includes `x-bt-used-endpoint` as a response header. It specifies which of your configured AI providers was used to complete the request
- Add support for deeplinking to comments within spans, allowing users to easily copy and share links to comments
- In Human Review mode, display all scores in a form
- Experiment table rows can now be sorted based on score changes and regressions for each group, relative to a selected comparison experiment
- The OTEL endpoint now converts attributes under the `braintrust` namespace directly to the corresponding Braintrust fields. For example, `braintrust.input` will appear as `input` in Braintrust. See the [tracing guide](/docs/guides/tracing/integrations#manual-tracing) for more details
- New OTEL attributes that accept JSON-serialized values have been added for convenience:
  - `gen_ai.prompt_json`
  - `gen_ai.completion_json`
  - `braintrust.input_json`
  - `braintrust.output_json`
  For more details, see the [tracing guide](/docs/guides/tracing/integrations#manual-tracing)
- Experiment tables and individual traces now support comparing trial data between experiments

### SDK (version 0.0.181)

- Add `ReadonlyAttachment.metadata` helper method to fetch a signed URL for
downloading the attachment metadata

### SDK (version 0.0.179)

- New `hook.expected` for reading and updating expected values in the Eval framework
- Small type improvements for `hook` objects
- Fixed a bug to enable support for `init_function` with LLM scorers in Python
- Support nested attachments in Python

## Week of 2024-12-30

- Add support for free-form human review scores (written to the `metadata` field)

### SDK (version 0.0.179) (unreleased)
- Add support for imports in Python functions pushed to Braintrust via `braintrust push`

### SDK (version 0.0.178)
- Cache prompts locally in a two-layered memory/disk cache,
  and attempt to use this cache if the prompt cannot be fetched from the Braintrust server
- Support for using custom functions that are stored in Braintrust in evals
  See the [docs](/docs/guides/evals/write#using-custom-promptsfunctions-from-braintrust) for more details
- Add support for running traced functions in a `ThreadPoolExecutor`
  in the Python SDK. See the [customize traces guide](/docs/guides/traces/customize)
  for more information
- Improved formatting of spans logged from the Vercel AI SDK's `generateObject` method
  The logged output now matches the format of OpenAI's structured outputs
- Default to `asyncFlush: true` in the TypeScript SDK
  This is usually safe since Vercel and Cloudflare both have `waitUntil`,
  and async flushes mean that clients will not be blocked if Braintrust is down

### SDK integrations: LangChain.js (version 0.0.2)
- Add support for initializing global LangChain callback handler to avoid manually passing the handler to each LangChain object

## Week of 2024-12-16

### API (version 0.0.61)
- Upgraded to Node.js 22 in Docker containers

### SDK (version 0.0.177)
- Support for creating and pushing custom scorers from your codebase
  with `braintrust push`
  Read the guides to [scorers](/docs/guides/functions/scorers)
  for more information

## Week of 2024-12-09

- Add support for structured outputs in the playground
![Structured outputs](./reference/release-notes/structured-outputs.gif)
- Sparkline charts added to the project home page
- Better handling of missing data points in monitor charts
- Clicking on monitor charts now opens a link to traces filtered to the selected time range
- Add `Endpoint supports streaming` flag to custom provider configuration. The [AI proxy](/docs/guides/proxy) will convert non-streaming endpoints to streaming format, allowing the provider's models to be used in the playground
- Experiments chart can be resized vertically by dragging the bottom of the chart
- BTQL sandbox to explore project data using [Braintrust Query Language](/docs/reference/btql)
- Add support for updating span data from custom span iframes

### Autoevals (version 0.0.110)

- Python Autoevals now support custom clients when calling evaluators. See [docs](https://pypi.org/project/autoevals/) for more details


### SDK (version 0.0.176)

- New `hook.metadata` for reading and updating Eval metadata when using the `Eval` framework. Previous `hook.meta` is now deprecated

### SDK integrations: LangChain.js (version 0.0.1)

- New LangChain.js integration to export traces from `langchainjs` runs

### SDK integrations: LangChain.js (version 0.0.1)

- New LangChain.js integration to export traces from `langchainjs` runs

## Week of 2024-12-02

- Significantly speed up loading performance for experiments and logs, especially with lots of spans
  This speed up comes with a few changes in behavior:
  - Searches inside experiments will only work over content in the tabular view, rather than over the full trace
  - While searching on the logs page, realtime updates are disabled
- Starring rows in experiment and dataset tables now supported
- "Order by regression" option in experiment column menu can now be toggled on and off without losing previous order
- Add expanded timeline view for traces
- Added a 'Request count' chart to the monitor page
- Add headers to custom provider configuration which the [AI proxy](/docs/guides/proxy) will include in the request to the custom endpoint
- The logs viewer now supports exporting the currently loaded rows as a CSV or JSON file

### API (version 0.0.60)

- Make PG_URL configuration more uniform between nodeJS and python clients

### SDK (version 0.0.175)

- Fix bug with serializing ReadonlyAttachment in logs

## Week of 2024-11-25

- Experiment columns can now be reordered from the column menu
- You can now customize legends in monitor charts. Select a legend item to highlight its data, Shift (⇧) + Click to select multiple items, or Command (⌘) / Ctrl (⌃) + Click to deselect

### SDK (version 0.0.174)

- AI SDK fixes: support for image URLs and properly formatted tool calls so "Try prompt" works in the UI

### SDK (version 0.0.173)

- Attachments can now be loaded when iterating an experiment or dataset

### SDK (version 0.0.172)

- Fix a bug where `braintrust eval` did not respect certain configuration options, like `base_experiment_id`
- Fix a bug where `invoke` in the Python SDK did not properly stream responses

## Week of 2024-11-18

- The Traceloop OTEL integration now uses the input and output attributes to populate the corresponding fields in Braintrust
- The monitor page now supports querying experiment metrics
- Removed the `filters` param from the REST API fetch endpoint. For complex
queries, we recommend using the `/btql` endpoint ([docs](/docs/reference/btql))
- New experiment summary layout option, a url-friendly view for experiment summaries that respects all filters
- Add a default limit of 10 to all fetch and `/btql` requests for project_logs
- You can now export your prompts from the playground as code snippets and run them through the [AI proxy](/docs/guides/proxy)
- Add a fallback for the "add prompt" dropdown button in the playground, which
will search for prompts within the current project if the cross-org prompts
query fails

### SDK (version 0.0.171)

- Add a `.data` method to the `Attachment` class, which lets you inspect the
loaded attachment data

## Week of 2024-11-12

- Support for creating and pushing custom Python tools and prompts from your codebase with `braintrust push`. Read the guides to [tools](/docs/guides/functions/tools) and [prompts](/docs/guides/functions/prompts) for more information
- You can now view grouped summary data for all experiments by selecting **Include comparisons in group** from the **Group by** dropdown inside an experiment
- The experiments page now supports downloading as CSV/JSON
- Downloading or duplicating a dataset in the UI now properly copies all dataset rows
- You can now view a score data as a bar chart for your experiments data by selecting **Score comparison** from the X axis selector
- Trials information is now shown as a separate column in diff mode in the experiment table
- Cmd/Ctrl + S hotkey to save from prompts in the playground and function dialogs

### SDK (version 0.0.170)

- Support uploading [file attachments in the Python SDK](/docs/reference/libs/python#attachment-objects)
- Log, feedback, and dataset inputs to the Python SDK are now synchronously deep-copied for more consistent logging

### SDK (version 0.0.169)

- The Python SDK `Eval()` function has been split into `Eval()` and `EvalAsync()` to make it clear which one should be called in an asynchronous context. The behavior of `Eval()` remains unchanged. However, `Eval()` callers running in an asynchronous context are strongly recommended to switch to `EvalAsync()` to improve type safety
- Improved type annotations in the Python SDK

### SDK (version 0.0.168)

- A new `Span.permalink()` method allows you to format a permalink for the current span. See [TypeScript docs](/docs/reference/libs/nodejs/interfaces/Span#permalink) or [Python docs](/docs/reference/libs/python#permalink) for details
- `braintrust push` support for Python tools and prompts

## Week of 2024-11-04

- The Braintrust [AI Proxy](/docs/guides/proxy) now supports the [OpenAI Realtime API](https://platform.openai.com/docs/guides/realtime), providing observability for voice-to-voice model sessions and simplifying backend infrastructure
- Add "Group by" functionality to the monitor page
- The experiment table can now be visualized in a [grid layout](/docs/guides/evals/interpret#grid-layout), where each column represents an experiment to compare long-form outputs side-by-side
- 'Select all' button in permission dialogs
- Create custom columns on dataset, experiment and logs tables from `JSON` values in `input`, `output`, `expected`, or `metadata` fields

### API (version 0.0.59)
- Fix permissions bug with updating org-scoped env vars

## Week of 2024-10-28

- The Braintrust [AI Proxy](/docs/guides/proxy) can now [issue temporary credentials](/docs/guides/proxy#api-key-management) to access the proxy for a limited time. This can be used to make AI requests directly from frontends and mobile apps, minimizing latency without exposing your API keys
- Move experiment score summaries to the table column headers. To view improvements and regressions per metadata or input group, first group the table by the relevant field. Sooo much room for [table] activities!
- You now receive a clear error message if you run out of free tier capacity while running an experiment from the playground
- Filters on JSON fields now support array indexing, e.g. `metadata.foo[0] = 'bar'`. See [docs](/docs/reference/btql#Expressions)

### SDK (version 0.0.168)

- `initDataset()`/`init_dataset()` used in `Eval()` now tracks the dataset ID and links to each row in the dataset properly

## Week of 2024-10-21

- Preview [file attachments](/docs/guides/tracing#uploading-attachments) in the trace view
- View and filter by comments in the experiment table
- Add table row numbers to experiments, logs, and datasets

### SDK (version 0.0.167)

- Support uploading [file attachments in the TypeScript SDK](/docs/reference/libs/nodejs/classes/Attachment)
- Log, feedback, and dataset inputs to the TypeScript SDK are now synchronously deep-copied for more consistent logging
- Address an issue where the TypeScript SDK could not make connections when running in a Cloudflare Worker

### API (version 0.0.59)

- Support uploading [file attachments](/docs/reference/libs/nodejs/classes/Attachment)
- You can now export [OpenTelemetry (OTel)](https://opentelemetry.io/docs/specs/otel/) traces to Braintrust. See
  the [tracing guide](/docs/guides/tracing/integrations#opentelemetry-otel) for more details

## Week of 2024-10-14

- The Monitor page now shows an aggregate view of log scores over time
- Improvement/Regression filters between experiments are now saved to the URL
- Add `max_concurrency` and `trial_count` to the playground when kicking off evals. `max_concurrency` is useful to
  avoid hitting LLM rate limits, and `trial_count` is useful for evaluating applications that have
  non-deterministic behavior
- Show a button to scroll to a single search result in a span field when using trace search
- Indicate spans with errors in the trace span list

### SDK (version 0.0.166)

- Allow explicitly specifying git metadata info in the Eval framework

### SDK (version 0.0.165)

- Support specifying dataset-level metadata in `initDataset/init_dataset`

### SDK (version 0.0.164)

- Add `braintrust.permalink` function to create deep links pointing to
particular spans in the Braintrust UI

## Week of 2024-10-07

- After using "Copy to Dataset" to create a new dataset row, the audit log of the new row now links back to the original experiment, log, or other dataset
- Tools now stream their `stdout` and `stderr` to the UI. This is helpful for debugging
- Fix prompt, scorer, and tool dropdowns to only show the correct function types

### SDK (version 0.0.163)

- Fix Python SDK compatibility with Python 3.8

### SDK (version 0.0.162)

- Fix Python SDK compatibility with Python 3.9 and older

### SDK (version 0.0.161)

- Add utility function `spanComponentsToObjectId` for resolving the object ID
from an exported span slug

## Week of 2024-09-30

- The [Github action](/docs/guides/evals/run#github-action) now supports Python runtimes
- Add support for [Cerebras](https://cerebras.ai/) models in the proxy, playground, and saved prompts
- You can now create [span iframe viewers](/docs/guides/tracing#custom-span-iframes) to visualize span data in a custom iframe
  In this example, the "Table" section is a custom span iframe
![Span iframe](./guides/traces/span-iframe.png)
- `NOT LIKE`, `NOT ILIKE`, `NOT INCLUDES`, and `NOT CONTAINS` supported in BTQL
- Add "Upload Rows" button to insert rows into an existing dataset from CSV or JSON
- Add "Maximum" aggregate score type
- The experiment table now supports grouping by input (for trials) or by a metadata field
    - The Name and Input columns are now pinned
- Gemini models now support multimodal inputs

## Week of 2024-09-23

- Basic monitor page that shows aggregate values for latency, token count, time to first token, and cost for logs
- Create custom tools to use in your prompts and in the playground. See the [docs](/docs/guides/prompts#calling-external-tools) for more details
- <Link href="/app/settings?subroute=env-vars" target="_blank">Set org-wide environment variables</Link> to use in these tools
- Pull your prompts to your codebase using the `braintrust pull` command
- Select and compare multiple experiments in the experiment view using the `compared with` dropdown
- The playground now displays aggregate scores (avg/max/min) for each prompt and supports sorting rows by a score
- Compare span field values side-by-side in the trace viewer when fullscreen and diff mode is enabled

<LoomVideo id="41a9beec00324500a2221b15bf9483cf" />

### SDK (version 0.0.160)

- Fix a bug with `setFetch()` in the TypeScript SDK

### SDK (version 0.0.159)

- In Python, running the CLI with `--verbose` now uses the `INFO` log level, while still printing full stack traces. Pass the flag twice (`-vv`) to use the `DEBUG` log level
- Create and push custom tools from your codebase with `braintrust push`. See [docs](/docs/guides/prompts#calling-external-tools) for more details. TypeScript only for now
- A long awaited feature: you can now pull prompts to your codebase using the `braintrust pull` command. TypeScript only for now

### API (version 0.0.56)

- Hosted tools are now available in the API
- Environment variables are now supported in the API (not yet in the standard REST API). See the [docker compose file](https://github.com/braintrustdata/braintrust-deployment/blob/main/docker/docker-compose.api.yml#L65)
  for information on how to configure the secret used to encrypt them if you are using Docker
- Automatically backfill `function_data` for prompts created via the API

## Week of 2024-09-16

- The tag picker now includes tags that were added dynamically via API, in addition to the tags configured for your project
- Added a REST API for managing AI secrets. See [docs](/docs/reference/api/AiSecrets)


### SDK (version 0.0.158)

- A dedicated `update` method is now available for datasets
- Fixed a Python-specific error causing experiments to fail initializing when git diff --cached encounters invalid or inaccessible Git repositories
- Token counts have the correct units when printing `ExperimentSummary` objects
- In Python, `MetricSummary.metric` could have an `int` value. The type annotation has been updated

## Week of 2024-09-09

- You can now create server-side online evaluations for your logs. Online evals support both [autoevals](/docs/reference/autoevals) and
  [custom scorers](/docs/guides/playground) you define as LLM-as-a-judge, TypeScript, or Python functions. See
  [docs](/docs/guides/evals/write#online-evaluation) for more details
<LoomVideo id="13e916c6095c4a98bc5682bed038c7ea" />
- New member invitations now support being added to multiple permission groups
- Move datasets and prompts to a new Library navigation tab, and include a list of custom scorers
- Clean up tree view by truncating the root preview and showing a preview of a node only if collapsed
![Truncated tree view](./reference/release-notes/truncated-tree-view.png)
- Automatically save changes to table views

## Week of 2024-09-02

- You can now upload typescript evals from the command line as functions, and then use them in the playground
- Click a span field line to highlight it and pin it to the URL
- Copilot tab autocomplete for prompts and data in the Braintrust UI

```bash
# This will bundle and upload the task and scorer functions to Braintrust
npx braintrust eval --bundle
```

### API (version 0.0.54)

- Support for bundled eval uploads
- The `PATCH` endpoint for prompts now supports updating the `slug` field

### SDK (version 0.0.157)

- Enable the `--bundle` flag for `braintrust eval` in the TypeScript SDK

## Week of 2024-08-26

- Basic filter UI (no BTQL necessary)
- Add to dataset dropdown now supports adding to datasets across projects
- Add REST endpoint for batch-updating ACLs: `/v1/acl/batch_update`
- Cmd/Ctrl click on a table row to open it in a new tab
- Show the last 5 basic filters in the filter editor
- You can now explicitly set and edit prompt slugs

### Autoevals (version 0.0.86)

- Add support for Azure OpenAI in node

### SDK (version 0.0.155)

- The client wrappers `wrapOpenAI()`/`wrap_openai()` now support [Structured Outputs](https://platform.openai.com/docs/guides/structured-outputs)

### API (version 0.0.54)

- Don't fail insertion requests if realtime broadcast fails

## Week of 2024-08-19

- Fixed comment deletion
- You can now use `%` in BTQL queries to represent percent values. E.g. `50%` will be interpreted as `0.5`

### API (version 0.0.54)

- Performance optimizations to filters on `scores`, `metrics`, and `created` fields
- Performance optimizations to filter subfields of `metadata` and `span_attributes`

## Week of 2024-08-12

- You can now create custom LLM and code (TypeScript and Python) evaluators in the playground
<LoomVideo id="407591dee805422588ee83a8bcb44100" />

- Fullscreen trace toggle
- Datasets now accept JSON file uploads
- When uploading a CSV/JSON file to a dataset, columns/fields named `input`, `expected`, and `metadata`
are now auto-assigned to the corresponding dataset fields
- Fix bug in logs/dataset viewer when changing the search params

### API (version 0.0.53)

- The API now supports running custom LLM and code (TypeScript and Python) functions. To enable this in the:
  - AWS Cloudformation stack: turn on the `EnableQuarantine` parameter
  - Docker deployment: set the `ALLOW_CODE_FUNCTION_EXECUTION` environment variable to `true`

## Week of 2024-08-05

- Full text search UI for all span contents in a trace
- New metrics in the UI and summary API: prompt tokens, completion tokens, total tokens, and LLM duration
  - These metrics, along with cost, now exclude LLM calls used in autoevals (as of 0.0.85)
- Switching organizations via the header navigates to the same-named project in the selected organization
- Added `MarkAsyncWrapper` to the Python SDK to allow explicitly marking
functions which return awaitable objects as async

### Autoevals (version 0.0.85)

- LLM calls used in autoevals are now marked with `span_attributes.purpose = "scorer"` so they can be excluded from
  metric and cost calculations

### Autoevals (version 0.0.84)

- Fix a bug where `rationale` was incorrectly formatted in Python
- Update the `full` docker deployment configuration to bundle the metadata DB
  (supabase) inside the main docker compose file. Thus no separate supabase
  cluster is required. See
  [docs](/docs/guides/self-hosting/docker#full-configuration) for details. If
  you are upgrading an existing full deployment, you will likely want to mark
  the supabase db volumes `external` to continue using your existing data (see
  comments in the `docker-compose.full.yml` file for more details)

### SDK (version 0.0.151)

- `Eval()` can now take a base experiment. Provide either `baseExperimentName`/`base_experiment_name` or
  `baseExperimentId`/`base_experiment_id`

## Week of 2024-07-29

- Errors now show up in the trace viewer
- New cookbook recipe on [benchmarking LLM providers](/docs/cookbook/recipes/ProviderBenchmark)
- Viewer mode selections will no longer automatically switch to a non-editable view if the field is editable and persist across trace/span changes
- Show `%` in diffs instead of `pp`
- Add rename, delete and copy current project id actions to the project dropdown
- Playgrounds can now be shared publicly
- Duration now reflects the "task" duration not the overall test case duration (which also includes scores)
- Duration is now also displayed in the experiment overview table
- Add support for Fireworks and Lepton inference providers
- "Jump to" menu to quickly navigate between span sections
- Speed up queries involving metadata fields, e.g. `metadata.foo ILIKE '%bar%'`, using the columnstore backend if it is available
- Added `project_id` query param to REST API queries which already accept
  `project_name`. E.g. [GET
  experiments](/docs/reference/api/Experiments#list-experiments)
- Update to include the latest Mistral models in the proxy/playground

### SDK (version 0.0.148)

- While tracing, if your code errors, the error will be logged to the span. You can also manually log the `error` field through the API
  or the logging SDK

### SDK (version 0.0.147)

- `project_name` is now `projectName`, etc. in the `invoke(...)` function in TypeScript
- `Eval()` return values are printed in a nicer format (e.g. in Notebooks)
- [`updateSpan()`/`update_span()`](/docs/guides/tracing#updating-spans) allows you to update a span's fields after it has been created

## Week of 2024-07-22

- Categorical human review scores can now be re-ordered via Drag-n-Drop
![Reorder categorical score](./reference/release-notes/category-score-reorder.gif)
- Human review row selection is now a free text field, enabling a quick jump to a specific row
![Human review free text](./reference/release-notes/humanreviewfreetext.png)
- Added REST endpoint for managing org membership. See
  [docs](/docs/reference/api/Organizations#modify-organization-membership)

### API (version 0.0.51)

* The proxy is now a first-class citizen in the API service, which simplifies deployment and sets the groundwork for some
  exciting new features. Here is what you need to know:
  * The updates are available as of API version 0.0.51
  * The proxy is now accessible at `https://api.braintrust.dev/v1/proxy`. You can use this as a base URL in your OpenAI client,
    instead of `https://braintrustproxy.com/v1`. [NOTE: The latter is still supported, but will be deprecated in the future.]
  * If you are self-hosting, the proxy is now bundled into the API service. That means you no longer need to deploy the proxy as
    a separate service
  * If you have deployed through AWS, after updating the Cloudformation, you'll need to grab the "Universal API URL" from the
    "Outputs" tab

![Universal URL Cloudformation](./reference/release-notes/universal-url-cloudformation.png)

  * Then, replace that in your settings page <Link href="/app/settings?subroute=api-url" target="_blank">settings page</Link>

![Universal API](./reference/release-notes/universal-api.png)

  * If you have a Docker-based deployment, you can just update your containers
  * Once you see the "Universal API" indicator, you can remove the proxy URL from your settings page, if you have it set

### SDK (version 0.0.146)

* Add support for `max_concurrency` in the Python SDK
* Hill climbing evals that use a `BaseExperiment` as data will use that as the default base experiment

## Week of 2024-07-15

- In preparation for auth changes, we are making a series of updates that may affect self-deployed instances:
  - Preview URLs will now be subdomains of `*.preview.braintrust.dev` instead of `vercel.app`. Please add this domain to your
    allow list
  - To continue viewing preview URLs, you will need to update your stack (to update the allow list to include the new domain pattern)
  - The data plane may make requests back to `*.preview.braintrust.dev` URLs. This allows you to test previews that include control plane
    changes. You may need to whitelist traffic from the data plane to `*.preview.braintrust.dev` domains
  - Requests will optionally send an additional `x-bt-auth-token` header. You may need to whitelist this header
  - User impersonation through the `x-bt-impersonate-user` header now accepts
    either the user's id or email. Previously only user id was accepted

### Autoevals (version 0.0.80)

- New `ExactMatch` scorer for comparing two values for exact equality

### Autoevals (version 0.0.77)

* Officially switch the default model to be `gpt-4o`. Our testing showed that it performed on average 10% more accurately than `gpt-3.5-turbo`!
* Support claude models (e.g. claude-3-5-sonnet-20240620). You can use them by simply specifying the `model` param in any LLM based evaluator
  * Under the hood, this will use the proxy, so make sure to configure your Anthropic API keys in your settings

## Week of 2024-07-08

- Human review scores are now sortable from the project configuration page
![Reorder scores](./reference/release-notes/reorder-human-review-scores.gif)
- Streaming support for tool calls in Anthropic models through the proxy and playground
- The playground now supports different "parsing" modes:
  - `auto`: (same as before) the completion text and the first tool call arguments, if any
  - `parallel`: the completion text and a list of all tool calls
  - `raw`: the completion in the OpenAI non-streaming format
  - `raw_stream`: the completion in the OpenAI streaming format
- Cleaned up environment variables in the public [docker
  deployment](https://github.com/braintrustdata/braintrust-deployment/tree/main/docker). Functionally, nothing has changed


### Autoevals (version 0.0.76)

- New `.partial(...)` syntax to initialize a scorer with partial arguments like `criteria` in `ClosedQA`
- Allow messages to be inserted in the middle of a prompt

## Week of 2024-07-01

- Table views [can now be saved](/docs/reference/views), persisting the BTQL filters, sorts, and column state
- Add support for the new `window.ai` model into the playground
![window.ai](./reference/release-notes/window-ai.gif)
- Use push history when navigating table rows to allow for back button navigation
- In the experiments list, grouping by a metadata field will group rows in the table as well
- Allow the trace tree panel to be resized
- Port the log summary query to BTQL. This should speed up the query, especially
  if you have clickhouse configured in your cloud environment. This
  functionality requires upgrading your data backend to version 0.0.50

### SDK (version 0.0.140)

- New `wrapTraced` function allows you to trace javascript functions in a more ergonomic way

```typescript #skip-compile
import { wrapTraced } from "braintrust";

const foo = wrapTraced(async function foo(input) {
  const resp = await client.chat.completions.create({
    model: "gpt-3.5-turbo",
    messages: [{ role: "user", content: input }],
  });
  return resp.choices[0].message.content ?? "unknown";
});
```


### SDK (version 0.0.138)
- The TypeScript SDK's `Eval()` function now takes a `maxConcurrency` parameter, which bounds the
  number of concurrent tasks that run
- `braintrust install api` now sets up your API and Proxy URL in your environment
- You can now specify a custom `fetch` implementation in the TypeScript SDK

## Week of 2024-06-24

- Update the experiment progress and experiment score distribution chart layouts
- Format table column headers with icons
- Move active filters to the table toolbar
- Enable RBAC for all users. When inviting a new member, prompt to add that member to an RBAC Permission group
- Use btql to power the datasets list, making it significantly faster if you have multiple large datasets
- Experiments list chart supports click interactions. Left click to select an experiment, right click to add an annotation
- Jump into comparison view between 2 experiments by selecting them in the table an clicking "Compare"

### Deployment

- The proxy service now supports more advanced functionality which requires setting the `PG_URL` and `REDIS_URL` parameters. If you do not
  set them, the proxy will still run without caching credentials or requests

## Week of 2024-06-17

- Add support for labeling [expected fields using human review](/docs/guides/human-review#writing-categorical-scores-to-expected-field)
- Create and edit descriptions for datasets
- Create and edit metadata for prompts
- Click scores and attributes (tree view only) in the trace view to filter by them
- Highlight the experiments graph to filter down the set of experiments
- Add support for new models including Claude 3.5 Sonnet

## Week of 2024-06-10

- Improved empty state and instructions for custom evaluators in the playground
- Show query examples when filtering/sorting
- [Custom comparison keys](/docs/guides/evals/interpret#customizing-the-comparison-key) for experiments
- New model dropdown in the playground/prompt editor that is organized by provider and model type

## Week of 2024-06-03

- You can now collapse the trace tree. It's auto collapsed if you have a single span
![Collapsible trace tree](./reference/release-notes/trace-tree.png)
- Improvements to the experiment chart including greyed out lines for inactive scores and improved legend
- Show diffs when you save a new prompt version

![Prompt diff](./reference/release-notes/save-prompt.png)

## Week of 2024-05-27

- You can now see which users are viewing the same traces as you are in real-time
- Improve whitespace and presentation of diffs in the trace view
- Show markdown previews in score editor
- Show cost in spans and display the average cost on experiment summaries and diff views
- Published a new [Text2SQL eval recipe](/docs/cookbook/recipes/Text2SQL-Data)
- Add groups view for RBAC

## Week of 2024-05-20

- Deprecate the legacy dataset format (`output` in place of `expected`) in a new version of the SDK (0.0.130). For now, data can still be fetched in the legacy format by setting the `useOutput` / `use_output` flag to false when using `initDataset()` / `init_dataset()`. We recommend updating your code to use datasets with `expected` instead of `output` as soon as possible
- Improve the UX for saving and updating prompts from the playground
- New hide/show column controls on all tables
- New [model comparison](/docs/cookbook/recipes/ModelComparison) cookbook recipe
- Add support for model / metadata comparison on the experiments view
- New experiment picker dropdown
- Markdown support in the LLM message viewer

## Week of 2024-05-13

- Support copying to clipboard from `input`, `output`, etc. views
- Improve the empty-state experience for datasets
- New multi-dimensional charts on the experiment page for comparing models and model parameters
- Support `HTTPS_PROXY`, `HTTP_PROXY`, and `NO_PROXY` environment variables in the API containers
- Support infinite scroll in the logs viewer and remove dataset size limitations

## Week of 2024-05-06

- Denser trace view with span durations built in
- Rework pagination and fix scrolling across multiple pages in the logs viewer
- Make BTQL the default search method
- Add support for Bedrock models in the playground and the proxy
- Add "copy code" buttons throughout the docs
- Automatically overflow large objects (e.g. experiments) to S3 for faster loading and better performance

## Week of 2024-04-29

- Show images in LLM view, adding the ability to display images in the LLM view in the trace viewer
  ![Images in playground](./reference/release-notes/326593724-6a33c3f9-6aad-44a8-b978-d1d8245dcc66.png)
- Send an invite email when you invite a new user to your organization
- Support selecting/deselecting scores in the experiment view
- Roll out [Braintrust Query Language](/docs/reference/btql) (BTQL) for querying logs and traces

## Week of 2024-04-22

- Smart relative time labels for dates (`1h ago`, `3d ago`, etc.)
- Added double quoted string literals support, e.g., `tags contains "foo"`
- Jump to top button in trace details for easier navigation
- Fix a race condition in distributed tracing, in which subspans could hit the
  backend before their parent span, resulting in an inaccurate trace structure

<Callout type="warn">

As part of this change, we removed the `parent_id` argument from the latest SDK,
which was previously deprecated in favor of `parent`. `parent_id` is only able
to use the race-condition-prone form of distributed tracing, so we felt it would
be best for folks to upgrade any of their usages from `parent_id` to `parent`
Before upgrading your SDK, if you are currently using `parent_id`, you can port
over to using `parent` by changing any exported IDs from `span.id` to
`span.export()` and then changing any instances of `parent_id=[span_id]` to
`parent=[exported_span]`

For example, if you had distributed tracing code like the following:

<CodeTabs>

<TSTab>

```javascript #skip-compile
import { initLogger } from "braintrust";

const logger = initLogger({
  projectName: "My Project",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

export async function POST(req: Request) {
  return logger.traced(async (span) => {
    const { body } = req;
    const result = await someLLMFunction(body);
    span.log({ input: body, output: result });
    return {
      result,
      requestId: span.id,
    };
  });
}

export async function POSTFeedback(req: Request) {
  logger.traced(
    async (span) => {
      logger.logFeedback({
        id: span.id, // Use the newly created span's id, instead of the original request's id
        comment: req.body.comment,
        scores: {
          correctness: req.body.score,
        },
        metadata: {
          user_id: req.user.id,
        },
      });
    },
    {
      parentId: req.body.requestId,
      name: "feedback",
    },
  );
}
```

</TSTab>

<PYTab>

```python
from braintrust import init_logger

logger = init_logger(project="My Project")


def my_route_handler(req):
    with logger.start_span() as span:
        body = req.body
        result = some_llm_function(body)
        span.log(input=body, output=result)
        return {
            "result": result,
            "request_id": span.id,
        }


def my_feedback_handler(req):
    with logger.start_span("feedback", parent_id=req.body.request_id) as span:
        logger.log_feedback(
            id=span.id,  # Use the newly created span's id, instead of the original request's id
            scores={
                "correctness": req.body.score,
            },
            comment=req.body.comment,
            metadata={
                "user_id": req.user.id,
            },
        )
```

</PYTab>

</CodeTabs>

It would now look like this:

<CodeTabs>

<TSTab>

```javascript #skip-compile
import { initLogger } from "braintrust";

const logger = initLogger({
  projectName: "My Project",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

export async function POST(req: Request) {
  return logger.traced(async (span) => {
    const { body } = req;
    const result = await someLLMFunction(body);
    span.log({ input: body, output: result });
    return {
      result,
      requestId: span.export(),
    };
  });
}

export async function POSTFeedback(req: Request) {
  logger.traced(
    async (span) => {
      logger.logFeedback({
        id: span.id, // Use the newly created span's id, instead of the original request's id
        comment: req.body.comment,
        scores: {
          correctness: req.body.score,
        },
        metadata: {
          user_id: req.user.id,
        },
      });
    },
    {
      parent_id: req.body.requestId,
      name: "feedback",
    },
  );
}
```

</TSTab>

<PYTab>

```python
from braintrust import init_logger

logger = init_logger(project="My Project")


def my_route_handler(req):
    with logger.start_span() as span:
        body = req.body
        result = some_llm_function(body)
        span.log(input=body, output=result)
        return {
            "result": result,
            "request_id": span.export(),
        }


def my_feedback_handler(req):
    with logger.start_span("feedback", parent=req.body.request_id) as span:
        logger.log_feedback(
            id=span.id,  # Use the newly created span's id, instead of the original request's id
            scores={
                "correctness": req.body.score,
            },
            comment=req.body.comment,
            metadata={
                "user_id": req.user.id,
            },
        )
```

</PYTab>

</CodeTabs>

</Callout>

## Week of 2024-04-15

- Incremental support for roles-based access control (RBAC) logic within the API
  server backend

<Callout type="warn">

As part of this change, we removed certain API endpoints which are no longer in
use. In particular, the `/crud/{object_type}` endpoint. For the handful of
usages of these endpoints in old versions of the SDK libraries, we added
backwards-compatibility routes, but it is possible we may have missed a few
Please let us know if your code is trying to use an endpoint that no longer
exists and we can remediate

</Callout>

- Changed the semantics of experiment initialization with `update=True`
  Previously, we would require the experiment to already exist, now we will
  create the experiment if it doesn't already exist otherwise return the
  existing one

<Callout type="warn">

This change affects the semantics of the `PUT /v1/experiment` operation, so that
it will not replace the contents of an existing experiment with a new one, but
instead just return the existing one, meaning it behaves the same as `POST
/v1/experiment`. Eventually we plan to revise the update semantics for other
object types as well. Therefore, we have deprecated the `PUT` endpoint across
the board and plan to remove it in a future revision of the API

</Callout>

## Week of 2024-04-08

- Added support for new multimodal models (`gpt-4-turbo`, `gpt-4-vision-preview`, `gpt-4-1106-vision-preview`,
  `gpt-4-turbo-2024-04-09`, `claude-3-opus-20240229`, `claude-3-sonnet-20240229`, `claude-3-haiku-20240307`)
- Introduced [REST API for RBAC](/docs/api/spec#roles) (Role-Based Access Control) objects including CRUD operations on roles, groups, and permissions, and added a read-only API for users
- Improved AI search and added positive/negative tag filtering in AI search. To positively filter, prefix the tag with `+`, and to negatively filter, prefix the tag with `-`

<Callout type="warn">
  We are making some systematic changes to the search experience, and the search
  syntax is subject to change
</Callout>

## Week of 2024-04-01

- Added functionality for distributed tracing. See the
  [docs](/docs/guides/tracing#distributed-tracing) for more details

<Callout type="warn">

As part of this change, we had to rework the core logging implementation in the
SDKs to rely on some newer backend API features. Therefore, if you are hosting
Braintrust on-prem, before upgrading your SDK to any version `>= 0.0.115`, make
sure your API version is `>= 0.0.35`. You can query the version of the on-prem
server with `curl [api-url]/version`, where the API URL can be found on the <Link href="/app/settings?subroute=api-url" target="_blank">settings page</Link>

</Callout>

## Week of 2024-03-25

- Introduce multimodal support for OpenAI and Anthropic models in the prompt playground and proxy. You can now pass image URLs, base64-encoded image strings, or mustache template variables to models that support multimodal inputs
  ![Multimodal prompt](./reference/release-notes/multimodal-prompt.gif)
- The REST API now gzips responses
- You can now return dynamic arrays of scores in `Eval()` functions ([docs](/docs/guides/evals#dynamic-scoring))
- Launched [Reporters](/docs/guides/evals#custom-reporters), a way to summarize and report eval results in a custom format
- New coat of paint in the trace view
- Added support for Clickhouse as an additional storage backend, offering a more scalable solution for handling large datasets and performance improvements for certain query types. You can enable it by
  setting the `UseManagedClickhouse` parameter to `true` in the CloudFormation template or installing the docker container
- Implemented realtime checks using a WebSocket connection and updated proxy configurations to include CORS support
- Introduced an API version checker tool so you know when your API version is outdated

## Week of 2024-03-18

- Add new database parameters for external databases in the CloudFormation template
- Faster optimistic updates for large writes in the UI
- "Open in playground" now opens a lighter weight modal instead of the full playground
- Can create a new prompt playground from the prompt viewer

## Week of 2024-03-11

- Shipped support for [prompt management](/docs/guides/prompts)
- Moved playground sessions to be within projects. All existing sessions are now in the "Playground Sessions" project
- Allowed customizing proxy and real-time URLs through the web application, adding flexibility for different deployment scenarios
- Improved documentation for Docker deployments
- Improved folding behavior in data editors

## Week of 2024-03-04

- Support custom models and endpoint configuration for all providers
- New add team modal with support for multiple users
- New information architecture to enable faster project navigation
- Experiment metadata now visible in the experiments table
- Improve UI write performance with batching
- Log filters now apply to _any_ span
- Share button for traces
- Images now supported in the tree view (see [tracing docs](/docs/guides/tracing#multimodal-content) for more)

## Week of 2024-02-26

- Show auto scores before manual scores (matching trace) in the table
- New logo is live!
- Any span can now submit scores, which automatically average in the trace. This makes it easier to label
  scores in the spans where they originate
- Improve sidebar scrolling behavior
- Add AI search for datasets and logs
- Add tags to the SDK
- Support viewing and updating metadata on the experiment page

## Week of 2024-02-19

<Callout type="warn">

We rolled out a breaking change to the REST API that renames the
`output` field to `expected` on dataset records. This change brings
the API in line with [last week's update](#week-of-2024-02-12) to
the Braintrust SDK. For more information, refer to the REST API docs
for dataset records ([insert](/docs/api/spec#insert-dataset-events)
and [fetch](/docs/api/spec#fetch-dataset-get-form))

</Callout>

- Add support for [tags](/docs/guides/logging#tags-and-queues)
- Score fields are now sorted alphabetically
- Add support for Groq ModuleResolutionKind
- Improve tree viewer and XML parser
- New experiment page redesign

## Week of 2024-02-12

<Callout type="warn">

We are rolling out a change to dataset records that renames the `output`
field to `expected`. If you are using the SDK, datasets will still fetch
records using the old format for now, but we recommend future-proofing
your code by setting the `useOutput` / `use_output` flag to false when
calling `initDataset()` / `init_dataset()`, which will become the default
in a future version of Braintrust

When you set `useOutput` to false, your dataset records will contain
`expected` instead of `output`. This makes it easy to use them with
`Eval(...)` to provide expected outputs for scoring, since you'll
no longer have to manually rename `output` to `expected` when passing
data to the evaluator:

<CodeTabs>
<TSTab>

```typescript
import { Eval, initDataset } from "braintrust";
import { Levenshtein } from "autoevals";

Eval("My Eval", {
  data: initDataset("Existing Dataset", { useOutput: false }), // Records will contain `expected` instead of `output`
  task: (input) => "foo",
  scores: [Levenshtein],
});
```

</TSTab>

<PYTab>

```python
from autoevals import Levenshtein
from braintrust import Eval, init_dataset

Eval(
    "My Eval",
    data=init_dataset("Existing Dataset", use_output=False),  # Records will contain `expected` instead of `output`
    task=lambda input: "foo",
    scores=[Levenshtein],
)
```

</PYTab>
</CodeTabs>

Here's an example of how to insert and fetch dataset records using the new format:

<CodeTabs>
<TSTab>

```typescript #skip-compile
import { initDataset } from "braintrust";

// Currently `useOutput` defaults to true, but this will change in a future version of Braintrust
const dataset = initDataset("My Dataset", { useOutput: false });

dataset.insert({
  input: "foo",
  expected: { result: 42, error: null }, // Instead of `output`
  metadata: { model: "gpt-3.5-turbo" },
});
await dataset.flush();

for await (const record of dataset) {
  console.log(record.expected); // Instead of `record.output`
}
```

</TSTab>

<PYTab>

```python
from braintrust import init_dataset

# Currently `use_output` defaults to True, but this will change in a future version of Braintrust
dataset = init_dataset("My Dataset", use_output=False)

dataset.insert(
    input="foo",
    expected=dict(result=42, error=None),  # Instead of `output`
    metadata=dict(model="gpt-3.5-turbo"),
)
dataset.flush()

for record in dataset:
    print(record["expected"])  # Instead of `record["output"]`
```

</PYTab>
</CodeTabs>

</Callout>

- Support duplicate `Eval` names
- Fallback to `BRAINTRUST_API_KEY` if `OPENAI_API_KEY` is not set
- Throw an error if you use `experiment.log` and `experiment.start_span` together
- Add keyboard shortcuts (j/k/p/n) for navigation
- Increased tooltip size and delay for better usability
- Support more viewing modes: HTML, Markdown, and Text

## Week of 2024-02-05

![Playground](/docs/release-notes/ReleaseNotes-2023-02-05-Playground.gif)

- Tons of improvements to the prompt playground:
  - A new "compact" view, that shows just one line per row, so you can quickly scan across rows. You can toggle between the two modes
  - Loading indicators per cell
  - The run button transforms into a "Stop" button while you are streaming data
  - Prompt variables are now syntax highlighted in purple and use a monospace font
  - Tab now autocompletes
  - We no longer auto-create variables as you're typing (was causing more trouble than helping)
  - Slider params like `max_tokens` are now optional
- Cloudformation now supports more granular RDS configuration (instance type, storage, etc)
- **Support optional slider params**
  - Made certain parameters like `max_tokens` optional
  - Accompanies pull request https://github.com/braintrustdata/braintrust-proxy/pull/23
- Lots of style improvements for tables
  - Fixed filter bar styles
  - Rendered JSON cell values using monospace type
  - Adjusted margins for horizontally scrollable tables
  - Implemented a smaller size for avatars in tables
- Deleting a prompt takes you back to the prompts tab

## Week of 2024-01-29

- New [REST API](/docs/api/spec)
- [Cookbook](/docs/cookbook) of common use cases and examples
- Support for [custom models](/docs/guides/playground#custom-models) in the playground
- Search now works across spans, not just top-level traces
- Show creator avatars in the prompt playground
- Improved UI breadcrumbs and sticky table headers

## Week of 2024-01-22

- UI improvements to the playground
- Added an example of [closed QA / extra fields](/docs/guides/evals#additional-fields)
- New YAML parser and new syntax highlighting colors for data editor
- Added support for enabling/disabling certain git fields from collection (in org settings and the SDK)
- Added new GPT-3.5 and 4 models to the playground
- Fixed scrolling jitter issue in the playground
- Made table fields in the prompt playground sticky

## Week of 2024-01-15

- Added ability to download dataset as CSV
- Added YAML support for logging and visualizing traces
- Added JSON mode in the playground
- Added span icons and improved readability
- Enabled shift modifier for selecting multiple rows in Tables
- Improved tables to allow editing expected fields and moved datasets to trace view

## Week of 2024-01-08

- Released new [Docker deployment method for self hosting](https://www.braintrustdata.com/docs/self-hosting/docker)
- Added ability to manually score results in the experiment UI
- Added comments and audit log in the experiment UI

## Week of 2024-01-01

- Added ability to upload dataset CSV files in prompt playgrounds
- Published new [guide for tracing and logging your code](https://www.braintrustdata.com/docs/guides/tracing)
- Added support to download experiment results as CSVs

## Week of 2023-12-25

- API keys are now scoped to organizations, so if you are part of multiple orgs, new API keys will only permit
  access to the org they belong to
- You can now search for experiments by any metadata, including their name, author, or even git metadata
- Filters are now saved in URL state so you can share a link to a filtered view of your experiments or logs
- Improve performance of project page by optimizing API calls

<Callout type="warn">

We made several cleanups and improvements to the low-level typescript and python
SDKs (0.0.86). If you use the Eval framework, nothing should change for you, but
keep in mind the following differences if you use the manual logging
functionality:

- Simplified the low-level tracing API (updated docs coming soon!)
  - The current experiment and current logger are now maintained globally
    rather than as async-task-local variables. This makes it much simpler to
    start tracing with minimal code modification. Note that creating
    experiments/loggers with `withExperiment`/`withLogger` will now set the
    current experiment globally (visible across all async tasks) rather than
    local to a specific task. You may pass `setCurrent: false/set_current=False`
    to avoid setting the global current experiment/logger
  - In python, the `@traced` decorator now logs the function input/output by
    default. This might interfere with code that already logs input/output
    inside the `traced` function. You may pass `notrace_io=True` as an argument
    to `@traced` to turn this logging off
  - In typescript, the `traced` method can start spans under the global
    logger, and is thus async by default. You may pass `asyncFlush: true` to
    these functions to make the traced function synchronous. Note that if the
    function tries to trace under the global logger, it must also have
    `asyncFlush: true`
  - Removed the `withCurrent`/`with_current` functions
  - In typescript, the `Span.traced` method now accepts `name` as an optional
    argument instead of a required positional param. This matches the behavior
    of all other instances of `traced`. `name` is also now optional in python,
    but this doesn't change the function signature
- `Experiments` and `Datasets` are now lazily-initialized, similar to `Loggers`
  This means all write operations are immediate and synchronous. But any metadata
  accessor methods (`[Experiment|Logger].[id|name|project]`) are now async
- Undo auto-inference of `force_login` if `login` is invoked with different
  params than last time. Now `login` will only re-login if `forceLogin: true/force_login=True` is provided

</Callout>

## Week of 2023-12-18

- Dropped the official 2023 Year-in-Review dashboard. Check out yours [here](/app/year-in-review)!

![2023 year in review](/blog/img/2023-summary.png)

- Improved ergonomics for the Python SDK:
  - The `@traced` decorator will automatically log inputs/outputs
  - You no longer need to use context managers to scope experiments or loggers
- Enable skew protection in frontend deploys, so hopefully no more hard refreshes
- Added syntax highlighting in the sidepanel to improve readability
- Add `jsonl` mode to the eval CLI to log experiment summaries in an easy-to-parse format

## Week of 2023-12-11

- Released new [trials](https://www.braintrustdata.com/docs/guides/evals#trials) feature to rerun each input multiple times and collect aggregate results for a more robust score
- Added ability to run evals in the prompt playground. Use your existing dataset and the autoevals functions to score playground outputs
- Released new version of SDK (0.0.81) including a small breaking change. When setting the experiment name in the `Eval` function, the `exprimentName` key pair should be moved to a top level argument
  before:

```
Eval([eval_name], {
  ...,
  metadata: {
    experimentName: [experimentName]
  }
})
```

after:

```
Eval([eval_name], {
  ...,
  experimentName: [experimentName]
})
```

- Added support for Gemini and Mistral Platform in AI proxy and playground

## Week of 2023-12-4

- Enabled the prompt playground and datasets for free users
- Added Together.ai models including Mixtral to AI Proxy
- Turned prompts tab on organization view into a list
- Removed data row limit for the prompt playground
- Enabled configuration for dark mode and light mode in settings
- Added automatic logging of a diff if an experiment is run on a repo with uncommitted changes

## Week of 2023-11-27

- Added experiment search on project view to filter by experiment name
  <figure>
    ![Experiment search and filtering on project
    view](/docs/release-notes/ReleaseNotes11-27-search.gif)
  </figure>
- Upgraded AI Proxy to support [tracking Prometheus metrics](https://github.com/braintrustdata/braintrust-proxy/blob/a31a82e6d46ff442a3c478773e6eec21f3d0ba69/apis/cloudflare/wrangler-template.toml#L19C1-L19C1)
- Modified Autoevals library to use the [AI proxy](/docs/guides/proxy)
- Upgraded Python braintrust library to parallelize evals
- Optimized experiment diff view for performance improvements

## Week of 2023-11-20

- Added support for new Perplexity models (ex: pplx-7b-online) to playground
- Released [AI proxy](/docs/guides/proxy): access many LLMs using one API w/ caching
- Added [load balancing endpoints](/docs/guides/proxy#load-balancing) to AI proxy
- Updated org-level view to show projects and prompt playground sessions
- Added ability to batch delete experiments
- Added support for Claude 2.1 in playground

## Week of 2023-11-13

- Made experiment column resized widths persistent
- Fixed our libraries including Autoevals to work with OpenAI’s new libraries
  <figure>
    ![Added OpenAI function calling in the prompt
    playground](/docs/release-notes/ReleaseNotes-2023-11-functions.gif)
  </figure>
- Added support for function calling and tools in our prompt playground
- Added tabs on a project page for datasets, experiments, etc

## Week of 2023-11-06

- Improved selectors for diffing and comparison modes on experiment view
- Added support for new OpenAI models (GPT4 preview, 3.5turbo-1106) in playground
- Added support for OS models (Mistral, Codellama, Llama2, etc.) in playground using Perplexity's APIs

## Week of 2023-10-30

- Improved experiment sidebar to be fully responsive and resizable
- Improved tooltips within the web UI
- Multiple performance optimizations and bug fixes

## Week of 2023-10-23

- [Improved prompt playground variable handling and visualization](/docs/release-notes/ReleaseNotes-2023-10-PromptPlaygroundVar.mp4)

- Added time duration statistics per row to experiment summaries

![ReleaseNotes-2023-10-dataset.png](/docs/release-notes/ReleaseNotes-2023-10-TimeDurationExperiments.png)

- Multiple performance optimizations and bug fixes

## Week of 2023-10-16

- [Launched new tracing feature: log and visualize complex LLM chains and executions.](/docs/guides/evals#tracing)
- Added a new “text-block” prompt type in the playground that just returns a string or variable back without a LLM call (useful for chaining prompts and debugging)
- Increased default # of rows per page from 10 to 100 for experiments
- UI fixes and improvements for the side panel and tooltips
- The experiment dashboard can be customized to show the most relevant charts

## Week of 2023-10-09

- Performance improvements related to user sessions

## Week of 2023-10-02

- All experiment loading HTTP requests are 100-200ms faster
- The prompt playground now supports autocomplete
- Dataset versions are now displayed on the datasets page

![ReleaseNotes-2023-10-dataset.png](/docs/release-notes/ReleaseNotes-2023-10-dataset.png)

- Projects in the summary page are now sorted alphabetically
- Long text fields in logged data can be expanded into scrollable blocks
- [We evaluated the Alpaca evals leaderboard in Braintrust](https://www.braintrustdata.com/app/braintrustdata.com/p/Alpaca-Evals)
- [New tutorial for finetuning GPT3.5 and evaluating with Braintrust](https://colab.research.google.com/drive/10KIXBHjZ0VUc-zN79_cuVeKy9ZiUQy4M?usp=sharing)

## Week of 2023-09-18

- The Eval framework is now supported in Python! See the updated [evals guide](/docs/guides/evals) for more information:

```python
from autoevals import LevenshteinScorer
from braintrust import Eval

Eval(
    "Say Hi Bot",
    data=lambda: [
        {
            "input": "Foo",
            "expected": "Hi Foo",
        },
        {
            "input": "Bar",
            "expected": "Hello Bar",
        },
    ],  # Replace with your eval dataset
    task=lambda input: "Hi " + input,  # Replace with your LLM call
    scores=[LevenshteinScorer],
)
```

- Onboarding and signup flow for new users
- Switch product font to Inter

## Week of 2023-09-11

- Big performance improvements for registering experiments (down from ~5s to &lt;1s). Update the SDK to take advantage of these improvements
- New graph shows aggregate accuracy between experiments for each score

  ![Score Comparison Chart](/docs/release-notes/ReleaseNotes-2023-09-Comparison.png)

- Throw errors in the prompt playground if you reference an invalid variable
- A significant backend database change which significantly improves performance while reducing costs. Please contact us if you have not already heard from us about upgrading your deployment
- No more record size constraints (previously, strings could be at most 64kb long)
- New autoevals for numeric diff and JSON diff

## Week of 2023-09-05

- You can duplicate prompt sessions, prompts, and dataset rows in the prompt playground
- You can download prompt sessions as JSON files (including the prompt templates, prompts, and completions)
- You can adjust model parameters (e.g. temperature) in the prompt playground
- You can publicly share experiments (e.g. [Alpaca Evals](https://www.braintrustdata.com/app/braintrustdata.com/p/Alpaca-Evals/GPT4-w-metadata-claudegraded?c=llama2-70b-w-metadata-claudegraded))
- Datasets now support editing, deleting, adding, and copying rows in the UI
- There is no longer a 64KB limit on strings

## Week of 2023-08-28

- The prompt playground is now live! We're excited to get your feedback as we continue to build
  this feature out. See [the docs](/docs/guides/playground) for more information

![Sync Playground](/docs/release-notes/ReleaseNotes-2023-08-Playground.gif)

## Week of 2023-08-21

- A new chart shows experiment progress per score over time

![Experiment Progress](/docs/release-notes/ReleaseNotes-2023-08-ExperimentProgress.png)

- The [eval CLI](/docs/guides/evals) now supports `--watch`, which will automatically re-run your evaluation when you make
  changes to your code
- You can now edit datasets in the UI

![Edit Dataset](/docs/release-notes/ReleaseNotes-2023-08-EditDataset.gif)

## Week of 2023-08-14

- Introducing datasets! You can now upload datasets to Braintrust and use them in your experiments. Datasets are
  versioned, and you can use them in multiple experiments. You can also use datasets to compare your model's
  performance against a baseline. Learn more about [how to create and use datasets in the docs](/docs/guides/datasets)
- Fix several performance issues in the SDK and UI

## Week of 2023-08-07

- Complex data is now substantially more performant in the UI. Prior to this change, we ran schema
  inference over the entire `input`, `output`, `expected`, and `metadata` fields, which could result
  in complex structures that were slow and difficult to work with. Now, we simply treat these fields
  as `JSON` types
- The UI updates in real-time as new records are logged to experiments
- Ergonomic improvements to the SDK and CLI:
  - The JS library is now Isomorphic and supports both Node.js and the browser
  - The Evals CLI warns you when no files match the `.eval.[ts|js]` pattern

## Week of 2023-07-31

- You can now break down scores by metadata fields:

![Grouped Score Chart](/docs/release-notes/ReleaseNotes-2023-07-Group-Chart.png)

- Improve performance for experiment loading (especially complex experiments). Prior to this change,
  you may have seen experiments take 30s+ occasionally or even fail. To enable this, you'll need to
  update your CloudFormation

- Support for renaming and deleting experiments:

![Rename Delete Menu](/docs/release-notes/ReleaseNotes-2023-07-Rename-Delete.png)

- When you expand a cell in detail view, the row is now highlighted:

![Highlight Row](/docs/release-notes/ReleaseNotes-2023-08-TableSelected.png)

## Week of 2023-07-24

- A new [framework](/docs/guides/evals) for expressing evaluations in a much simpler way:

```js #skip-compile
import { Eval } from "braintrust";
import { Factuality } from "autoevals";

Eval("My Evaluation", {
  data: () => [
    {
      input: "Which country has the highest population?",
      expected: "China",
      meta: { type: "question" },
    },
  ],
  task: (input) => callModel(input),
  scores: [Factuality],
});
```

Besides being much easier than the logging SDK, this framework sets the foundation for evaluations
that can be run automatically as your code changes, built and run in the cloud, and more. We are
very excited about the use cases it will open up!

- `inputs` is now `input` in the SDK (>= 0.0.23) and UI. You do not need to make any code changes, although you should gradually start
  using the `input` field instead of `inputs` in your SDK calls, as `inputs` is now deprecated and will eventually be removed
- Improved diffing behavior for nested arrays

## Week of 2023-07-17

- A couple of SDK updates (>= v0.0.21) that allow you to update an existing experiment `init(..., update=True)` and specify an
  id in `log(..., id='my-custom-id')`. These tools are useful for running an experiment across multiple processes,
  tasks, or machines, and idempotently logging the same record (identified by its `id`)
  - Note: If you have Braintrust installed in your own cloud environment, make sure to update the CloudFormation (available at
    [https://braintrust-cf.s3.amazonaws.com/braintrust-latest.yaml](https://braintrust-cf.s3.amazonaws.com/braintrust-latest.yaml))
- Tables with lots and lots of columns are now visually more compact in the UI:

_Before:_

![Table before](/docs/release-notes/ReleaseNotes-2023-07-Table-Before.png)

_After:_

![Table after](/docs/release-notes/ReleaseNotes-2023-07-Table-After.png)

## Week of 2023-07-10

- A new [Node.js SDK](/docs/libs/nodejs) ([npm](https://www.npmjs.com/package/braintrust)) which mirrors the [Python SDK](/docs/reference/libs/python). As this SDK is new, please let us know
  if you run into any issues or have any feedback

<Callout type="warn">
If you have Braintrust installed in your own cloud environment, make sure to update the CloudFormation (available at
[https://braintrust-cf.s3.amazonaws.com/braintrust-latest.yaml](https://braintrust-cf.s3.amazonaws.com/braintrust-latest.yaml))
to include some functionality the Node.js SDK relies on

You can do this in the AWS console, or by running the following command (with the `braintrust` command included in the Python SDK)

```bash
braintrust install api <YOUR_CLOUDFORMAT_STACK_NAME> --update-template
```

</Callout>

- You can now swap the primary and comparison experiment with a single click

![Swap experiments](/docs/release-notes/ReleaseNotes-2023-07-Swap.gif)

- You can now compare `output` vs. `expected` within an experiment

![Diff output and expected](/docs/release-notes/ReleaseNotes-2023-07-Diff.gif)

- Version 0.0.19 is out for the SDK. It is an important update that throws an error if your payload is larger than 64KB in size

## Week of 2023-07-03

- Support for real-time updates, using Redis. Prior to this, Braintrust would wait for your data warehouse to sync up with
  Kafka before you could view an experiment, often leading to a minute or two of time before a page loads. Now, we cache experiment
  records as your experiment is running, making experiments load instantly. To enable this, you'll need to update your CloudFormation

- New settings page that consolidates team, installation, and API key settings. You can now invite team members
  to your Braintrust account from the "Team" page

  ![Settings Page](/docs/release-notes/ReleaseNotes-2023-07-Settings.png)

- The experiment page now shows commit information for experiments run inside of a git repository

  ![Git info](/docs/release-notes/ReleaseNotes-2023-07-git-info.png)

## Week of 2023-06-26

- Experiments track their git metadata and automatically find a "base" experiment to compare against, using
  your repository's base branch
- The Python SDK's [`summarize()`](/docs/libs/python#summarize) method now returns an [`ExperimentSummary`](/docs/libs/python#experimentsummary-objects) object with score
  differences against the base experiment (v0.0.10)
- Organizations can now be "multi-tenant", i.e. you do not need to install in your cloud account. If you start with
  a multi-tenant account to try out Braintrust, and decide to move it into your own account, Braintrust can migrate it for you

## Week of 2023-06-19

- New scatter plot and histogram insights to quickly analyze scores and filter down examples

  ![Scatter Plot](/docs/release-notes/ReleaseNotes-2023-06-Scatter.gif)

- API keys that can be set in the SDK (explicitly or through an environment variable) and do not require user login
  Visit the <Link href="/app/settings?subroute=api-keys">settings page</Link> to create an API key
  - Update the braintrust Python SDK to [version 0.0.6](https://pypi.org/project/braintrust/0.0.6/) and the CloudFormation template (https://braintrust-cf.s3.amazonaws.com/braintrust-latest.yaml) to use the new API key feature

## Week of 2023-06-12

- New `braintrust install` CLI for installing the CloudFormation
- Improved performance for event logging in the SDK
- Auto-merge experiment fields with different types (e.g. `number` and `string`)

## Week of 2023-06-05

- [Tutorial guide + notebook](/docs/start)
- Automatically refresh cognito tokens in the Python client
- New filter and sort operators on the experiments table:
  - Filter experiments by changes to scores (e.g. only examples with a lower score than another experiment)
  - Custom SQL filters
  - Filter and sort bubbles to visualize/clear current operations
- [Alpha] SQL query explorer to run arbitrary queries against one or more experiments

  <br />
  <Image
    alt="SQL Explorer"
    src="/docs/release-notes/sql-explorer.png"
    height="300"
    width="300"
  />
