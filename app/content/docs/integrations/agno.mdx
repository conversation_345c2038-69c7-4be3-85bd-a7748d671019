---
title: "Agno"
---

import ModuleInstaller from "#/ui/docs/module-installer";

# Agno

Braintrust traces for [Agno](https://www.agno.com/) agents (Supports Agno version 2 and higher).

<ModuleInstaller languages={["py"]} packageNames="braintrust agno" />

To trace Agno agents with Braintrust using an OpenAI model, configure these environment variables:

```bash title=".env"
BRAINTRUST_API_KEY=your-api-key
OPENAI_API_KEY=your-openai-key
```

Call `setup_agno` and then create an Agent.
### Example

A simple Agno agent that calls Yahoo finance tools.

<ModuleInstaller languages={["py"]} packageNames="braintrust agno yfinance" />

```python title="agno_braintrust.py"
from braintrust.wrappers.agno import setup_agno

setup_agno(project_name="simple-agent-project")

from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.tools.yfinance import YFinanceTools

# Create and configure the agent
agent = Agent(
    name="Stock Price Agent",
    model=OpenAIChat(id="gpt-4o-mini"),
    tools=[YFinanceTools()],
    instructions="You are a stock price agent. Answer questions in the style of a stock analyst.",
)

response = agent.run("What is the current price of AAPL?")
print(response.content)
```
