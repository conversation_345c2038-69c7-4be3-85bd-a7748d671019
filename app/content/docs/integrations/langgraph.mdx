---
title: "LangGraph"
---

import { CodeTabs, PYTab, TSTab } from "#/ui/docs/code-tabs";
import ModuleInstaller from "#/ui/docs/module-installer";

# LangGraph

Trace your LangGraph applications by configuring a global LangChain callback handler.

## Installation

<CodeTabs>

<TSTab>

Install the LangChain integration, Braintrust SDK, and LangGraph:

<ModuleInstaller languages={["ts"]} packageNames="braintrust @braintrust/langchain-js @langchain/core @langchain/langgraph @langchain/openai" />

</TSTab>

<PYTab>

<ModuleInstaller languages={["py"]} packageNames="braintrust braintrust-langchain langchain-core langgraph langchain-openai" />

</PYTab>

</CodeTabs>

## Usage

<CodeTabs>

<TSTab>

```typescript title="trace-langgraph.ts" #skip-compile
import {
  Braintrust<PERSON>allbackHand<PERSON>,
  setG<PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@braintrust/langchain-js";
import { END, START, StateGraph, StateGraphArgs } from "@langchain/langgraph";
import { ChatOpenAI } from "@langchain/openai";
import { initLogger } from "braintrust";

const logger = initLogger({
  projectName: "My Project",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

const handler = new BraintrustCallbackHandler({ logger });
setGlobalHandler(handler);

// Define the state structure for the graph
type HelloWorldGraphState = Record<string, any>;

const graphStateChannels: StateGraphArgs<HelloWorldGraphState>["channels"] = {};

const model = new ChatOpenAI({
  model: "gpt-4o-mini",
});

async function sayHello(state: HelloWorldGraphState) {
  const res = await model.invoke("Say hello");
  return { message: res.content };
}

function sayBye(state: HelloWorldGraphState) {
  console.log(`From the 'sayBye' node: Bye world!`);
  return {};
}

async function main() {
  const graphBuilder = new StateGraph({ channels: graphStateChannels })
    .addNode("sayHello", sayHello)
    .addNode("sayBye", sayBye)
    .addEdge(START, "sayHello")
    .addEdge("sayHello", "sayBye")
    .addEdge("sayBye", END);

  const helloWorldGraph = graphBuilder.compile();

  // Execute the graph - all operations will be logged to Braintrust
  await helloWorldGraph.invoke({});
}

main();
```

Learn more about [LangGraph](https://langchain-ai.github.io/langgraph/) in their documentation.

</TSTab>

<PYTab>

```python title="trace-langgraph.py"
import asyncio
import os
from typing import Dict

from braintrust import init_logger
from braintrust_langchain import BraintrustCallbackHandler, set_global_handler
from langchain_openai import ChatOpenAI
from langgraph.graph import END, START, StateGraph


async def main():
    init_logger(project="My Project", api_key=os.environ.get("BRAINTRUST_API_KEY"))

    handler = BraintrustCallbackHandler()
    set_global_handler(handler)

    # Initialize your LangChain components
    model = ChatOpenAI(model="gpt-4o-mini")

    def say_hello(state: Dict[str, str]):
        response = model.invoke("Say hello")
        return response.content

    def say_bye(state: Dict[str, str]):
        print("From the 'sayBye' node: Bye world!")
        return "Bye"

    # Create the state graph
    workflow = (
        StateGraph(state_schema=Dict[str, str])
        .add_node("sayHello", say_hello)
        .add_node("sayBye", say_bye)
        .add_edge(START, "sayHello")
        .add_edge("sayHello", "sayBye")
        .add_edge("sayBye", END)
    )

    graph = workflow.compile()

    # Execute the graph - all operations will be logged to Braintrust
    await graph.ainvoke({})


if __name__ == "__main__":
    asyncio.run(main())
```

Learn more about [LangGraph](https://langchain-ai.github.io/langgraph/) in their documentation.

</PYTab>

</CodeTabs>

![LangGraph trace visualization in Braintrust showing the execution flow of nodes and their relationships](/docs/integrations/langgraph.png)
