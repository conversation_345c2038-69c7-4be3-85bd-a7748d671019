import "server-only";

import { z } from "zod";
import { trackSegmentEvent } from "#/utils/segment/segment";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import type { AuthLookup } from "#/utils/server-util";

// Base schema for all server-side events
const baseServerEventSchema = z.object({
  userId: z.string(),
  orgId: z.string().optional(),
  timestamp: z.string().optional(), // ISO string
  source: z.literal("server"),
  // Add any other common fields here
});

// Playground run event schema
export const playgroundRunEventSchema = baseServerEventSchema.extend({
  event: z.literal("playgroundRun"),
  properties: z.object({
    playgroundId: z.string(),
    projectId: z.string().nullable(),
    runId: z.string(), // Unique identifier for each playground run
    outcome: z.enum(["success", "error", "cancelled"]),
    errorMessage: z.string().optional(),
    // Run configuration and metadata
    details: z.record(z.unknown()).optional(),
    // Task-specific details (nested within details)
    taskDetails: z
      .array(
        z.object({
          id: z.string().optional(),
          name: z.string().optional(),
          taskIndex: z.number().optional(),
          type: z.enum(["prompt", "function", "remote_eval"]).optional(),
          model: z.string().optional(),
          modelProvider: z.string().optional(),
          sourceType: z
            .enum(["inline", "saved_prompt", "function", "remote"])
            .optional(),
          toolTypes: z.array(z.string()).optional(),
          executionTime: z.number().optional(),
          parentTaskId: z.string().optional(),
          temperature: z.number().optional(),
          topP: z.number().optional(),
          maxTokens: z.number().optional(),
          streamingEnabled: z.boolean().optional(),
          promptType: z.enum(["chat", "completion"]).optional(),
        }),
      )
      .optional(),
  }),
});

// Playground create event schema
export const playgroundCreateEventSchema = baseServerEventSchema.extend({
  event: z.literal("playgroundCreate"),
  properties: z.object({
    playgroundName: z.string(),
    playgroundId: z.string(),
    projectId: z.string(),
    projectName: z.string(),
    initialDataType: z.enum([
      "prompt",
      "function",
      "scorer",
      "dataset",
      "none",
    ]),
    initialObjectId: z.string().optional(), // ID of the initial object (prompt, function, scorer, dataset)
  }),
});

// Playground delete event schema
export const playgroundDeleteEventSchema = baseServerEventSchema.extend({
  event: z.literal("playgroundDelete"),
  properties: z.object({
    playgroundId: z.string(),
    projectId: z.string(),
    projectName: z.string().optional(),
    deletionMethod: z
      .enum(["delete_button", "keyboard_shortcut", "batch_delete", "other"])
      .optional(),
    numTasks: z.number().optional(),
    hasDataset: z.boolean().optional(),
    hasScorers: z.boolean().optional(),
    lastModified: z.string().optional(),
    createdTime: z.string().optional(),
  }),
});

// Experiment create event schema
export const experimentCreateEventSchema = baseServerEventSchema.extend({
  event: z.literal("experimentCreateServer"),
  properties: z.object({
    experimentId: z.string(),
    experimentName: z.string(),
    entryPoint: z.string().optional(),
    flowId: z.string(),
    projectId: z.string(),
    projectName: z.string(),
    orgId: z.string().optional(),
    mode: z.enum(["playground", "single"]).optional(),
    datasetId: z.string().optional(),
    datasetName: z.string().optional(),
    datasetRowCount: z.number().optional(),
    sourcePlaygroundId: z.string().optional(),
    sourceScorerId: z.string().optional(),
    // Method tracking
    method: z.enum(["ui", "sdk"]).default("ui"),
    // Outcome tracking
    outcome: z.enum(["success", "error", "cancelled"]),
    errorMessage: z.string().optional(),
    // Minimal experiment summaries for dedup/aggregation across a flow
    experimentDetails: z
      .array(
        z.object({
          id: z.string(),
          name: z.string(),
          taskId: z.string().optional(),
          sourceElementId: z.string().optional(),
          taskType: z
            .enum(["prompt", "function", "agent", "remote_eval"])
            .optional(),
          sourceType: z
            .enum(["inline", "saved_prompt", "function", "remote"])
            .optional(),
          hasMetadata: z.boolean().optional(),
        }),
      )
      .optional(),
    // Scorer details array
    scorerDetails: z
      .array(
        z.object({
          scorerId: z.string().optional(),
          scorerName: z.string(),
          scorerType: z.enum(["global", "custom"]).optional(),
          hasMetadata: z.boolean().optional(),
        }),
      )
      .optional(),
    // Additional metadata
    sourcePage: z.string().optional(),
    // Source tracking
    source: z.literal("server"),
  }),
});

export const checkoutStartEventSchema = baseServerEventSchema.extend({
  event: z.literal("checkoutStart"),
  properties: z.object({
    orgId: z.string(),
    orgName: z.string(),
    flowId: z.string().optional(), // Optional for server-side events
    purchasePlanSlug: z.string().optional(),
    purchasePlanId: z.string().optional(),
    sourcePage: z.string().optional(),
  }),
});

export const checkoutSuccessEventSchema = baseServerEventSchema.extend({
  event: z.literal("checkoutSuccess"),
  properties: z.object({
    orgId: z.string(),
    orgName: z.string(),
    flowId: z.string(),
    purchasePlanSlug: z.string().optional(),
    purchasePlanId: z.string().optional(),
    couponCode: z.string().optional(),
    setupIntentId: z.string().optional(),
    orbCustomerId: z.string().optional(),
    sourceType: z.string().optional(),
  }),
});

// Add more event schemas here as needed
export const serverEventSchemas = {
  playgroundRun: playgroundRunEventSchema,
  playgroundCreate: playgroundCreateEventSchema,
  playgroundDelete: playgroundDeleteEventSchema,
  experimentCreateServer: experimentCreateEventSchema,
  checkoutStart: checkoutStartEventSchema,
  checkoutSuccess: checkoutSuccessEventSchema,
} as const;

type ServerEventSchemas = typeof serverEventSchemas;
export type ServerEventName = keyof ServerEventSchemas;
export type ServerEventProps<K extends ServerEventName> = z.infer<
  ServerEventSchemas[K]
>;
export type ServerEventProperties<K extends ServerEventName> = z.infer<
  ServerEventSchemas[K]
>["properties"];

/**
 * Server-side analytics logging utility
 * Provides a type-safe way to log server-side events with proper validation
 */
export class ServerAnalytics {
  private authLookup?: AuthLookup;

  constructor(authLookup?: AuthLookup) {
    this.authLookup = authLookup;
  }

  /**
   * Log a server-side event with proper validation and error handling
   */
  async track<K extends ServerEventName>(
    eventName: K,
    properties: ServerEventProperties<K>,
    options?: {
      authLookup?: AuthLookup;
      userId?: string;
      orgId?: string;
    },
  ): Promise<void> {
    try {
      // Get auth info if not provided
      const authLookup =
        options?.authLookup ??
        this.authLookup ??
        (await getServerSessionAuthLookup());

      if (!authLookup?.user_id) {
        console.warn(
          "ServerAnalytics: No user ID available for event tracking",
          { eventName },
        );
        return;
      }

      // Construct the full event object
      const eventData = {
        userId: options?.userId ?? authLookup.user_id,
        orgId: options?.orgId ?? authLookup.org_id ?? undefined,
        timestamp: new Date().toISOString(),
        source: "server" as const,
        event: eventName,
        properties,
      };

      // Validate the event data
      const schema = serverEventSchemas[eventName];
      const validationResult = schema.safeParse(eventData);

      if (!validationResult.success) {
        console.error("ServerAnalytics: Invalid event data", {
          eventName,
          errors: validationResult.error.errors,
          data: eventData,
        });
        return;
      }

      // Extract orgId from properties if it exists
      const propertiesWithOrgId = { ...eventData.properties };
      const propertiesOrgId =
        "orgId" in propertiesWithOrgId ? propertiesWithOrgId.orgId : undefined;

      // Track the event via Segment

      await trackSegmentEvent({
        userId: eventData.userId,
        event: eventName,
        properties: {
          ...propertiesWithOrgId,
          orgId: propertiesOrgId ?? eventData.orgId,
          source: eventData.source,
          timestamp: eventData.timestamp,
        },
      });
    } catch (error) {
      // Don't let analytics errors break the main functionality
      console.error("ServerAnalytics: Failed to track event", {
        eventName,
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }
}

/**
 * Create a ServerAnalytics instance with the current auth context
 */
export async function createServerAnalytics(
  authLookup?: AuthLookup,
): Promise<ServerAnalytics> {
  const resolvedAuthLookup = authLookup ?? (await getServerSessionAuthLookup());
  return new ServerAnalytics(resolvedAuthLookup);
}

/**
 * Convenience function for one-off event tracking
 */
export async function trackServerEvent<K extends ServerEventName>(
  eventName: K,
  properties: ServerEventProperties<K>,
  options?: {
    authLookup?: AuthLookup;
    userId?: string;
    orgId?: string;
  },
): Promise<void> {
  const analytics = await createServerAnalytics(options?.authLookup);
  return analytics.track(eventName, properties, options);
}
