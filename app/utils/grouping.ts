import { isObject } from "#/utils/object";
import { deserializePlainStringAsJSON } from "#/utils/object";
import { getObjValueByPath } from "braintrust/util";
import { getDiffRight } from "./diffs/diff-objects";
import { parseTags } from "./parse";
import {
  type BtqlQueryBuilder,
  useBtqlQueryBuilder,
} from "./btql/use-query-builder";
import {
  fetchBtql,
  type FetchBtqlOptions,
  fetchBtqlPaginated,
  useFetchBtqlOptions,
  useIsRootBtqlSnippet,
} from "./btql/btql";
import { z } from "zod";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { doubleQuote } from "./sql-utils";
import { useEffect, useMemo } from "react";
import { useActiveRowAndSpan } from "#/ui/query-parameters";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function extractGroupValue(row?: any, groupingPath?: string[]) {
  if (!groupingPath || groupingPath.length === 0) {
    return;
  }

  switch (groupingPath[0]) {
    case "metadata": {
      const metadata = getDiffRight(row?.metadata);
      const parsedMetadata = isObject(metadata)
        ? metadata
        : deserializePlainStringAsJSON(
            typeof metadata === "string" ? metadata : "{}",
          )?.value;

      return groupingPath.length > 0
        ? getObjValueByPath({ metadata: parsedMetadata }, groupingPath)
        : undefined;
    }
    case "tags": {
      const tags = parseTags(row?.tags);
      return tags?.includes(groupingPath[1]) ? groupingPath[1] : undefined;
    }
    default: {
      return undefined;
    }
  }
}

export type GroupingData = {
  path: string[];
  value: unknown;
};

/**
 * Parse grouping for associated traces in the table (aka threads/agents)
 * @param row
 * @param grouping JSON stringified array or custom column field name
 * @returns
 */
export function parseGroupForRow(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  row: any,
  grouping: string | null | undefined,
): GroupingData | undefined {
  const groupingPath = parseGroupingStr(grouping);
  const value = extractGroupValue(row, groupingPath);

  return value
    ? {
        path: groupingPath,
        value,
      }
    : undefined;
}

function parseGroupingStr(grouping: string | null | undefined) {
  let groupingPath: string[] = [];
  try {
    groupingPath = JSON.parse(grouping ?? "");
  } catch {
    if (grouping) {
      groupingPath = [grouping];
    }
  }

  return groupingPath;
}

export function useGroupedRootSpanIds({
  objectType,
  objectId,
  groupingStr,
  staleTime,
}: {
  objectType: string | null | undefined;
  objectId: string | null | undefined;
  groupingStr: string | null | undefined;
  staleTime?: number;
}) {
  const [{ r: activeRowId }] = useActiveRowAndSpan();

  const groupingPath = useMemo(
    () => parseGroupingStr(groupingStr),
    [groupingStr],
  );
  const builder = useBtqlQueryBuilder({});
  const btqlOptions = useFetchBtqlOptions();
  const isRootBtqlSnippet = useIsRootBtqlSnippet();

  const hasValidObject = !!(objectType && objectId);
  const primaryRootSpanId = getDiffRight(activeRowId);
  const { data: groupValue } = useQuery({
    queryKey: rowMetadataValueQueryKey({
      objectType,
      objectId,
      rootSpanId: primaryRootSpanId,
      groupingPath,
    }),
    queryFn: async ({ signal }: { signal: AbortSignal }) => {
      const groupType = groupingPath?.[0];
      const metadataPath = groupingPath?.slice(1);
      const selectExpr =
        groupType === "metadata" && metadataPath?.length
          ? {
              btql: `metadata.${metadataPath?.map((p) => doubleQuote(p)).join(".")}`,
            }
          : groupType === "tags"
            ? {
                btql: "tags",
              }
            : {
                btql: "null",
              };
      const { data } = await fetchBtql({
        args: {
          query: {
            filter: builder.and(
              { btql: isRootBtqlSnippet },
              {
                op: "eq",
                left: { btql: "root_span_id" },
                right: { op: "literal", value: primaryRootSpanId },
              },
            ),
            from: builder.from(
              objectType ?? "",
              objectId ? [objectId] : [],
              "spans",
            ),
            select: [
              {
                alias: "value",
                expr: selectExpr,
              },
            ],
            limit: 1,
          },
          brainstoreRealtime: true,
        },
        ...btqlOptions,
        signal,
      });
      const value = data[0]?.value;
      if (!value || groupType === "metadata") {
        return value ?? null;
      }
      return extractGroupValue({ tags: value }, groupingPath) ?? null;
    },
    enabled: hasValidObject && !!primaryRootSpanId && groupingPath.length > 1,
    staleTime: Infinity,
    placeholderData: undefined,
  });

  const groupingData = useMemo(() => {
    if (
      !groupValue ||
      !groupingPath ||
      groupingPath.length === 0 ||
      groupingPath.length > 2
    ) {
      return undefined;
    }
    return {
      path: groupingPath,
      value: groupValue,
    };
  }, [groupValue, groupingPath]);

  const { data: groupedRowIdsData } = useQuery({
    queryKey: getGroupedSpanIdsQueryKey({
      objectType,
      objectId,
      groupingData,
    }),
    queryFn: getGroupedSpanIdsFn({
      builder,
      btqlOptions,
      isRootBtqlSnippet,
      objectType,
      objectId,
      groupingData,
    }),
    staleTime: staleTime ?? Infinity,
    throwOnError: false,
    placeholderData: undefined,
    enabled: hasValidObject && !!groupingData && !!groupingData.value,
  });

  const queryClient = useQueryClient();
  useEffect(() => {
    const groupingData = groupedRowIdsData?.groupingData;
    if (
      groupedRowIdsData?.data.length === 0 ||
      !groupingData?.value ||
      !groupingData?.path?.length
    ) {
      return;
    }

    groupedRowIdsData?.data.forEach((r) => {
      const queryKey = rowMetadataValueQueryKey({
        objectType,
        objectId,
        rootSpanId: r.root_span_id,
        groupingPath: groupingData?.path,
      });
      const existing = queryClient.getQueryData(queryKey);

      if (!existing) {
        queryClient.setQueryData(queryKey, groupingData.value);
      }
    });
  }, [objectType, objectId, groupedRowIdsData, queryClient]);

  return useMemo(
    () =>
      groupingStr
        ? new Set((groupedRowIdsData?.data ?? []).map((r) => r.root_span_id))
        : undefined,
    [groupingStr, groupedRowIdsData?.data],
  );
}

function rowMetadataValueQueryKey({
  objectType,
  objectId,
  rootSpanId,
  groupingPath,
}: {
  objectType: string | null | undefined;
  objectId: string | null | undefined;
  rootSpanId: string | null;
  groupingPath: string[];
}) {
  return ["rowMetadataValue", objectType, objectId, rootSpanId, groupingPath];
}

export const idSchema = z.object({
  id: z.string(),
  root_span_id: z.string(),
  _pagination_key: z.string(),
});

export type RowData = z.infer<typeof idSchema>;

export function getGroupedSpanIdsQueryKey({
  objectType,
  objectId,
  groupingData,
}: {
  objectType: string | null | undefined;
  objectId: string | null | undefined;
  groupingData?: GroupingData;
}) {
  return ["groupedRootSpanIds", objectType, objectId, groupingData];
}

export function getGroupedSpanIdsFn({
  builder,
  btqlOptions,
  isRootBtqlSnippet,
  objectType,
  objectId,
  groupingData,
}: {
  builder: BtqlQueryBuilder;
  btqlOptions: FetchBtqlOptions;
  isRootBtqlSnippet: string;
  objectType: string | null | undefined;
  objectId: string | null | undefined;
  groupingData?: GroupingData;
}) {
  return async ({ signal }: { signal: AbortSignal }) => {
    const groupType = groupingData!.path[0];
    const groupingPath = groupingData!.path.slice(1);
    if (!groupingData?.value || !groupType) {
      console.warn("no grouping value or type");
      return { data: [], groupingData: undefined };
    }
    const result = await fetchBtqlPaginated(
      {
        args: {
          query: {
            filter: builder.and(
              { btql: isRootBtqlSnippet },
              groupType === "metadata"
                ? {
                    op: "eq",
                    left: builder.ident("metadata", ...(groupingPath ?? [])),
                    right: {
                      op: "literal",
                      value: `${groupingData!.value}`,
                    },
                  }
                : groupType === "tags"
                  ? {
                      op: "includes",
                      haystack: builder.ident("tags"),
                      needle: {
                        op: "literal",
                        value: `${groupingPath[0]}`,
                      },
                    }
                  : null,
            ),
            from: builder.from(
              objectType ?? "",
              objectId ? [objectId] : [],
              "spans",
            ),
            select: [
              {
                alias: "id",
                expr: { btql: "id" },
              },
              {
                alias: "root_span_id",
                expr: { btql: "root_span_id" },
              },
              {
                alias: "_pagination_key",
                expr: { btql: "_pagination_key" },
              },
            ],
          },
          brainstoreRealtime: true,
        },
        ...btqlOptions,
        schema: idSchema,
        signal,
      },
      100,
      10,
      false,
    );

    return {
      data: result?.data ?? [],
      groupingData,
    };
  };
}
