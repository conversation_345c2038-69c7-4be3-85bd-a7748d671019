import { type RowId, rowIdSchema } from "#/ui/query-parameters";
import { z } from "zod";
import {
  createDiffObjectSchema,
  DiffLeftField,
  type DiffObjectType,
  DiffRightField,
  getDiffFieldIndex,
  getDiffRight,
} from "./diffs/diff-objects";
import { isEmpty, isObject, TRANSACTION_ID_FIELD } from "braintrust/util";
import { SINGLETON_DATASET_ID } from "#/app/app/[org]/p/[project]/playgrounds/[playground]/playx/stream";
import { objectReferenceSchema } from "@braintrust/typespecs";

export type RowIdInfo = {
  id: string;
  rootSpanId: string;
  spanId: string;
  diffObject?: string | DiffObjectType<string> | null;
};

export function extractRowIdInfo(r: unknown): RowIdInfo | null {
  const rowIdInfoFields = extractRowIdInfoFields(r);
  if (!rowIdInfoFields) {
    return null;
  }
  const diffObject = extractRowIdDiffObject(r) ?? null;
  return { ...rowIdInfoFields, diffObject };
}

function extractRowIdInfoFields(
  r: unknown,
): Omit<RowIdInfo, "urlDiffObject"> | null {
  if (isEmpty(r) || !isObject(r)) {
    return null;
  }

  const id = getDiffRight(r.id);
  const rootSpanId = getDiffRight(r.root_span_id);
  const spanId = getDiffRight(r.span_id);

  return {
    id,
    rootSpanId,
    spanId,
  };
}

const layoutTypeEnum = z.enum(["list", "grid"]);
type LayoutType = z.infer<typeof layoutTypeEnum>;
const newBaseQueryRowSchema = z.object({
  root_span_id: rowIdSchema,
  __bt_internal: z
    .object({
      _meta: z
        .object({
          diffModeEnabled: z.boolean(),
          layoutType: layoutTypeEnum,
          isPlayground: z.boolean().nullish(),
        })
        .nullish(),
    })
    .passthrough()
    .nullish(),
});

function extractRowIdDiffObject(r: unknown): RowId | null {
  if (isEmpty(r)) {
    return null;
  }
  const parsed = newBaseQueryRowSchema.safeParse(r);
  if (!parsed.success) {
    return null;
  }

  const data = parsed.data;
  if (!data.__bt_internal?._meta?.layoutType) {
    return data.root_span_id;
  }
  const layoutType = data.__bt_internal._meta.layoutType;
  if (!data.__bt_internal?._meta?.isPlayground) {
    return layoutType === "list"
      ? data.root_span_id
      : (parseGridLayoutRowId(r, false)?.rowId ?? null);
  }

  return playgroundRowId(r, layoutType);
}

function playgroundRowId(data: unknown, layoutType: LayoutType) {
  const parsed = parseRowId(data, layoutType);
  if (!parsed) {
    return null;
  }
  const { rowId, originDataset, hasPlaygroundData, datasetId } = parsed;
  const result = {
    ...rowId,
    ...(datasetId === SINGLETON_DATASET_ID && hasPlaygroundData
      ? {
          originId: SINGLETON_DATASET_ID,
          originObjectId: SINGLETON_DATASET_ID,
          originXactId: "0",
        }
      : originDataset
        ? {
            originId: originDataset.id,
            originObjectId: originDataset.object_id,
            originXactId: originDataset[TRANSACTION_ID_FIELD],
          }
        : datasetId
          ? {
              objectType: "dataset",
              objectId: datasetId,
            }
          : undefined),
  };
  return result;
}

const diffObjectStringSchema = createDiffObjectSchema(z.string());

const listLayoutRowBaseSchema = z.object({
  id: diffObjectStringSchema,
  root_span_id: diffObjectStringSchema,
  dataset_id: diffObjectStringSchema,
  playground_row_id: diffObjectStringSchema.nullish(),
  origin: diffObjectStringSchema.nullish(),
});
const listLayoutRowSchema = newBaseQueryRowSchema.extend(
  listLayoutRowBaseSchema.shape,
);

function parseRowId(data: unknown, layoutType: LayoutType) {
  if (layoutType === "list") {
    return parseListLayoutRowId(data);
  }
  return parseGridLayoutRowId(data, true);
}

function parseListLayoutRowId(_data: unknown) {
  const parsedRow = listLayoutRowSchema.safeParse(_data);
  if (!parsedRow.success) {
    return null;
  }
  const data = parsedRow.data;

  // track playground_row_id instead of origin because the non-dataset case won't have an origin
  const hasPlaygroundData =
    data.playground_row_id &&
    Object.values(data.playground_row_id).some((id) => id);
  const originDataset = data.origin ? parseDiffObjectOrigin(data.origin) : null;
  const datasetId = getDiffRight(data.dataset_id);

  if (!hasPlaygroundData) {
    return {
      rowId: data.root_span_id,
      originDataset: null,
      hasPlaygroundData,
      datasetId,
    };
  }

  const rowId: Record<string, unknown> = {
    // Right now this is always true.
    // If we change it so that we can view comparisons in list mode without diff view,
    // then we need to change this we can check this value
    diffModeEnabled: `${!!data.__bt_internal?._meta?.diffModeEnabled}`,
  };
  for (const k of Object.keys(data.root_span_id)) {
    if (getDiffFieldIndex(k) == null) {
      continue;
    }
    if (data.playground_row_id?.[k] != null) {
      rowId[k] = data.playground_row_id[k];
    }
  }

  return {
    rowId,
    originDataset,
    hasPlaygroundData,
    datasetId,
  };
}

function parseDiffObjectOrigin(origin: DiffObjectType<string>) {
  for (const [k, v] of Object.entries(origin)) {
    if (getDiffFieldIndex(k) == null) {
      continue;
    }
    const parsed = parseOrigin(v);
    if (parsed?.object_type === "dataset") {
      return parsed;
    }
  }
  return null;
}

const gridLayoutRowBaseSchema = z.object({
  id: z.string(),
  root_span_id: z.string(),
  dataset_id: z.string().nullish(),
  playground_row_id: z.string().nullish(),
  origin: z.string().nullish(),
});
const gridLayoutRowSchema = newBaseQueryRowSchema.extend(
  gridLayoutRowBaseSchema.shape,
);
const gridLayoutComparisonRowSchema = z.object({
  data: gridLayoutRowBaseSchema,
});

function parseGridLayoutRowId(_data: unknown, isPlayground: boolean) {
  const parsedRow = gridLayoutRowSchema.safeParse(_data);
  if (!parsedRow.success) {
    return null;
  }
  const data = parsedRow.data;
  const rowDataEntries: [string, unknown][] = [
    ["diffModeEnabled", `${!!data.__bt_internal?._meta?.diffModeEnabled}`],
    [
      DiffRightField,
      {
        id:
          data.playground_row_id ??
          (isPlayground ? data.id : data.root_span_id),
        isPlaygroundRow: !!data.playground_row_id,
      },
    ],
  ];
  // track playground_row_id instead of origin because the non-dataset case won't have an origin
  let hasPlaygroundData = false;
  const primaryOrigin = parseOrigin(data.origin);
  let originDataset = null;
  if (primaryOrigin?.object_type === "dataset") {
    originDataset = primaryOrigin;
  }
  hasPlaygroundData = !!data.playground_row_id;
  for (const [k, d] of Object.entries(data.__bt_internal ?? {})) {
    const parsed = gridLayoutComparisonRowSchema.safeParse(d);
    if (!parsed.success || !parsed.data.data.root_span_id) {
      continue;
    }

    hasPlaygroundData ||= !!parsed.data.data.playground_row_id;

    const origin = parseOrigin(parsed.data.data?.origin);
    originDataset ??= origin?.object_type === "dataset" ? origin : null;

    rowDataEntries.push([
      k === "e2" ? DiffLeftField : k,
      {
        id:
          parsed.data.data.playground_row_id ??
          (isPlayground ? parsed.data.data.id : parsed.data.data.root_span_id),
        isPlaygroundRow: !!parsed.data.data.playground_row_id,
      },
    ]);
  }

  const rowId: DiffObjectType<string> = Object.fromEntries(
    rowDataEntries.flatMap(([k, v]) => {
      if (typeof v === "string") {
        return [[k, v]];
      }
      if (
        hasPlaygroundData &&
        v instanceof Object &&
        (!("isPlaygroundRow" in v) || !v.isPlaygroundRow)
      ) {
        return [];
      }
      if (v instanceof Object && "id" in v && typeof v.id === "string") {
        return [[k, v.id]];
      }
      return [];
    }),
  );
  return {
    rowId,
    originDataset,
    hasPlaygroundData,
    datasetId: data.dataset_id,
  };
}

function parseOrigin(origin?: string | null) {
  if (!origin) {
    return null;
  }
  try {
    return objectReferenceSchema.parse(JSON.parse(origin));
  } catch {
    return null;
  }
}
