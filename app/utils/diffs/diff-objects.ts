import { isObject, isEmpty } from "#/utils/object";
import { z } from "zod";

export const DiffRightField = "_bt_internal_right" as const;
export const DiffLeftField = "_bt_internal_left" as const;

const DiffFieldsEnum = z.enum([DiffRightField, DiffLeftField]);

export const createDiffObjectSchema = <T extends z.ZodType>(
  schema: T,
  // in trace view, DiffRightField for traces/spans are allowed to be undefined
  // in playground, DiffRightField is allowed to be undefined if the first prompt hasn't been run
  rightFieldRequired?: boolean,
) =>
  z
    .object({
      [DiffLeftField]: schema.nullish(),
      [DiffRightField]: rightFieldRequired
        ? schema.refine((val) => val !== undefined)
        : schema.nullish(),
    })
    // e3..eN
    .catchall(schema.nullish())
    .refine((val) =>
      Object.keys(val).some(
        (k) =>
          // check that at least one of the two fields exists
          // so that we don't parse random objects as valid
          getDiffFieldIndex(k) != null,
      ),
    );

export function getDiffFieldIndex(k: string) {
  if (k.startsWith("e")) {
    const index = Number(k.slice(1));
    return isNaN(index) ? null : index;
  }
  const index = DiffFieldsEnum.options.findIndex(
    (fieldName) => fieldName === k,
  );
  return index === -1 ? null : index;
}

export type DiffObjectType<T> = z.infer<
  ReturnType<typeof createDiffObjectSchema<z.ZodType<T>>>
>;

// Performance optimization to prevent recreating the schema for every isDiffObject invocation
// - not-insignificant render time savings for arrow table
const diffObjectSchema = createDiffObjectSchema(z.any());

export function isDiffObject<T>(row: unknown): row is DiffObjectType<T> {
  const parsed = diffObjectSchema.safeParse(row);
  return parsed.success;
}

export function isEmptyDiffObject<T>(row: unknown): row is DiffObjectType<T> {
  return (
    isDiffObject(row) &&
    isEmpty(row[DiffLeftField]) &&
    isEmpty(row[DiffRightField]) &&
    Object.values(row).every(isEmpty)
  );
}

// pick only the right side of all diff objects. left side is the comparison side
// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export function flattenDiffObjects(obj: any): any {
  if (!isObject(obj)) {
    return obj;
  }

  const flatObj = Object.fromEntries(
    Object.entries(obj).map(([k, v]) => {
      if (isDiffObject(v)) {
        return [k, v[DiffRightField]];
      }

      if (isObject(v)) {
        return [k, flattenDiffObjects(v)];
      }

      return [k, v];
    }),
  );

  return flatObj;
}

export function getDiffLeft(value?: unknown) {
  return isDiffObject(value) ? value[DiffLeftField] : value;
}

export function getDiffRight(value?: unknown) {
  return isDiffObject(value) ? value[DiffRightField] : value;
}

export function getDiffValue({
  value,
  index,
}: {
  value?: unknown;
  index: number;
}) {
  if (index === 0) {
    return getDiffRight(value);
  }
  if (index === 1) {
    return getDiffLeft(value);
  }

  return isDiffObject(value) ? value[`e${index + 1}`] : value;
}

export function diffKeynameForIndex(i: number, legacyNames?: boolean) {
  if (i <= 0 && legacyNames) return DiffRightField;
  if (i == 1 && legacyNames) return DiffLeftField;
  return `e${i + 1}`;
}

export function getOrderedDiffObjectValues<T>(obj: DiffObjectType<T>) {
  const values: T[] = [getDiffRight(obj), getDiffLeft(obj)];

  let index = 3;
  while (`e${index}` in obj) {
    values.push(obj[`e${index}`]!);
    index++;
  }

  return values.filter((v) => v != null);
}
