import { useMemo } from "react";
import { type DataObjectType } from "#/utils/btapi/btapi";
import {
  useInferObjectSchemaPaths,
  useInferRowData,
} from "#/ui/use-infer-object";
import { type NestedField } from "@braintrust/local/query";

export function useInferCustomColumnPaths({
  objectType,
  objectId,
  rowId,
}: {
  objectType: DataObjectType;
  objectId: string | null;
  rowId: string | null;
}) {
  const inferData = useInferRowData({
    objectType,
    objectId,
    rowId,
  });

  const inferSchemaPaths = useMemo(
    () => [
      "input",
      "expected",
      "metadata",
      ...(objectType !== "dataset" ? ["output"] : []),
    ],
    [objectType],
  );

  const inferSchemaData = useInferObjectSchemaPaths({
    objectType,
    objectId,
    rowId,
    paths: inferSchemaPaths,
  });

  return useMemo(() => {
    if (!inferSchemaData || !inferData) {
      return;
    }

    const processedPaths = processInferredPaths(
      inferSchemaData,
      inferData.data,
    );

    const paths = Array.from(processedPaths.values());
    const groupedFields = paths.reduce((acc: Record<string, string[][]>, d) => {
      const firstItem = d[0];
      if (!acc[firstItem]) {
        acc[firstItem] = [];
      }
      if (d.length > 1) {
        acc[firstItem].push(d.slice(1));
      }
      return acc;
    }, {});

    Object.keys(groupedFields).forEach((key) => {
      groupedFields[key].sort((a, b) => {
        return a.join(".").localeCompare(b.join("."));
      });
    });

    return groupedFields;
  }, [inferSchemaData, inferData]);
}

function processInferredPaths(
  inferSchemaData: NestedField[],
  inferData: Record<string, unknown>[],
) {
  const processedPaths = new Map<string, string[]>();

  for (const pathParts of inferSchemaData) {
    if (processedPaths.has(pathParts.join("."))) {
      continue;
    }
    for (const data of inferData) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      let current: any = data;
      const modifiedPath = [...pathParts];

      for (let i = 0; i < pathParts.length; i++) {
        if (!current || typeof current !== "object") {
          break;
        }
        const part = pathParts[i];
        if (Array.isArray(current)) {
          modifiedPath.splice(i, 0, "[0]");
        }
        current = current[part];
      }

      processedPaths.set(pathParts.join("."), modifiedPath);
    }
  }

  return processedPaths;
}
