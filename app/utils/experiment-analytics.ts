"use server";

import { trackServerEvent } from "#/utils/server-analytics";
import type { AuthLookup } from "#/utils/server-util";

export type ExperimentCreateEvent = {
  experimentId: string;
  experimentName: string;
  entryPoint?: string;
  flowId: string;
  projectId: string;
  projectName: string;
  orgId?: string;
  mode?: "playground" | "single";
  datasetId?: string;
  datasetName?: string;
  datasetRowCount?: number;
  sourcePlaygroundId?: string;
  sourceScorerId?: string;
  // Method tracking
  method: "ui" | "sdk";
  // Outcome tracking
  outcome: "success" | "error" | "cancelled";
  errorMessage?: string;
  // Minimal experiment summaries for dedup per flow
  experimentDetails?: Array<{
    id: string;
    name: string;
    taskId?: string;
    sourceElementId?: string;
    taskType?: "prompt" | "function" | "agent" | "remote_eval";
    sourceType?: "inline" | "saved_prompt" | "function" | "remote";
    hasMetadata?: boolean;
  }>;
  scorerDetails?: Array<{
    scorerId?: string;
    scorerName: string;
    scorerType?: "global" | "custom";
    hasMetadata?: boolean;
  }>;
  sourcePage?: string;
  source: "server";
};

/**
 * Track an experiment creation event
 * This function can be called from server actions or API routes
 */
export async function trackExperimentCreate(
  event: ExperimentCreateEvent,
  authLookupRaw?: AuthLookup,
): Promise<{ success: true }> {
  await trackServerEvent("experimentCreateServer", event, {
    authLookup: authLookupRaw,
  });
  return { success: true };
}
