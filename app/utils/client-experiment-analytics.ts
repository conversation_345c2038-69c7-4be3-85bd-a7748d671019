"use client";

import { useAppAnalytics } from "#/ui/analytics/segment-analytics";

export type ExperimentCreateEvent = {
  experimentId: string;
  experimentName: string;
  entryPoint?: string;
  flowId: string;
  projectId: string;
  projectName: string;
  orgId?: string;
  mode?: "playground" | "single";
  datasetId?: string;
  datasetName?: string;
  // Method tracking
  method: "ui" | "sdk";
  // Outcome tracking
  outcome: "success" | "error" | "cancelled";
  errorMessage?: string;
  // Minimal experiment summaries for dedup/aggregation across a flow
  experimentDetails?: Array<{
    id: string;
    name: string;
    taskId?: string;
    sourceElementId?: string;
    taskType?: "prompt" | "function" | "agent" | "remote_eval";
    sourceType?: "inline" | "saved_prompt" | "function" | "remote";
    hasMetadata?: boolean;
  }>;
  // Scorer details array
  scorerDetails?: Array<{
    scorerId?: string;
    scorerName: string;
    scorerType?: "global" | "custom";
    hasMetadata?: boolean;
  }>;
  // Additional metadata
  sourcePage?: string;
  modelProvider?: string;
  model?: string;
  temperature?: number;
  language?: string;
  source: "web";
};

/**
 * Hook to track experiment creation events on the client side
 * This matches the exact schema of the server-side experimentCreate event
 */
export function useExperimentAnalytics() {
  const { track } = useAppAnalytics();

  const trackExperimentCreate = (event: ExperimentCreateEvent) => {
    track("experimentCreate", event);
  };

  return {
    trackExperimentCreate,
  };
}
