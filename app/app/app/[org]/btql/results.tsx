import {
  useMemo,
  useCallback,
  type SetStateAction,
  type Dispatch,
  memo,
} from "react";
import { type RowData, Table } from "#/ui/arrow-table";
import { useViewStates } from "#/utils/view/use-view";
import prettyBytes from "pretty-bytes";
import { type BTQLResponse, parseBtqlSchema } from "#/utils/btql/btql";
import { DataType, type Field, type Schema } from "apache-arrow";
import { z } from "zod";
import { serializeJSONWithPlainString } from "#/utils/object";
import { relativeTimeMs } from "#/ui/date";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { Button } from "#/ui/button";
import { downloadAsCSV, downloadAsJSON } from "#/utils/download";
import { CircleArrowDown, FileJson, FileSpreadsheet } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { getDiffRight } from "#/utils/diffs/diff-objects";

function QueryResultsComponent({
  scrollContainerRef,
  rawResult,
  error,
  lastQueryMs,
  setSelectedRow,
  activeRow,
  setActiveRow,
  scrollMarginRef,
  chartButton,
  fixLoopButton,
}: {
  rawResult: BTQLResponse<Record<string, unknown>> | null;
  error: string | null;
  lastQueryMs: number | null;
  scrollContainerRef: React.RefObject<HTMLDivElement | null>;
  setSelectedRow: Dispatch<
    SetStateAction<{
      row: Record<string, unknown> | null;
      index?: number;
    } | null>
  >;
  activeRow: string | null;
  setActiveRow: Dispatch<SetStateAction<string | null>>;
  scrollMarginRef: React.RefObject<HTMLDivElement | null>;
  chartButton: React.ReactNode;
  fixLoopButton?: React.ReactNode;
}) {
  const viewProps = useViewStates({
    pageIdentifier: "btql",
    viewParams: undefined,
    clauseChecker: null,
  });

  const tableState = useMemo<{
    data: RowData[];
    fields: Field[] | undefined;
  }>(() => {
    if (!rawResult) {
      return {
        data: [],
        fields: undefined,
      };
    }

    // If the data does not have an id field, then add one
    const fullArrowSchema = parseBtqlSchema(null /*ignored*/, rawResult.schema);
    return {
      data: postProcessData(rawResult.data, fullArrowSchema),
      fields: fullArrowSchema.fields,
    };
  }, [rawResult]);

  const byteEstimate = useMemo(() => {
    if (!rawResult) {
      return null;
    }
    return JSON.stringify(rawResult.data).length;
  }, [rawResult]);

  const downloadResultsAsJSON = useCallback(() => {
    if (!rawResult || !rawResult.data.length) {
      return;
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const filename = `btql-results-${timestamp}`;
    downloadAsJSON(filename, rawResult.data);
  }, [rawResult]);

  const downloadResultsAsCSV = useCallback(() => {
    if (!rawResult || !rawResult.data.length) {
      return;
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const filename = `btql-results-${timestamp}`;
    downloadAsCSV(filename, rawResult.data);
  }, [rawResult]);

  const errorUI = error ? (
    <div className="group relative flex flex-1 items-center gap-2">
      <span>{error}</span>
      {fixLoopButton && fixLoopButton}
    </div>
  ) : null;

  const handleRowClick = useCallback(
    (row: RowData) => {
      if (!rawResult) return;
      const rowId = getDiffRight(row.id);

      const index = rawResult.data.findIndex((d) => d.id === row.id);
      if (index > -1) {
        setSelectedRow({ row: rawResult.data[index], index });
      } else {
        const index = rowId !== null ? parseInt(rowId, 10) : undefined;
        setSelectedRow({
          row:
            typeof index === "number" ? (rawResult.data[index] ?? null) : null,
          index,
        });
      }
      setActiveRow(rowId);
    },
    [rawResult, setSelectedRow, setActiveRow],
  );

  const hasError = Boolean(error);
  const noResult = !Boolean(rawResult);
  if (noResult && !hasError) {
    return (
      <TableEmptyState
        label="Run query to view results"
        labelClassName="text-sm"
        className="mt-3 flex-1 py-8"
      />
    );
  }

  const emptyResults = rawResult?.data.length === 0;
  if (emptyResults && !hasError) {
    return (
      <TableEmptyState
        label="No results found"
        labelClassName="text-sm"
        className="mt-3 flex-1 py-8"
      />
    );
  }

  return (
    <Table
      {...tableState}
      viewProps={viewProps}
      scrollContainerRef={scrollContainerRef}
      isSortable={false}
      rowEvents={{
        onClick: (row) => () => handleRowClick(row.original),
      }}
      openRowId={activeRow}
      scrollMarginRef={scrollMarginRef}
      stickyBarClassName="w-[calc(100cqw+1.5rem)]"
      extraRightControls={
        <div className="flex h-8 flex-1 items-center justify-end gap-4 text-xs text-primary-500">
          {rawResult && (
            <div>{rawResult.data.length.toLocaleString()} rows</div>
          )}
          {byteEstimate !== null && <div>{prettyBytes(byteEstimate)}</div>}
          {lastQueryMs && <div>{relativeTimeMs(lastQueryMs)}</div>}
          {chartButton}
          {rawResult && rawResult.data.length > 0 && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  size="xs"
                  variant="ghost"
                  Icon={CircleArrowDown}
                  className="text-primary-600"
                  isDropdown
                >
                  Download
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onSelect={downloadResultsAsJSON}>
                  <FileJson className="size-3 flex-none" />
                  Download as JSON
                </DropdownMenuItem>
                <DropdownMenuItem onSelect={downloadResultsAsCSV}>
                  <FileSpreadsheet className="size-3 flex-none" />
                  Download as CSV
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      }
      tableType="detailed"
      error={errorUI}
      skipErrorReporting
    />
  );
}

export const QueryResults = memo(QueryResultsComponent);
QueryResults.displayName = "QueryResults";

function postProcessData(
  data: Record<string, unknown>[],
  schema: Schema,
): RowData[] {
  return data.map((d, index) => {
    const id = z.string().safeParse(d.id);
    return {
      ...Object.fromEntries(
        Object.entries(d).map(([k, v]) => {
          const fieldSchema = schema.fields.find((f) => f.name === k);
          const val = DataType.isUtf8(fieldSchema)
            ? serializeJSONWithPlainString(v)
            : v;
          return [k, val];
        }),
      ),
      id: id.success
        ? id.data
        : d.id
          ? serializeJSONWithPlainString(d.id)
          : `${index}`,
    };
  });
}
