"use client";

import { useAnalytics } from "#/ui/use-analytics";
import { useOrg } from "#/utils/user";
import { BodyWrapper } from "#/app/app/body-wrapper";
import { CheckOrg } from "../../../clientlayout";
import { useIsClient } from "#/utils/use-is-client";
import { TableSkeleton } from "#/ui/table/table-skeleton";
import {
  OptimizationProvider,
  useOptimizationContext,
} from "#/utils/optimization/provider";
import { GlobalChatProvider } from "#/ui/optimization/global-chat-provider";
import { PromptTextArea } from "#/ui/optimization/prompt-text-area";
import { useCallback, useEffect, useRef, useState } from "react";
import { useGlobalChat } from "#/ui/optimization/use-global-chat-context";
import { Messages } from "#/ui/optimization/messages";
import { useUpsellContext } from "#/app/playground/upsell-dialog";
import { But<PERSON> } from "#/ui/button";
import { CopySlash, PlusIcon, TextSearch, TrendingUp } from "lucide-react";
import { SessionsHistoryDropdown } from "#/ui/optimization/sessions-history-dropdown";
import { motion } from "motion/react";
import { cn } from "#/utils/classnames";
import { useFeatureFlags } from "#/lib/feature-flags";
import { useRouter } from "next/navigation";
import { getOrgLink } from "../../../getOrgLink";
import { AccessFailed } from "#/ui/access-failed";
import { isEmpty } from "#/utils/object";
import { useConfirmationContext } from "#/ui/optimization/confirmation-context";

export interface Params {
  org: string;
  project: string;
}

// Inner component that uses the context
function LoopChatContent({}: {}) {
  const {
    createNewSession,
    chatSessions,
    currentChatSessionId,
    setCurrentChatSessionId,
    currentSessionMessages,
    currentSessionContextObjects,
    model,
    currentSessionTools,
    setCurrentSessionContextObjects,
    setModel,
    setCurrentSessionTools,
    currentSessionUserMessage,
    setCurrentSessionUserMessage,
    setCurrentSessionHasInteractedWithContextObjects,
    currentSessionIsActive,
    handleSendMessage,
    handleAbort,
    deleteSession,
    implementedTools,
    pageKey,
    chat,
    activeSessionId,
  } = useGlobalChat();
  const { getConfirmationByToolCallId, isConfirming } =
    useConfirmationContext();

  const {
    allowRunningWithoutConsent,
    setAllowRunningWithoutConsent,
    timeRangeSettings,
    setTimeRangeSettings,
  } = useOptimizationContext();
  const { onUpsell } = useUpsellContext();

  const [shouldAutoScroll, setShouldAutoScroll] = useState(true);

  const contentRef = useRef<HTMLDivElement | null>(null);
  const textAreaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    if (contentRef.current && shouldAutoScroll) {
      contentRef.current.scrollTop = contentRef.current.scrollHeight;
    }
  }, [currentSessionMessages, shouldAutoScroll]);

  const handleScroll = useCallback(() => {
    if (contentRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = contentRef.current;
      //4px threshold for determining if user is at the bottom.
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 4;
      setShouldAutoScroll(isAtBottom);
    }
  }, []);

  const handleCreateNewSession = useCallback(() => {
    if (onUpsell) {
      onUpsell();
      return;
    }

    const emptySession = chatSessions.sessions.find(
      (session) =>
        session.messages.length === 0 && session.id !== currentChatSessionId,
    );

    if (emptySession) {
      setCurrentChatSessionId(emptySession.id);
    } else if (currentSessionMessages.length > 0) {
      createNewSession();
    }

    if (textAreaRef.current) {
      textAreaRef.current.focus();
    }
  }, [
    chatSessions.sessions,
    currentChatSessionId,
    currentSessionMessages.length,
    setCurrentChatSessionId,
    createNewSession,
    onUpsell,
  ]);

  return (
    <BodyWrapper innerClassName="flex flex-col items-center">
      <motion.div
        className={cn(
          "flex w-full items-center gap-2 p-4",
          currentSessionMessages.length === 0 && "max-w-2xl px-2 pt-6 pb-0",
        )}
      >
        <div className="ml-auto flex items-center gap-1">
          <SessionsHistoryDropdown
            chatSessions={chatSessions}
            setCurrentChatSessionId={setCurrentChatSessionId}
            deleteSession={deleteSession}
            currentChatSessionId={currentChatSessionId}
            isConfirming={isConfirming}
            className="border-none bg-background hover:bg-primary-100"
            size="full"
            activeSessionId={activeSessionId}
          />
          {currentSessionMessages.length > 0 && (
            <Button
              variant="ghost"
              size="sm"
              Icon={PlusIcon}
              onClick={handleCreateNewSession}
              iconClassName="size-4"
            >
              New
            </Button>
          )}
        </div>
      </motion.div>
      {currentSessionMessages.length > 0 && (
        <div
          ref={contentRef}
          className={
            "mx-auto -mt-6 flex w-full max-w-2xl flex-1 flex-col gap-1 overflow-y-auto px-3 py-2 pb-10 text-xs"
          }
          onScroll={handleScroll}
        >
          <Messages
            parsedMessages={currentSessionMessages}
            setAllowRunningWithoutConsent={setAllowRunningWithoutConsent}
            allowRunningWithoutConsent={allowRunningWithoutConsent}
            handleCreateNewSession={handleCreateNewSession}
            pageKey={pageKey}
            chat={chat}
            size="full"
            handleSendMessage={handleSendMessage}
            getConfirmationByToolCallId={getConfirmationByToolCallId}
          />
        </div>
      )}
      <motion.div
        layout="position"
        transition={{ duration: 0.2, ease: "easeInOut" }}
        initial={{ opacity: 0.8, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className={cn(
          "mx-auto w-full max-w-2xl",
          currentSessionMessages.length === 0 && "pt-6",
        )}
      >
        <PromptTextArea
          textAreaRef={textAreaRef}
          allowRunningWithoutConsent={allowRunningWithoutConsent}
          setAllowRunningWithoutConsent={setAllowRunningWithoutConsent}
          currentModel={model}
          setCurrentModel={setModel}
          currentTool={currentSessionTools}
          setCurrentTool={setCurrentSessionTools}
          contextObjects={currentSessionContextObjects}
          setContextObject={setCurrentSessionContextObjects}
          userMessage={currentSessionUserMessage}
          setUserMessage={setCurrentSessionUserMessage}
          setCurrentSessionHasInteractedWithContextObjects={
            setCurrentSessionHasInteractedWithContextObjects
          }
          isChatActive={currentSessionIsActive}
          isConfirming={isConfirming}
          handleSendMessage={handleSendMessage}
          handleAbort={handleAbort}
          implementedTools={implementedTools}
          setShouldAutoScroll={setShouldAutoScroll}
          pageKey={pageKey}
          hasMultipleSelectedExperiments={false}
          timeRangeSettings={timeRangeSettings}
          setTimeRangeSettings={setTimeRangeSettings}
          minRows={2}
          size="full"
        />
      </motion.div>
      {currentSessionMessages.length === 0 && (
        <div className="mx-auto flex w-full max-w-2xl items-center gap-2 p-2">
          <Button
            variant="border"
            size="xs"
            Icon={TextSearch}
            onClick={() => {
              handleSendMessage(
                {
                  id: crypto.randomUUID(),
                  type: "user_message",
                  message: "Find common usecases from the logs",
                },
                {
                  clearContextObjects: true,
                  clearUserMessage: true,
                },
              );
            }}
            iconClassName="size-4"
            className="flex-1 gap-2"
          >
            Find common usecases
          </Button>
          <Button
            variant="border"
            size="xs"
            Icon={TrendingUp}
            onClick={() => {
              handleSendMessage(
                {
                  id: crypto.randomUUID(),
                  type: "user_message",
                  message: "Trending user behaviors from the logs",
                },
                {
                  clearContextObjects: true,
                  clearUserMessage: true,
                },
              );
            }}
            iconClassName="size-4"
            className="flex-1 gap-2"
          >
            Trending user behaviors
          </Button>
          <Button
            variant="border"
            size="xs"
            Icon={CopySlash}
            onClick={() => {
              handleSendMessage({
                id: crypto.randomUUID(),
                type: "user_message",
                message: "Find common failures/errors from the logs",
              });
            }}
            iconClassName="size-4"
            className="flex-1 gap-2"
          >
            Common failures/errors
          </Button>
        </div>
      )}
    </BodyWrapper>
  );
}

export default function ClientPage({
  params,
  projectId,
}: {
  params: Params;
  projectId: string;
}) {
  const { name: orgName } = useOrg();
  const router = useRouter();
  useAnalytics({
    page: {
      category: "Loop explorer",
    },
  });
  const { flags } = useFeatureFlags();

  const isClient = useIsClient();
  useEffect(() => {
    if (isClient && !flags.skellyLoop) {
      router.replace(getOrgLink({ orgName }));
    }
  }, [isClient, flags.skellyLoop, orgName, router]);

  if (!isClient) {
    return <TableSkeleton />;
  }

  if (!flags.skellyLoop) {
    return null;
  }

  if (isEmpty(projectId)) {
    return <AccessFailed objectType="Project" objectName={params.project} />;
  }

  return (
    <CheckOrg params={{ org: orgName }}>
      <OptimizationProvider queryProjectId={projectId}>
        <GlobalChatProvider>
          <LoopChatContent />
        </GlobalChatProvider>
      </OptimizationProvider>
    </CheckOrg>
  );
}
