"use client";

import {
  type SetStateAction,
  type Dispatch,
  use<PERSON>allback,
  useContext,
  useMemo,
  useRef,
  useState,
} from "react";

import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { AccessFailed } from "#/ui/access-failed";
import { useAnalytics } from "#/ui/use-analytics";
import { useAppAnalytics } from "#/ui/analytics/segment-analytics";
import { But<PERSON>, buttonVariants } from "#/ui/button";
import { ConfirmationDialog } from "#/ui/dialogs/confirmation";
import { useCreateDatasetDialog } from "#/ui/dialogs/create-dataset";
import { MainContentWrapper } from "#/ui/layout/main-content-wrapper";
import { Loading } from "#/ui/loading";
import { type SavingState, SavingStatus } from "#/ui/saving";
import {
  CancelSelectionButton,
  SelectionBarButton,
} from "#/ui/table/selection-bar";
import { type TraceViewParams } from "#/ui/trace/trace";
import { computeDisplayPaths } from "#/utils/display-paths";
import {
  DatasetIdField,
  OriginField,
  ProjectIdField,
  TagsField,
  TransactionIdField,
  useDBQuery,
} from "#/utils/duckdb";
import {
  type OtherObjectInserterInfo,
  useOtherObjectInserter,
} from "#/utils/other-object-inserter";
import { pluralizeWithCount } from "#/utils/plurals";
import { type DuckDBJSONStruct, DuckDBTypeHints } from "#/utils/schema";
import { type Table as ArrowTable, type TypeMap } from "apache-arrow";
import { useEntityContextActions } from "../../../useEntityContextActions";
import { EntityContextMenu } from "#/ui/entity-context-menu";
import { DatasetMetadata } from "../../experiments/[experiment]/DatasetMetadata";
import { getDatasetLink, getDatasetsLink } from "./getDatasetLink";
import { ImportWizard } from "./importWizard";
import { objectCopyName } from "#/utils/metadata";
import { IS_MERGE_FIELD } from "braintrust/util";
import {
  datasetSchema as datasetZodSchema,
  type ViewType,
} from "@braintrust/typespecs";
import { TagBulkEditor } from "#/ui/trace/tags";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "#/ui/resizable";
import { usePanelSize } from "#/ui/use-panel-size";
import Footer from "#/ui/landing/footer";
import {
  type UnionedPaginatedObjectViewerDataComponents,
  usePaginatedObjectViewerDataComponents,
  usePaginatedObjectViewerVizComponents,
} from "#/ui/paginated-object-viewer";
import { useBtql } from "#/utils/btql/btql";
import { doubleQuote, singleQuote } from "#/utils/sql-utils";
import { type ZodError, z } from "zod";
import Link from "next/link";
import {
  Beaker,
  Copy,
  Database,
  Plus,
  Shapes,
  Trash,
  Upload,
} from "lucide-react";
import { isEmpty } from "#/utils/object";
import { type DatasetInfo, type fetchDatasetInfo } from "./actions";
import { toast } from "sonner";
import { newId } from "braintrust";
import { cn } from "#/utils/classnames";
import { useQueryFunc } from "#/utils/react-query";
import { UpdateableDataTextEditor } from "#/ui/data-text-editor";
import TextArea from "#/ui/text-area";
import { zodErrorToString } from "#/utils/validation";
import { ReviewButton } from "../../review-button";
import { TableRowHeightToggle } from "#/ui/table-row-height-toggle";
import { useActiveRowAndSpan } from "#/ui/query-parameters";
import { useTraceFullscreen } from "#/ui/trace/use-trace-fullscreen";
import { decodeURIComponentPatched } from "#/utils/url";
import { type VizQueryProps } from "#/ui/viz-query";
import {
  type UpdateValueFn,
  type ArrowTableUIProps,
  type RowData,
} from "#/ui/arrow-table";
import { useInsertRows } from "./useInsertRows";
import {
  useClauseChecker,
  type UseClauseCheckerProps,
} from "#/utils/search-btql";
import {
  useViewStates,
  type ViewProps,
  type ViewParams,
} from "#/utils/view/use-view";
import { useGetRowsForExport } from "#/utils/data-object";
import { UploadContext, type ImportProgressType } from "#/ui/upload-provider";
import { LibraryItemLinks } from "../../library/library-item-links";
import useEvent from "react-use-event-hook";
import { type UpdateRowFn } from "#/utils/mutable-object";
import { type RowIdInfo } from "#/utils/row-id";
import { DatasetDropdown } from "#/ui/dataset-dropdown";
import { SpanField } from "#/ui/trace/use-span-field-order.tsx";
import {
  BodyWrapper,
  HEIGHT_WITH_DOUBLE_TOP_OFFSET,
  HEIGHT_WITH_TOP_OFFSET,
} from "#/app/app/body-wrapper";
import { useSummaryPaginatedObjectViewerDataComponents } from "#/ui/summary-paginated-object-viewer";
import { useFeatureFlags } from "#/lib/feature-flags";
import {
  BUILT_IN_CUSTOM_COLUMNS,
  useCustomColumns,
} from "#/utils/custom-columns/use-custom-columns";
import { AssignBulkEditor } from "#/ui/trace/assign";
import {
  OptimizationChat,
  useIsLoopEnabled,
} from "#/ui/optimization/optimization-chat";
import { GlobalChatProvider } from "#/ui/optimization/global-chat-provider";
import { OptimizationProvider } from "#/utils/optimization/provider";
import { createDataset } from "./createDataset";
import { DockedChatSpacer } from "../../playgrounds/[playground]/docked-chat-spacer";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { GenerateRowsButton } from "../../playgrounds/[playground]/generate-rows-button";
import { type Permission } from "@braintrust/typespecs";
import { useRouter } from "next/navigation";
import { useOrg, useUser } from "#/utils/user";
import { useSessionToken } from "#/utils/auth/session-token";
import { useDefaultPromptData } from "#/ui/prompts/use-default-prompt-data";
import { createPromptSession } from "#/app/app/[org]/prompt/[prompt]/createPromptSession";
import { getPlaygroundLink } from "#/app/app/[org]/prompt/[prompt]/getPromptLink";
import { performUpsert } from "#/utils/duckdb";
import { newObjectName } from "#/utils/metadata";
import { CreateExperimentDialog } from "#/app/app/[org]/prompt/[prompt]/create-experiment-dialog";
import { useScorerFunctions } from "#/app/app/[org]/prompt/[prompt]/scorers/open";
import { ProjectPromptsDropdownProvider } from "../../playgrounds/prompts-dropdown";
import { ScorersDropdownProvider } from "../../playgrounds/scorers-dropdown";
import { useDownloadRowsMenu } from "#/ui/download";
import { useUploadDatasetDialog } from "#/app/app/[org]/prompt/[prompt]/upload-dataset-dialog";
import { useBtqlAutocompleteDataSources } from "#/app/app/[org]/btql/use-btql-autocomplete-data-sources";
import { useRowCount } from "#/ui/use-row-count";

const NEVER_VISIBLE_COLUMNS = new Set(["span_id"]);

export interface Params {
  org: string;
  project: string;
  dataset: string;
}

const traceViewParamArgs: Partial<TraceViewParams> = {
  editableFields: [SpanField.INPUT, SpanField.EXPECTED, SpanField.METADATA],
  hiddenFields: [SpanField.METRICS],
};

export default function ClientPage({
  params,
  datasetInfo: datasetInfoServer,
  defaultPanelLayout,
  permissions,
}: {
  params: Params;
  datasetInfo: DatasetInfo | null;
  defaultPanelLayout: DatasetPanelLayout;
  permissions: Permission[];
}) {
  const orgName = decodeURIComponentPatched(params.org);
  const projectName = decodeURIComponentPatched(params.project);
  const datasetName = decodeURIComponentPatched(params.dataset);
  const [savingState, setSavingState] = useState<SavingState>("none");

  const { projectId } = useContext(ProjectContext);

  const {
    data: dataset,
    isLoading: datasetIsLoading,
    invalidate,
  } = useQueryFunc<typeof fetchDatasetInfo>({
    fName: "fetchDatasetInfo",
    args: {
      org_name: orgName,
      project_name: projectName,
      dataset_name: datasetName,
    },
    serverData: datasetInfoServer,
  });

  useAnalytics({
    page: dataset && {
      category: "dataset",
      props: {
        project_id: dataset.project_id,
        dataset_id: dataset.id,
      },
    },
  });

  const objectType = "dataset" as const;
  const objectId = dataset?.id ?? null;
  const viewType: ViewType | null = objectType;
  const viewParams: ViewParams | undefined =
    projectId && viewType
      ? {
          objectType: "project",
          objectId: projectId,
          viewType,
        }
      : undefined;
  const pageIdentifier = objectType + "-" + objectId;

  const clauseCheckerProps = useClauseChecker("dataset", true);
  const viewProps = useViewStates({
    viewParams,
    clauseChecker: clauseCheckerProps.clauseChecker,
    pageIdentifier,
  });

  const props = {
    defaultPanelLayout,
    dataset,
    viewProps,
    viewParams,
    savingState,
    setSavingState,
    objectType,
    objectId,
    orgName,
    projectName,
    projectId,
    datasetName,
    invalidate,
    datasetIsLoading,
    permissions,
  };

  const {
    flags: { fastExperimentSummary, fastDatasetSummary },
  } = useFeatureFlags();

  return (
    <ProjectPromptsDropdownProvider>
      <ScorersDropdownProvider>
        {fastExperimentSummary && fastDatasetSummary ? (
          <SummaryDatasetViewer {...props} />
        ) : (
          <NonSummaryDatasetViewer
            {...props}
            clauseCheckerProps={clauseCheckerProps}
          />
        )}
      </ScorersDropdownProvider>
    </ProjectPromptsDropdownProvider>
  );
}

export type DatasetPanelLayout = {
  trace: number;
  main: number;
};

// Refactor the page into two options: with and without the summary viewer.

interface CommonDatasetViewerProps {
  defaultPanelLayout: DatasetPanelLayout;
  dataset: Awaited<ReturnType<typeof fetchDatasetInfo>>;
  viewProps: ViewProps;
  viewParams: ViewParams | undefined;
  savingState: SavingState;
  setSavingState: Dispatch<SetStateAction<SavingState>>;
  objectType: "dataset";
  objectId: string | null;
  orgName: string;
  projectName: string;
  projectId: string | null;
  datasetName: string;
  invalidate: () => Promise<void>; // invalidates dataset list
  datasetIsLoading: boolean;
  permissions: Permission[];
}

function NonSummaryDatasetViewer(
  props: CommonDatasetViewerProps & {
    clauseCheckerProps: UseClauseCheckerProps;
  },
) {
  const { clauseCheckerProps, ...commonProps } = props;
  const {
    dataset,
    viewProps,
    viewParams,
    setSavingState,
    objectType,
    objectId,
  } = commonProps;

  const [isExperimentDialogOpen, setIsExperimentDialogOpen] = useState(false);

  const paginatedObjectViewerDataComponents =
    usePaginatedObjectViewerDataComponents({
      objectType,
      objectId,
      objectName: dataset?.name ?? null,
      pageSize: 40,
      setSavingState,
      traceViewParamArgs,
      viewProps,
      viewParams,
      disableGrouping: true,
      useClauseCheckerProps: clauseCheckerProps,
      enableStarColumn: true,
    });

  return (
    <DatasetViewerInner
      paginatedObjectViewerDataComponents={paginatedObjectViewerDataComponents}
      typeHints={undefined}
      isSortable={true}
      isExperimentDialogOpen={isExperimentDialogOpen}
      setIsExperimentDialogOpen={setIsExperimentDialogOpen}
      {...commonProps}
    />
  );
}

function SummaryDatasetViewer(props: CommonDatasetViewerProps) {
  const { dataset, viewParams, setSavingState, objectType, objectId } = props;

  const [isExperimentDialogOpen, setIsExperimentDialogOpen] = useState(false);

  const customColumnState = useCustomColumns({
    scope: objectId
      ? {
          object_type: "dataset",
          object_id: objectId,
          subtype: "",
          variant: "dataset",
        }
      : undefined,
  });

  const typeHints = useMemo(() => {
    return {
      ...DuckDBTypeHints[objectType],
      ...Object.fromEntries(
        customColumnState?.customColumnDefinitions?.map((c) => [
          c.name,
          "JSON",
        ]) ?? [],
      ),
    };
  }, [objectType, customColumnState?.customColumnDefinitions]);

  const paginatedObjectViewerDataComponents =
    useSummaryPaginatedObjectViewerDataComponents({
      objectType,
      objectId,
      objectName: dataset?.name ?? null,
      pageSize: 40,
      setSavingState,
      traceViewParamArgs,
      viewParams,
      enableStarColumn: true,
    });

  const { flags } = useFeatureFlags();

  return (
    <DatasetViewerInner
      paginatedObjectViewerDataComponents={paginatedObjectViewerDataComponents}
      typeHints={typeHints}
      isSortable={flags.summarySorting}
      isExperimentDialogOpen={isExperimentDialogOpen}
      setIsExperimentDialogOpen={setIsExperimentDialogOpen}
      {...props}
    />
  );
}

function DatasetViewerInner({
  defaultPanelLayout,
  objectType,
  objectId,
  paginatedObjectViewerDataComponents,
  dataset,
  orgName,
  projectName,
  projectId,
  datasetName,
  savingState,
  setSavingState,
  invalidate,
  datasetIsLoading,
  viewProps,
  typeHints,
  isSortable,
  permissions,
  isExperimentDialogOpen,
  setIsExperimentDialogOpen,
}: {
  paginatedObjectViewerDataComponents: UnionedPaginatedObjectViewerDataComponents<
    RowData,
    unknown
  >;
  isExperimentDialogOpen: boolean;
  setIsExperimentDialogOpen: (open: boolean) => void;
} & CommonDatasetViewerProps & {
    typeHints: DuckDBJSONStruct | undefined;
    isSortable: boolean;
  }) {
  const isLoopEnabled = useIsLoopEnabled();
  const { mutateDatasets, orgDatasets } = useContext(ProjectContext);
  const orgId = dataset?.org_id;
  const { executeImport, importProgress, setImportProgress } =
    useContext(UploadContext);
  const [{ r: activeRowId }, setActiveRowAndSpan] = useActiveRowAndSpan();

  const { functions: scorerFunctions } = useScorerFunctions({});

  const { dataSources } = useBtqlAutocompleteDataSources({});

  const router = useRouter();
  const org = useOrg();
  const { getOrRefreshToken } = useSessionToken();
  const { user } = useUser();
  const { track } = useAppAnalytics();

  const {
    dml,
    hasTags,
    projectedPaths,
    rowSchema: datasetSchema,
    selectionProps,
    loadingStatus,
    filters,
  } = paginatedObjectViewerDataComponents;
  const {
    deselectAllTableRows,
    getSelectedRowsWithData,
    selectedRows,
    selectedRowsNumber,
  } = selectionProps;

  // NOTE: We should probably exclude "id" (and in general take into account which fields are shown vs hidden)
  const displayPaths = useMemo(
    () => computeDisplayPaths(datasetSchema, projectedPaths),
    [datasetSchema, projectedPaths],
  );

  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  const [rowsToDelete, setRowsToDelete] = useState<any[]>([]);

  const setOtherDatasetInserterInfo = useOtherObjectInserter({
    objectType: "dataset",
    setSavingState,
  });

  const vizQueryRef = useRef<{
    data: ArrowTable<TypeMap> | null;
  } | null>(null);

  const exportedColumns = useMemo(
    () =>
      projectedPaths.concat(
        (paginatedObjectViewerDataComponents.customColumns.columns || []).map(
          ({ name }) => name,
        ),
      ),
    [projectedPaths, paginatedObjectViewerDataComponents.customColumns.columns],
  );

  const getRowsForExport = useGetRowsForExport({
    objectType,
    objectId,
    filters,
    exportedColumns,
    // If we've already loaded the entire dataset or there's a row selection,
    // just use the loaded rows.
    shouldRefetchObject:
      loadingStatus !== "reached_end" && selectedRowsNumber === 0,
    vizQueryRef:
      paginatedObjectViewerDataComponents.tableQuery.type === "tableQuery"
        ? vizQueryRef
        : undefined,
    rowData:
      paginatedObjectViewerDataComponents.tableQuery.type === "tableData"
        ? paginatedObjectViewerDataComponents.tableQuery.data
        : undefined,
    layout: viewProps.layout,
    dataObjectSearchParams: useMemo(
      () => ({
        custom_columns: (
          paginatedObjectViewerDataComponents.customColumns.columns || []
        )
          .map((c) => ({
            expr: { btql: c.expr },
            alias: c.name,
          }))
          .concat(
            (BUILT_IN_CUSTOM_COLUMNS["dataset"] || []).map((c) => ({
              expr: { btql: c.expr },
              alias: c.name,
            })),
          ),
      }),
      [paginatedObjectViewerDataComponents.customColumns.columns],
    ),
  });

  const makeOtherDatasetInserterInfo = useCallback(
    ({
      otherDataset,
      fromSelection,
    }: {
      otherDataset: {
        id: string;
        name: string;
        project_name: string;
      };
      fromSelection: boolean;
    }): OtherObjectInserterInfo => ({
      objectInfo: otherDataset,
      getRows: async () => {
        const selectedRowIds = fromSelection
          ? getSelectedRowsWithData().map((row) => row.id)
          : undefined;
        const rows = await getRowsForExport({
          rowIds: selectedRowIds,
          allColumns: true,
        });
        if (!rows) {
          toast.error("Failed to load rows to copy");
          return [];
        }

        return rows.map((rowJson) => ({
          id: rowJson.id,
          input: rowJson.input,
          metadata: rowJson.metadata,
          expected: rowJson.expected,
          [TagsField]: rowJson.tags,
          [DatasetIdField]: otherDataset.id,
          ...(dataset
            ? {
                [ProjectIdField]: dataset.project_id,
                [OriginField]: {
                  object_type: "dataset",
                  object_id: dataset.id,
                  id: rowJson.id,
                  _xact_id: rowJson._xact_id,
                },
              }
            : {}),
        }));
      },
      onSuccess: (numRows: number) => {
        const datasetName = otherDataset.name;
        const otherDatasetProjectName = otherDataset.project_name;
        toast(
          `Copied ${numRows} ${numRows === 1 ? "row" : "rows"} to dataset`,
          {
            action: (
              <Link
                className={buttonVariants({ size: "xs" })}
                href={getDatasetLink({
                  orgName,
                  projectName: otherDatasetProjectName,
                  datasetName,
                })}
                target="_blank"
              >
                Go to dataset
              </Link>
            ),
            duration: 10000,
            dismissible: true,
          },
        );
      },
      cleanup: () => deselectAllTableRows(),
    }),
    [
      deselectAllTableRows,
      getSelectedRowsWithData,
      getRowsForExport,
      orgName,
      dataset,
    ],
  );

  const { modal: createDatasetModal, open: openCreateDatasetDialog } =
    useCreateDatasetDialog({
      onSuccessfulCreate: ({ datasetId, datasetName, projectName }) => {
        mutateDatasets();
        setOtherDatasetInserterInfo(
          makeOtherDatasetInserterInfo({
            otherDataset: {
              id: datasetId,
              name: datasetName,
              project_name: projectName,
            },
            fromSelection: true,
          }),
        );
      },
      orgId,
      projectName,
    });

  const { modal: duplicateDatasetModal, open: openDuplicateDatasetDialog } =
    useCreateDatasetDialog({
      onSuccessfulCreate: ({ datasetId, datasetName, projectName }) => {
        mutateDatasets();
        setOtherDatasetInserterInfo(
          makeOtherDatasetInserterInfo({
            otherDataset: {
              id: datasetId,
              name: datasetName,
              project_name: projectName,
            },
            fromSelection: false,
          }),
        );
      },
      orgId,
      projectName,
    });

  const { modal: uploadDatasetModal, open: openUploadDatasetDialog } =
    useUploadDatasetDialog({
      orgId: org.id,
      projectName,
      existingDataset: dataset ?? undefined,
      promptName: dataset?.name ?? "",
      onSuccessfullyUploaded: async () => {
        mutateDatasets();
        invalidate();
      },
    });

  const traceViewParams = paginatedObjectViewerDataComponents.traceViewParams;
  const [rowIdInfo, setRowIdInfo] = useState<RowIdInfo[]>([]);

  const { data: datasetVersionBtql, apiTooOld: datasetVersionApiTooOld } =
    useBtql({
      name: "Dataset version query",
      query: dataset?.id
        ? `
    measures: max(${TransactionIdField}) from: dataset(${singleQuote(
      dataset.id,
    )})
  `
        : null,
      brainstoreRealtime: true,
    });
  const { data: datasetVersionLegacy } = useDBQuery(
    datasetVersionApiTooOld === true &&
      traceViewParams.expandedRowParams.primaryScan
      ? `SELECT MAX(${doubleQuote(TransactionIdField)})::text FROM (${
          traceViewParams.expandedRowParams.primaryScan
        })`
      : null,
    traceViewParams.expandedRowParams.primaryScanReady,
  );
  const [datasetVersionTable, datasetVersionFromIncompleteData] =
    datasetVersionBtql
      ? [datasetVersionBtql, false]
      : datasetVersionLegacy
        ? [datasetVersionLegacy, true]
        : [null, undefined];
  const datasetVersion =
    datasetVersionTable &&
    z.string().nullish().parse(datasetVersionTable.get(0)?.toArray()[0]);

  const insertRows = useInsertRows({
    dml,
    projectId: dataset?.project_id,
    datasetId: dataset?.id,
  });

  const partialSetImportProgress = (newState: Partial<ImportProgressType>) =>
    setImportProgress?.((oldState) => ({
      ...oldState,
      ...newState,
    }));

  const addRow = useCallback(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    async (row: any, onOptimisticUpdate?: () => void) =>
      await dml.upsert(
        await dml.prepareUpserts([
          {
            [DatasetIdField]: dataset?.id,
            [ProjectIdField]: dataset?.project_id,
            [IS_MERGE_FIELD]: false,
            ...row,
          },
        ]),
        {
          onOptimisticUpdate,
        },
      ),
    [dataset?.id, dataset?.project_id, dml],
  );

  const defaultPromptData = useDefaultPromptData();

  const onCreatePlayground = useCallback(async () => {
    const apiUrl = org.api_url;
    const userId = user?.id;
    if (!apiUrl || !userId || !dataset?.id) return;

    const playgroundName = newObjectName("Playground");
    const flowId = newId();

    // Track the playground create attempt
    track("playgroundCreateAttempt", {
      playgroundName: playgroundName,
      entryPoint: "datasetPageCreateButton",
      flowId: flowId,
      projectId: projectId!,
      projectName: projectName,
      initialDataType: "dataset",
      initialObjectId: dataset.id,
    });

    const sessionName = playgroundName;
    const { data, error } = await createPromptSession({
      orgName,
      projectName,
      sessionName,
      initialRecords: [
        {
          prompt_data: defaultPromptData,
        },
      ],
      initialPromptArgs: { apiUrl, getOrRefreshToken, userId },
    });
    if (!error && data) {
      const sessionToken = await getOrRefreshToken();
      await performUpsert(null, apiUrl, sessionToken, userId, [
        {
          id: newId(),
          prompt_session_id: data.id,
          org_id: org.id,
          prompt_session_data: { dataset_id: dataset.id },
        },
      ]);
      // Track successful playground creation
      track("playgroundCreate", {
        playgroundName: data.name,
        playgroundId: data.id,
        entryPoint: "datasetPageCreateButton",
        flowId: flowId,
        projectId: projectId!,
        projectName: projectName,
        initialDataType: "dataset",
        initialObjectId: dataset.id,
      });

      router.push(
        getPlaygroundLink({
          orgName,
          projectName,
          playgroundName: data.name,
        }),
      );
    }
  }, [
    orgName,
    projectName,
    dataset?.id,
    getOrRefreshToken,
    org.api_url,
    org.id,
    router,
    user?.id,
    defaultPromptData,
    track,
    projectId,
  ]);

  const onAddRow = async (e: React.MouseEvent<HTMLElement>) => {
    e.stopPropagation();
    const id = newId();
    await addRow({
      id,
    });
    setActiveRowAndSpan({ r: id, s: id });
  };

  const updateDataset = useCallback(
    async (
      datasetId: string,
      updates: Pick<DatasetInfo, "description" | "metadata">,
    ) => {
      setSavingState("saving");
      const reqBody = {
        id: datasetId,
        ...updates,
      };
      const resp = await fetch("/api/dataset/patch_id", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(reqBody),
      });
      if (!resp.ok) {
        const respText = await resp.text();
        toast.error("Failed to update dataset", { description: respText });
        setSavingState(new Error(respText));
      } else {
        setSavingState("saved");
        mutateDatasets();
        invalidate();
      }

      setTimeout(() => setSavingState?.("none"), 5000);
    },
    [invalidate, mutateDatasets, setSavingState],
  );

  const updateMetadata: UpdateValueFn = useCallback(
    async (value) => {
      if (!dataset?.id) {
        return null;
      }

      try {
        datasetZodSchema.shape.metadata.parse(value);
        await updateDataset(dataset?.id, {
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
          metadata: value as Record<string, unknown>,
        });
      } catch (error) {
        toast.error("Error updating metadata", {
          description: zodErrorToString(
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
            error as ZodError,
            0,
            false,
          ),
        });
      } finally {
        return "0";
      }
    },
    [dataset?.id, updateDataset],
  );

  const extraLeftControls = (
    <TableRowHeightToggle
      tableRowHeight={viewProps.rowHeight ?? "compact"}
      onSetRowHeight={viewProps.setRowHeight}
    />
  );

  const extraRightControls = (
    <div className="flex justify-end gap-2">
      <Button
        Icon={Upload}
        size="xs"
        disabled={
          importProgress.currentState !== "not started" &&
          importProgress.currentState !== "closed"
        }
        onClick={() => openUploadDatasetDialog(dataset ?? undefined)}
      >
        <span className="hidden @xl/controls:block">Import</span>
      </Button>
      {isLoopEnabled && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button Icon={Plus} size="xs">
              <span className="hidden @xl/controls:block">Row</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start">
            <DropdownMenuGroup>
              <DropdownMenuItem
                onClick={onAddRow}
                className="flex items-center gap-2"
              >
                <Plus className="size-3" />
                Blank row
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <GenerateRowsButton asDropdownMenuItem />
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </div>
  );

  const mainPaneScrollContainer = useRef<HTMLDivElement>(null);

  const updateRow = useEvent<UpdateRowFn>(async (row, path, newValue) => {
    const r = row;
    r.dataset_id = dataset?.id;
    return await dml.update([row], [{ path, newValue }]);
  });

  const rowCount = useRowCount({
    filters,
    objectType,
    objectId,
  });

  const downloadColumnVisibility = useMemo(() => {
    return {
      ...paginatedObjectViewerDataComponents.viewProps.columnVisibility,
      ...Object.fromEntries(
        Array.from(NEVER_VISIBLE_COLUMNS).map((k) => [k, false]),
      ),
    };
  }, [paginatedObjectViewerDataComponents.viewProps.columnVisibility]);

  const { downloadMenu, confirmDownloadDialog } = useDownloadRowsMenu({
    exportName: datasetName,
    getRowsForExport,
    columnVisibility: downloadColumnVisibility,
    selectedRows:
      paginatedObjectViewerDataComponents.selectionProps.selectedRows,
    rowCount: rowCount ?? 0,
  });

  const vizQueryPropsOverride = useMemo(() => {
    const propsOverride: Pick<
      ArrowTableUIProps<unknown, unknown>,
      | "className"
      | "displayPaths"
      | "scrollContainerRef"
      | "multilineRow"
      | "showRowNumber"
      | "enableStarColumn"
      | "updateRow"
      | "toolbarSlot"
      | "typeHints"
      | "isSortable"
    > &
      Pick<VizQueryProps<unknown, unknown>, "vizQueryRef"> = {
      displayPaths: displayPaths,
      scrollContainerRef: mainPaneScrollContainer,
      vizQueryRef,
      enableStarColumn: true,
      showRowNumber: true,
      className: "pt-3",
      updateRow,
      typeHints,
      isSortable,
      multilineRow:
        viewProps.rowHeight === "tall"
          ? {
              numRows: 5,
              gapSize: 0,
            }
          : undefined,
      toolbarSlot:
        selectedRowsNumber === 0 ? undefined : (
          <>
            <CancelSelectionButton
              onCancelSelection={deselectAllTableRows}
              selectedRowsNumber={selectedRowsNumber}
            />
            <DatasetDropdown
              selectedDatasetId={dataset?.id}
              datasets={orgDatasets.filter((d) => d.id !== dataset?.id)}
              onCreateNewDataset={openCreateDatasetDialog}
              onSelectDataset={(data) => {
                setOtherDatasetInserterInfo(
                  makeOtherDatasetInserterInfo({
                    otherDataset: data,
                    fromSelection: true,
                  }),
                );
              }}
            >
              <Button Icon={Database} size="xs">
                Add to dataset
              </Button>
            </DatasetDropdown>
            {downloadMenu}
            {confirmDownloadDialog}
            {hasTags && dml && (
              <TagBulkEditor
                dml={dml}
                selectionProps={{
                  selectedRows,
                  getSelectedRowsWithData,
                }}
                buttonClassName="text-primary-700"
              />
            )}
            {dml && (
              <AssignBulkEditor
                dml={dml}
                selectionProps={{
                  selectedRows,
                  getSelectedRowsWithData,
                }}
                buttonClassName="text-primary-700"
                objectType={objectType}
                aclObjectId={dataset?.id}
              />
            )}
            {
              <SelectionBarButton
                title="Place a copy of selected rows in current dataset"
                onClick={async () => {
                  const selectedRowIds = getSelectedRowsWithData().map(
                    (row) => row.id,
                  );
                  if (selectedRowIds.length === 0) {
                    throw new Error("No rows selected");
                  }
                  const rows = await getRowsForExport({
                    rowIds: selectedRowIds,
                    allColumns: true,
                  });
                  if (!rows) {
                    throw new Error("Failed to load rows to duplicate");
                  }

                  const rowsToDuplicate = rows.map((row) => {
                    const id = newId();
                    return {
                      id,
                      input: row.input,
                      metadata: row.metadata,
                      expected: row.expected,
                      [TagsField]: row.tags,
                      [DatasetIdField]: dataset?.id,
                      [ProjectIdField]: dataset?.project_id,
                    };
                  });
                  deselectAllTableRows();
                  const preparedUpserts =
                    await dml.prepareUpserts(rowsToDuplicate);
                  await dml.upsert(preparedUpserts);
                }}
              >
                Duplicate
              </SelectionBarButton>
            }
            <SelectionBarButton
              title="Delete selected rows from current dataset"
              onClick={() => {
                setRowsToDelete(getSelectedRowsWithData());
              }}
              Icon={Trash}
            />
          </>
        ),
    };
    return propsOverride;
  }, [
    displayPaths,
    updateRow,
    typeHints,
    isSortable,
    viewProps.rowHeight,
    selectedRowsNumber,
    deselectAllTableRows,
    hasTags,
    dml,
    selectedRows,
    getSelectedRowsWithData,
    dataset?.id,
    dataset?.project_id,
    orgDatasets,
    openCreateDatasetDialog,
    setOtherDatasetInserterInfo,
    makeOtherDatasetInserterInfo,
    getRowsForExport,
    downloadMenu,
    confirmDownloadDialog,
    objectType,
  ]);

  const paginatedObjectViewerVizComponents =
    usePaginatedObjectViewerVizComponents(paginatedObjectViewerDataComponents, {
      rowIdInfo,
      setRowIdInfo,
      aiSearchType: "dataset",
      extraLeftControls,
      extraRightControls,
      vizQueryPropsOverride,
      hideLiveButton: true,
      getRowsForExport,
      rowCount: rowCount ?? 0,
      downloadColumnVisibility,
    });

  const minSidePanelWidth = usePanelSize(380);
  const minMainPanelWidth = usePanelSize(520);

  const { actions: datasetActions, modals: datasetActionModals } =
    useEntityContextActions({
      entityType: "dataset",
      onUpdate: mutateDatasets,
      reloadPageOnUpdateArgs: {
        getEditedEntityLink: (datasetName) =>
          getDatasetLink({
            orgName,
            projectName,
            datasetName,
          }),
        getDeletedEntityLink: () => getDatasetsLink({ orgName, projectName }),
      },
    });

  const { isTraceFullscreen } = useTraceFullscreen();

  // https://github.com/bvaughn/react-resizable-panels?tab=readme-ov-file#how-can-i-use-persistent-layouts-with-ssr
  const onPanelLayout = useCallback(
    (sizes: number[]) => {
      const layoutCookie: DatasetPanelLayout = {
        ...defaultPanelLayout,
        main: sizes[0],
        trace: sizes[1],
      };
      document.cookie = `react-resizable-panels:dataset-layout=${JSON.stringify(
        layoutCookie,
      )}; path=/`;
    },
    [defaultPanelLayout],
  );

  const defaultDataSource = useMemo(
    () => [
      { entity: "dataset" as const, id: dataset?.id ?? "" },
      { entity: "project_logs" as const, id: projectId ?? "" },
    ],
    [dataset?.id, projectId],
  );

  if (isEmpty(projectId) || (!datasetIsLoading && !dataset)) {
    return <AccessFailed objectType="Dataset" objectName={datasetName} />;
  } else if (
    paginatedObjectViewerVizComponents.status === "loading_initial" ||
    !dataset
  ) {
    return (
      <div
        className={cn(
          "flex items-center justify-center bg-primary-50",
          HEIGHT_WITH_TOP_OFFSET,
        )}
      >
        <Loading />
      </div>
    );
  }

  const { datasetIsEmpty, tracePanelComponents, tableViewComponents } =
    paginatedObjectViewerVizComponents.status === "loaded_empty"
      ? {
          datasetIsEmpty: true,
          tracePanelComponents: undefined,
          tableViewComponents: undefined,
        }
      : { datasetIsEmpty: false, ...paginatedObjectViewerVizComponents };

  const showImportWizard = datasetIsEmpty;

  const mainPanelComponent = (
    <div
      className={cn(
        "relative flex flex-auto flex-col overflow-auto px-3",
        showImportWizard ? "pt-3" : "pt-0",
      )}
      ref={mainPaneScrollContainer}
    >
      {showImportWizard ? (
        <ImportWizard
          forEmpty
          setDataToImport={async ({ data }) => {
            await executeImport({
              data,
              setImportProgress: partialSetImportProgress,
              insertRows,
            });
            await mutateDatasets();
            await invalidate();
          }}
          setImportProgress={partialSetImportProgress}
          importProgress={importProgress}
          onAddRow={onAddRow}
        />
      ) : (
        tableViewComponents
      )}
      <div className="grow" />
      <Footer
        className="sticky left-0 w-full pb-4 sm:pb-4 lg:pb-4"
        inApp
        orgName={orgName}
      />
    </div>
  );

  const isReadOnly =
    !permissions.includes("update") && !permissions.includes("delete");

  return (
    <OptimizationProvider
      datasetId={dataset.id}
      datasetDML={dml}
      createDataset={createDataset}
      refreshDatasets={mutateDatasets}
      datasetData={paginatedObjectViewerDataComponents.tableQuery}
      setDatasetId={() => {}}
      queryProjectId={projectId ?? undefined}
    >
      <GlobalChatProvider defaultDataSource={defaultDataSource}>
        <div className="flex w-full">
          <MainContentWrapper
            hideFooter
            className={cn(
              "flex w-full flex-col overflow-hidden py-0",
              HEIGHT_WITH_TOP_OFFSET,
            )}
          >
            <div className="group -mx-3 flex flex-none flex-row flex-wrap items-start gap-2 bg-primary-50 px-3 pt-1 pb-2 md:items-center">
              <div className="mr-2 flex items-center gap-2">
                <h1 className="inline-flex items-center gap-2 text-base font-semibold">
                  <Database className="size-4 text-fuchsia-600 dark:text-fuchsia-400" />
                  {datasetName}
                </h1>
                <EntityContextMenu
                  objectType="dataset"
                  objectId={dataset.id}
                  objectName={datasetName}
                  orgName={orgName}
                  projectName={projectName}
                  buttonClassName="h-7 w-7"
                  handleDelete={() =>
                    datasetActions.deleteEntity({
                      entityName: datasetName,
                      entityId: dataset.id,
                      trackAnalytics: {
                        source: "dataset_page_overflow_control",
                      },
                    })
                  }
                  handleEdit={() =>
                    datasetActions.editEntityName({
                      entityName: datasetName,
                      entityId: dataset.id,
                      trackAnalytics: {
                        source: "dataset_page_overflow_control",
                      },
                    })
                  }
                  handleCopyId={() =>
                    datasetActions.copyEntityId({
                      entityId: dataset.id,
                    })
                  }
                  getRowsForExport={getRowsForExport}
                  extraOptions={[
                    ...(isReadOnly
                      ? []
                      : [
                          {
                            label: (
                              <>
                                <Copy className="size-3" />
                                Duplicate
                              </>
                            ),
                            onClick: () => {
                              openDuplicateDatasetDialog(
                                objectCopyName(datasetName),
                              );
                            },
                          },
                        ]),
                  ]}
                  downloadColumnVisibility={downloadColumnVisibility}
                  isReadOnly={isReadOnly}
                  rowCount={rowCount ?? 0}
                />
              </div>
              {datasetActionModals}
              <div className="flex flex-1 justify-end gap-2">
                <SavingStatus className="mr-2 text-xs" state={savingState} />
                {datasetVersion && dataset.id && (
                  <DatasetMetadata
                    showVersionOnPreview
                    datasetId={dataset.id}
                    datasetName={dataset.name}
                    datasetVersion={datasetVersion}
                    datasetVersionFromIncompleteData={
                      datasetVersionFromIncompleteData
                    }
                    orgName={orgName}
                    projectName={projectName}
                  />
                )}
                {paginatedObjectViewerVizComponents.status ===
                  "loaded_empty" && (
                  <Button size="xs" onClick={onAddRow} Icon={Plus}>
                    Row
                  </Button>
                )}
                <ReviewButton variant="border" />
                {isLoopEnabled && (
                  <OptimizationChat
                    contextSelectorData={{
                      dataSources,
                      btqlTabs: [],
                    }}
                  />
                )}
                {paginatedObjectViewerVizComponents.status !==
                  "loaded_empty" && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button size="xs" variant="primary" isDropdown>
                        Evaluate in
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="max-w-64" align="end">
                      <DropdownMenuItem
                        className="p-3"
                        onClick={() => {
                          toast.promise(onCreatePlayground(), {
                            loading: "Creating playground...",
                            success: "Playground created",
                            error: (error) =>
                              `Failed to create playground: ${error?.message ?? "Unknown error"}`,
                          });
                        }}
                      >
                        <Shapes className="size-4 flex-none text-accent-600" />
                        <span className="flex flex-col gap-0.5">
                          <span className="font-medium">Playground</span>
                          <span className="text-xs text-primary-500">
                            Run and compare prompts against this dataset
                            interactively
                          </span>
                        </span>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="p-3"
                        onClick={() => setIsExperimentDialogOpen(true)}
                      >
                        <Beaker className="size-4 flex-none text-comparison-600" />
                        <span className="flex flex-col gap-0.5">
                          <span className="font-medium">Experiment</span>
                          <span className="text-xs text-primary-500">
                            Track performance of a prompt over time using this
                            dataset
                          </span>
                        </span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </div>
            </div>
            <BodyWrapper
              outerClassName={cn("-mx-3", HEIGHT_WITH_DOUBLE_TOP_OFFSET)}
              innerClassName="flex overflow-hidden"
            >
              <div className="flex flex-auto overflow-hidden">
                {isTraceFullscreen && tracePanelComponents ? (
                  tracePanelComponents
                ) : showImportWizard ? (
                  mainPanelComponent
                ) : (
                  <ResizablePanelGroup
                    direction="horizontal"
                    autoSaveId="datasetPanelLayout"
                    className="flex w-full flex-row-reverse!"
                    onLayout={onPanelLayout}
                  >
                    <ResizablePanel
                      order={1}
                      defaultSize={Math.max(
                        defaultPanelLayout.trace,
                        minSidePanelWidth,
                      )}
                      minSize={minSidePanelWidth}
                      id="trace"
                      className="flex flex-col"
                    >
                      {tracePanelComponents ? (
                        tracePanelComponents
                      ) : (
                        <div className="flex flex-col overflow-y-auto p-3">
                          <div className="mb-2 flex-none text-xs text-primary-600">
                            Description
                          </div>
                          <TextArea
                            id="datsetDescription"
                            name="datasetDescription"
                            className="min-h-[38px]"
                            placeholder="Enter dataset description"
                            defaultValue={dataset.description ?? undefined}
                            onDebouncedChange={(val) => {
                              updateDataset(dataset.id, { description: val });
                            }}
                          />
                          <div className="mt-7 mb-2 flex-none text-xs text-primary-600">
                            Metadata
                          </div>
                          <UpdateableDataTextEditor
                            value={dataset.metadata}
                            rowId={dataset.id}
                            xactId="0"
                            updateValue={updateMetadata}
                            formatOnBlur
                          />
                          {dataset?.id && (
                            <LibraryItemLinks
                              projectName={projectName}
                              objectType={objectType}
                              objectId={dataset.id}
                              className="mt-5 text-xs font-normal text-primary-600"
                            />
                          )}
                        </div>
                      )}
                    </ResizablePanel>
                    <ResizableHandle />
                    <ResizablePanel
                      order={0}
                      className="relative flex"
                      minSize={minMainPanelWidth}
                      defaultSize={defaultPanelLayout.main}
                      id="main"
                    >
                      {mainPanelComponent}
                    </ResizablePanel>

                    {rowsToDelete.length > 0 && (
                      <ConfirmationDialog
                        open={rowsToDelete.length > 0}
                        onOpenChange={() => setRowsToDelete([])}
                        title="Delete rows"
                        description={`Are you sure you want to delete ${pluralizeWithCount(
                          rowsToDelete.length,
                          "row",
                          "rows",
                        )}?`}
                        confirmText={"Delete"}
                        onConfirm={async () => {
                          if (
                            activeRowId &&
                            rowsToDelete.some((r) => r.id === activeRowId)
                          ) {
                            setActiveRowAndSpan({ r: null });
                          }
                          deselectAllTableRows();
                          const preparedDeletes =
                            await dml.prepareDeletes(rowsToDelete);
                          await dml.upsert(preparedDeletes, {
                            onOptimisticUpdate: () => {
                              toast.success(
                                `Deleted ${pluralizeWithCount(rowsToDelete.length, "row", "rows")}`,
                              );
                            },
                          });
                        }}
                      />
                    )}
                    {createDatasetModal}
                    {duplicateDatasetModal}
                    {uploadDatasetModal}
                    <CreateExperimentDialog
                      mode="single"
                      dataset={dataset.id}
                      scorers={[]}
                      scorerFunctions={scorerFunctions}
                      maxConcurrency={10}
                      strict={false}
                    >
                      <button
                        style={{ display: "none" }}
                        ref={(el) => {
                          if (el && isExperimentDialogOpen) {
                            el.click();
                            setIsExperimentDialogOpen(false);
                          }
                        }}
                      />
                    </CreateExperimentDialog>
                  </ResizablePanelGroup>
                )}
              </div>
            </BodyWrapper>
          </MainContentWrapper>
          <DockedChatSpacer />
        </div>
      </GlobalChatProvider>
    </OptimizationProvider>
  );
}
