/* eslint-disable @typescript-eslint/consistent-type-assertions */
import { But<PERSON> } from "#/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "#/ui/dialog";
import {
  type PropsWithChildren,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  type SavedScorer,
  savedScorerToFunctionId,
  savedScoreSchema,
} from "#/utils/scorers";
import { Database, MinusCircle, Plus, XIcon } from "lucide-react";
import { DataTextEditor } from "#/ui/data-text-editor";
import {
  createParser,
  type EventSourceParser,
  type ParsedEvent,
  type ReconnectInterval,
} from "eventsource-parser";
import { toast } from "sonner";
import { type ExperimentSummary } from "braintrust";
import { useRouter } from "next/navigation";
import { ProjectContext } from "../../p/[project]/projectContext";
import type { ProjectContextDataset } from "../../p/[project]/project-actions";
import {
  type FunctionId,
  functionIdSchema,
  type RemoteEvalData,
  type RunEvalRequest,
} from "@braintrust/typespecs";
import { useOrg } from "#/utils/user";
import { _urljoin } from "braintrust/util";
import { newId } from "braintrust";
import {
  type BtSessionToken,
  type LoadedBtSessionToken,
  sessionFetchProps,
  useSessionToken,
} from "#/utils/auth/session-token";
import { normalizeProxyUrlBase } from "#/utils/user-types";
import { useFieldArray, useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "#/ui/form";
import { Input } from "#/ui/input";
import { CollapsibleSection } from "#/ui/collapsible-section";
import { DatasetDropdown } from "#/ui/dataset-dropdown";
import { cn } from "#/utils/classnames";
import { ScorersDropdown } from "./scorers/scorers-dropdown";
import { Switch } from "#/ui/switch";
import { type UIFunction } from "#/ui/prompts/schema";
import { AddTask } from "../../p/[project]/playgrounds/[playground]/add-task";
import { useProjectPromptsDropdown } from "../../p/[project]/playgrounds/prompts-dropdown";
import { useAgentsDropdown } from "../../p/[project]/playgrounds/agents-dropdown";
import { useScorerFunctions } from "./scorers/open";
import { PromptVersionItem, VersionSelector } from "./version-selector";
import { dataObjectTypes } from "#/utils/btapi/load-bt-cache-db";
import { TransactionIdField } from "@braintrust/local/query";
import Link from "next/link";
import { getProjectLink } from "../../p/[project]/getProjectLink";
import { Skeleton } from "#/ui/skeleton";
import { useAppAnalytics } from "#/ui/analytics/segment-analytics";
import { type EventName, type EventProps } from "#/analytics/events";

// Helper function to extract model provider from model name
function extractModelProvider(modelName: string): string | undefined {
  if (!modelName) return undefined;

  const lowerModelName = modelName.toLowerCase();

  // Common model name patterns
  if (
    lowerModelName.startsWith("gpt-") ||
    lowerModelName.startsWith("o1-") ||
    lowerModelName.includes("openai")
  ) {
    return "openai";
  }
  if (
    lowerModelName.startsWith("claude-") ||
    lowerModelName.startsWith("haiku-") ||
    lowerModelName.startsWith("sonnet-") ||
    lowerModelName.startsWith("opus-") ||
    lowerModelName.includes("anthropic")
  ) {
    return "anthropic";
  }
  if (lowerModelName.includes("gemini") || lowerModelName.includes("google")) {
    return "google";
  }
  if (lowerModelName.includes("mistral")) {
    return "mistral";
  }
  if (lowerModelName.includes("llama")) {
    return "meta";
  }

  // Default fallback
  return undefined;
}

export type CreateExperimentTask = z.infer<typeof createExperimentTaskSchema>;

const createExperimentTaskSchema = z.intersection(
  z.object({
    name: z.string(),
    metadata: z.record(z.unknown()),
    versionData: z
      .object({
        id: z.string().optional(),
        version: z.string().optional(),
        projectId: z.string().optional(),
        objectType: z.enum(dataObjectTypes),
      })
      .optional(),
  }),
  functionIdSchema,
);

export const createExperimentFormSchema = z.object({
  tasks: z.array(createExperimentTaskSchema).min(1),
  dataset: z.string().nullable(),
  scorers: z.array(savedScoreSchema),
  metadata: z.record(z.unknown()).optional().default({}),
  maxConcurrency: z.number().min(1).nullish(),
  trialCount: z.number().min(1).nullish(),
  strict: z.boolean().nullish(),
});

export const CreateSingleExperimentNullStateDialog = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const { functions } = useScorerFunctions({});
  return (
    <CreateExperimentDialog
      mode="single"
      scorers={[]}
      scorerFunctions={functions}
      maxConcurrency={10}
      strict={false}
    >
      {children}
    </CreateExperimentDialog>
  );
};

export const CreateExperimentDialog = ({
  children,
  dataset,
  getFunctions,
  scorers,
  scorerFunctions,
  maxConcurrency,
  strict,
  mode,
  extraMessages,
}: PropsWithChildren<{
  mode: "playground" | "single";
  dataset?: string;
  getFunctions?: (args: {
    useInlinePrompts: boolean;
  }) => Promise<CreateExperimentTask[]>;
  scorers: SavedScorer[];
  scorerFunctions: Record<string, UIFunction>;
  maxConcurrency: number | undefined;
  strict: boolean | undefined;
  extraMessages?: string;
}>) => {
  const [open, setOpen] = useState(false);

  const [tasks, setTasks] = useState<CreateExperimentTask[]>([]);
  const [isLoadingTasks, setIsLoadingTasks] = useState(mode === "playground");
  const { projectId, projectName } = useContext(ProjectContext);
  const { track } = useAppAnalytics();
  const flowId = useMemo(() => newId(), []);
  useEffect(() => {
    (async () => {
      if (!getFunctions) return;
      if (open) {
        const t = await getFunctions({ useInlinePrompts: false });
        setTasks(t);
        setIsLoadingTasks(false);
      } else {
        setTasks([]);
        setIsLoadingTasks(true);
      }
    })().catch((e) => {
      console.error(e);
      setIsLoadingTasks(false);
    });
  }, [open, getFunctions]);

  const numTasks = tasks.length;

  // Attempt tracking at dialog open (for non-empty state flows)
  const dialogAttemptTrackedRef = useRef(false);
  const getSourcePageForDialog = useCallback(() => {
    if (typeof window === "undefined") return "other";
    const p = window.location.pathname;
    if (p.includes("/playgrounds/")) return "playgrounds";
    if (p.endsWith("/experiments")) return "experiments";
    if (p.includes("/prompt/")) return "prompt";
    if (p.includes("/datasets/")) return "datasets";
    if (p.includes("/scorers")) return "scorers";
    return "other";
  }, []);
  const trackAttemptOnDialogOpen = useCallback(() => {
    if (!dialogAttemptTrackedRef.current && projectId) {
      dialogAttemptTrackedRef.current = true;
      const sourcePage = getSourcePageForDialog();
      track("experimentCreateAttempt", {
        flowId,
        projectId,
        projectName,
        entryPoint: "experimentsPageCreateButton",
        sourcePage,
        experimentName: tasks[0]?.name || "New Experiment",
        method: "ui",
        source: "web" as const,
      });
    }
  }, [track, flowId, projectId, projectName, tasks, getSourcePageForDialog]);

  return (
    <Dialog
      open={open}
      onOpenChange={(isOpen) => {
        setOpen(isOpen);
        if (isOpen) trackAttemptOnDialogOpen();
      }}
    >
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent>
        <DialogHeader className="mb-2">
          <DialogTitle>
            Create {numTasks === 1 ? "experiment" : "experiments"}
          </DialogTitle>
          <DialogDescription>
            Experiments are snapshots of your AI application at a point in time.
            They are used to evaluate and compare tasks.
          </DialogDescription>
        </DialogHeader>
        {isLoadingTasks && <Skeleton className="block h-12" />}
        {open && !isLoadingTasks && (
          <CreateExperimentForm
            mode={mode}
            dataset={dataset}
            tasks={tasks}
            scorers={scorers}
            scorerFunctions={scorerFunctions}
            maxConcurrency={maxConcurrency}
            strict={strict}
            extraMessages={extraMessages}
            onClose={() => setOpen(false)}
            flowId={flowId}
          />
        )}
      </DialogContent>
    </Dialog>
  );
};

export const CreateExperimentForm = ({
  mode,
  dataset,
  tasks,
  scorers,
  scorerFunctions,
  onClose,
  maxConcurrency,
  strict,
  extraMessages,
  onFormChange,
  footerClassName,
  flowId,
}: {
  mode: "playground" | "single";
  dataset?: string;
  tasks: CreateExperimentTask[];
  scorers: SavedScorer[];
  scorerFunctions: Record<string, UIFunction>;
  maxConcurrency: number | undefined;
  strict: boolean | undefined;
  onClose?: VoidFunction;
  extraMessages?: string;
  onFormChange?: (
    formValues: z.infer<typeof createExperimentFormSchema>,
  ) => void;
  footerClassName?: string;
  flowId: string;
}) => {
  const { mutateExperiments, projectId, projectName } =
    useContext(ProjectContext);
  const org = useOrg();
  const proxyUrl = org.proxy_url;
  const { track } = useAppAnalytics();

  const { getOrRefreshToken } = useSessionToken();

  const { orgDatasets, isOrgDatasetsLoading } = useContext(ProjectContext);
  const [datasetDropdownOpen, setDatasetDropdownOpen] = useState(false);

  // Utility function to get the actual page name for sourcePage tracking
  const getSourcePage = useCallback(() => {
    if (typeof window === "undefined") return "other";

    const p = window.location.pathname;
    if (p.includes("/playgrounds/")) return "playgrounds";
    if (p.endsWith("/experiments")) return "experiments";
    if (p.includes("/prompt/")) return "prompt";
    if (p.includes("/datasets/")) return "datasets";
    if (p.includes("/scorers")) return "scorers";
    return "other";
  }, []);

  const datasetNameMap = useMemo(
    () =>
      orgDatasets
        ? new Map(
            orgDatasets.map((dataset) => [
              dataset.id,
              {
                name: dataset.name,
                projectName: dataset.project_name,
              },
            ]),
          )
        : undefined,
    [orgDatasets],
  );

  const validScorers = scorers.filter(
    (s) => s.type === "global" || scorerFunctions[s.id],
  );
  const form = useForm<z.infer<typeof createExperimentFormSchema>>({
    resolver: zodResolver(createExperimentFormSchema),
    defaultValues: {
      dataset,
      tasks,
      scorers: validScorers,
      maxConcurrency,
      strict,
    },
  });

  useEffect(() => {
    if (!onFormChange) return;

    const sub = form.subscribe({
      formState: {
        values: true,
      },
      callback: (formState) => {
        if (formState.values) {
          onFormChange(formState.values);
        }
      },
    });

    return () => sub();
  }, [form, onFormChange]);

  const router = useRouter();
  const onGoToExperiment = (experimentUrl: string) => {
    router.push(experimentUrl);
  };

  const {
    fields: taskFields,
    remove: removeTask,
    append: addTask,
    update: updateTask,
  } = useFieldArray({
    control: form.control,
    name: "tasks",
  });

  const onSubmit = async (data: z.infer<typeof createExperimentFormSchema>) => {
    if (!org.api_url) {
      toast.error("Please sign in to create experiments");
      return;
    }

    const datasetEntry = orgDatasets.find((d) => d.id === data.dataset);
    if (!datasetEntry) {
      form.setError("dataset", {
        message: "Invalid dataset",
      });
      return;
    }

    const sessionToken = await getOrRefreshToken();

    data.tasks.forEach(({ name, metadata: modelMetadata, ...task }) => {
      const evalRequest: EvalRequest =
        "inline_function" in task && task.inline_function.type === "remote_eval"
          ? {
              type: "remote",
              // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
              request: task.inline_function as RemoteEvalData,
              data: {
                dataset_name: datasetEntry.name,
                project_name: datasetEntry.project_name,
              },
              scores: data.scorers.flatMap((s) =>
                s.type === "global" || scorerFunctions[s.id]
                  ? [
                      {
                        function_id: savedScorerToFunctionId(s),
                        name:
                          s.type === "global"
                            ? s.name
                            : (scorerFunctions[s.id].name ?? `scorer_${s.id}`),
                      },
                    ]
                  : [],
              ),
              experiment_name: name,
              project_id: projectId ?? undefined,
              parent: undefined,
              stream: true,
            }
          : {
              type: "api",
              request: {
                experiment_name: name,
                metadata: {
                  dataset: datasetEntry.name,
                  ...modelMetadata,
                  ...data.metadata,
                },
                project_id: projectId ?? "",
                data: {
                  dataset_name: datasetEntry.name,
                  project_name: datasetEntry.project_name,
                },
                task,
                scores: data.scorers.flatMap((s) =>
                  s.type === "global" || scorerFunctions[s.id]
                    ? [savedScorerToFunctionId(s)]
                    : [],
                ),
                stream: true,
                trial_count: data.trialCount,
                max_concurrency: data.maxConcurrency ?? null,
                strict: data.strict ?? undefined,
                extra_messages: extraMessages,
              },
            };
      runEval({
        proxyUrl,
        evalRequest,
        sessionToken,
        orgName: org.name,
        org,
        onGoToExperiment,
        mutateExperiments,
        formData: data,
        orgDatasets,
        scorerFunctions,
        promptsByProjectSorted,
        track,
        analyticsCtx: {
          common: {
            flowId,
            projectId: projectId!,
            projectName: projectName ?? "",
            mode,
            datasetId: dataset,
            sourcePage: getSourcePage(),
          },
          experimentName: tasks[0]?.name || "New Experiment",
          modelProvider: undefined,
          modelName: undefined,
          temperature: undefined,
        },
      });
    });
    onClose?.();
  };

  const onMetadataError = useCallback(
    (e: Error | null) => {
      if (!e) {
        form.clearErrors("metadata");
        return;
      }
      form.setError("metadata", {
        message: e.message,
      });
    },
    [form],
  );

  const { promptsByProjectSorted } = useProjectPromptsDropdown();
  const { promptsByProjectSorted: agentsByProjectSorted } = useAgentsDropdown();

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex w-full max-w-full min-w-0 flex-col gap-6"
      >
        <FormField
          control={form.control}
          name="tasks"
          render={({ field }) => (
            <div>
              <div className="flex items-center justify-between text-sm">
                Experiment names
                {mode === "single" && (
                  <Link
                    className="text-xs text-accent-600"
                    href={`${getProjectLink({
                      orgName: org.name,
                      projectName,
                    })}/prompts/new`}
                  >
                    Create prompt
                  </Link>
                )}
              </div>
              {taskFields.map((field, idx) => {
                const hasVersionSelector =
                  field.versionData?.id &&
                  field.versionData.projectId &&
                  field.versionData.objectType &&
                  field.versionData.version;
                return (
                  <FormItem key={field.id}>
                    <FormLabel className="sr-only">
                      Task {idx + 1} name
                    </FormLabel>
                    <FormControl>
                      <div
                        className={cn("flex flex-col text-sm", {
                          "mt-2": taskFields.length > 1,
                        })}
                      >
                        <div className="relative flex flex-1 items-center">
                          <Input
                            {...form.register(`tasks.${idx}.name`)}
                            placeholder="Enter task name"
                            className={cn("h-9 truncate pr-8", {
                              "rounded-b-none": hasVersionSelector,
                            })}
                          />
                          {(taskFields.length > 1 || mode === "single") && (
                            <Button
                              size="xs"
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                removeTask(idx);
                              }}
                              className="absolute right-1 z-20"
                              variant="ghost"
                              Icon={MinusCircle}
                            />
                          )}
                        </div>
                        {hasVersionSelector && (
                          <VersionSelector
                            className="h-7 min-h-0 flex-none rounded-t-none rounded-bl-md border-t-0 px-3 py-0"
                            rowId={field.versionData!.id!}
                            objectId={field.versionData!.projectId!}
                            objectType={field.versionData!.objectType!}
                            selectedVersion={field.versionData!.version!}
                            onVersionChange={(prompt) => {
                              // When creating experiments from a playground, tasks are initialized using the instances of a saved prompt in that playground.
                              // When a new version is selected, we need to update the function id used to create the experiment to the origin prompt id.
                              const newPromptId = prompt.prompt?.id
                                ? { function_id: prompt.prompt?.id }
                                : {};
                              const newTask = {
                                ...field,
                                ...newPromptId,
                                version: prompt.prompt?.[TransactionIdField],
                                versionData: {
                                  ...field.versionData!,
                                  version: prompt.prompt?.[TransactionIdField],
                                },
                              };
                              if (
                                "prompt_session_function_id" in newTask &&
                                "function_id" in newTask
                              ) {
                                // @ts-expect-error - When the experiment was initialized from the playground and then a different version chosen,
                                // we need to remove the old `prompt_session_function_id`
                                delete newTask.prompt_session_function_id;
                              }
                              updateTask(idx, newTask);
                            }}
                            label="Version"
                            VersionItemRenderer={PromptVersionItem}
                          />
                        )}
                      </div>
                    </FormControl>
                  </FormItem>
                );
              })}
              {taskFields.length > 1 && mode === "playground" && (
                <FormDescription className="mt-2 text-xs">
                  Each task from this playground will result in a separate
                  experiment
                </FormDescription>
              )}
              <FormMessage />

              {mode === "single" && (
                <AddTask
                  align="start"
                  projectName={projectName}
                  hideRemoteEvals
                  disallowBlankTasks
                  flowId={flowId}
                  projectId={projectId!}
                  experimentName={tasks[0]?.name || "New Experiment"}
                  onAdd={(t) => {
                    if (!t.origin?.prompt_id) return;
                    const isAgent = t.functionData?.type === "graph";
                    const isScorer = t.functionType === "scorer";
                    const taskName = isAgent
                      ? agentsByProjectSorted
                          .find((p) => p.projectId === t.origin?.project_id)
                          ?.prompts.find((p) => p.id === t.origin?.prompt_id)
                          ?.name
                      : promptsByProjectSorted
                          .find((p) => p.projectId === t.origin?.project_id)
                          ?.prompts.find((p) => p.id === t.origin?.prompt_id)
                          ?.name;

                    addTask({
                      function_id: t.origin.prompt_id,
                      version: t.origin.prompt_version,
                      versionData: {
                        projectId: t.origin.project_id,
                        objectType:
                          isScorer || isAgent
                            ? "project_functions"
                            : "project_prompts",
                        version: t.origin.prompt_version,
                        id: t.origin.prompt_id,
                      },
                      name: taskName ?? "Task",
                      metadata: {},
                    });
                    // Track element add (task or scorer)
                    if (projectId) {
                      const isScorer = t.functionType === "scorer";
                      track("experimentElementAdd", {
                        elementType: isScorer ? "scorer" : "task",
                        elementId: t.origin.prompt_id,
                        elementName: taskName ?? "Task",
                        taskType: isScorer
                          ? "function"
                          : isAgent
                            ? "function"
                            : "prompt",
                        scorerType: isScorer
                          ? t.functionData?.type === "global"
                            ? "global"
                            : "custom"
                          : undefined,
                        hasMetadata: false,
                        flowId,
                        entryPoint: "taskDropdown",
                        projectId,
                        projectName: projectName ?? "",
                        mode,
                        datasetId: form.getValues("dataset") ?? undefined,
                        datasetName: (() => {
                          const did = form.getValues("dataset");
                          const dn = did
                            ? datasetNameMap?.get(did)?.name
                            : undefined;
                          return typeof dn === "string" ? dn : undefined;
                        })(),
                        sourcePage:
                          typeof window !== "undefined"
                            ? window.location.pathname
                            : undefined,
                        source: "web" as const,
                      });
                    }
                  }}
                >
                  <Button size="sm" className="mt-2 w-full" Icon={Plus}>
                    Task
                  </Button>
                </AddTask>
              )}
            </div>
          )}
        />
        <FormField
          control={form.control}
          name="dataset"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex justify-between">
                Dataset
                {mode === "single" && (
                  <Link
                    className="text-xs text-accent-600"
                    href={`${getProjectLink({ orgName: org.name, projectName })}/datasets`}
                  >
                    Create dataset
                  </Link>
                )}
              </FormLabel>
              <FormControl>
                <div className="flex">
                  <DatasetDropdown
                    datasets={orgDatasets}
                    open={datasetDropdownOpen}
                    setOpen={(open) => {
                      setDatasetDropdownOpen(open);
                    }}
                    selectedDatasetId={field.value ?? undefined}
                    onSelectDataset={(dataset) => {
                      field.onChange(
                        dataset.id === undefined ? null : dataset.id,
                        {
                          shouldValidate: true,
                          shouldDirty: true,
                        },
                      );
                      if (dataset.id && projectId) {
                        track("experimentElementAdd", {
                          elementType: "dataset",
                          elementId: dataset.id,
                          elementName: dataset.name,
                          datasetType:
                            dataset.name &&
                            dataset.name.toLowerCase().includes("synthetic")
                              ? "synthetic"
                              : undefined,
                          hasMetadata: false,
                          flowId,
                          entryPoint: "datasetDropdown",
                          projectId,
                          projectName: projectName ?? "",
                          mode,
                          datasetId: dataset.id,
                          datasetName: dataset.name,
                          sourcePage:
                            typeof window !== "undefined"
                              ? window.location.pathname
                              : undefined,
                          source: "web" as const,
                        });
                      }
                    }}
                  >
                    {isOrgDatasetsLoading ? (
                      <Skeleton className="h-10 w-full" />
                    ) : (
                      <Button
                        size="sm"
                        className={cn(
                          "flex h-auto min-h-10 flex-1 justify-between px-3 py-2 text-left",
                          {
                            "rounded-r-none": !!field.value,
                            "font-normal text-primary-500": !field.value,
                          },
                        )}
                        isDropdown
                      >
                        <span className="flex flex-1 items-center gap-2">
                          <span className="flex h-[20px] flex-none items-center">
                            <Database className="size-3 text-fuchsia-600 dark:text-fuchsia-400" />
                          </span>
                          {field.value ? (
                            <>
                              <span className="flex-1">
                                {datasetNameMap?.get(field.value)?.name ??
                                  "Dataset"}
                              </span>
                              {datasetNameMap?.get(field.value)
                                ?.projectName && (
                                <span className="max-w-36 flex-none truncate pl-1 text-xs font-normal text-primary-500">
                                  {
                                    datasetNameMap?.get(field.value)
                                      ?.projectName
                                  }
                                </span>
                              )}
                            </>
                          ) : (
                            "Select a dataset"
                          )}
                        </span>
                      </Button>
                    )}
                  </DatasetDropdown>
                  {field.value && (
                    <Button
                      size="sm"
                      Icon={XIcon}
                      className="h-auto min-h-10 w-7 rounded-l-none border-l-0 p-0 text-primary-600"
                      onClick={() => field.onChange(null)}
                    />
                  )}
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="scorers"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex justify-between">
                Scorers
                <Link
                  className="text-xs text-accent-600"
                  href={`${getProjectLink({ orgName: org.name, projectName })}/scorers/new`}
                >
                  Create scorer
                </Link>
              </FormLabel>
              <FormControl>
                {projectId && (
                  <ScorersDropdown
                    projectId={projectId}
                    projectName={projectName}
                    savedScorers={field.value}
                    updateScorers={async (scorers) => {
                      // Determine newly added scorers
                      const prevKeys = new Set(
                        (field.value ?? []).map((s) =>
                          s.type === "function" ? s.id : s.name,
                        ),
                      );
                      const added = (scorers ?? []).filter(
                        (s) =>
                          !prevKeys.has(s.type === "function" ? s.id : s.name),
                      );
                      // Track each newly added scorer
                      for (const s of added) {
                        const elementId = s.type === "function" ? s.id : s.name;
                        const elementName =
                          s.type === "function" ? undefined : s.name;
                        track("experimentElementAdd", {
                          elementType: "scorer",
                          elementId,
                          elementName,
                          scorerType: s.type === "global" ? "global" : "custom",
                          hasMetadata: false,
                          flowId,
                          entryPoint: "scorerDropdown",
                          projectId,
                          projectName: projectName ?? "",
                          mode,
                          datasetId: form.getValues("dataset") ?? undefined,
                          datasetName: (() => {
                            const did = form.getValues("dataset");
                            const dn = did
                              ? datasetNameMap?.get(did)?.name
                              : undefined;
                            return typeof dn === "string" ? dn : undefined;
                          })(),
                          sourcePage:
                            typeof window !== "undefined"
                              ? window.location.pathname
                              : undefined,
                          source: "web" as const,
                        });
                      }
                      field.onChange(scorers);
                      return Promise.resolve(null);
                    }}
                    disableScorersThatRequireConfiguration
                  />
                )}
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div>
          <CollapsibleSection title="Advanced" defaultCollapsed>
            <div className="flex flex-col gap-6">
              <FormField
                control={form.control}
                name="metadata"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Metadata</FormLabel>
                    <FormControl>
                      <DataTextEditor
                        formatOnBlur
                        allowedRenderOptions={["json", "yaml"]}
                        value={field.value}
                        onError={onMetadataError}
                        onChange={(v) => {
                          field.onChange(v, {
                            shouldValidate: true,
                            shouldDirty: true,
                          });
                        }}
                        placeholder="Enter metadata (optional)"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="trialCount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Trial count</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="number"
                        min={1}
                        onWheel={(e) => e.currentTarget.blur()}
                        value={field.value ?? ""}
                        placeholder="Enter trial count (optional)"
                        onChange={(e) => {
                          const value = e.target.value;
                          field.onChange(value === "" ? null : Number(value), {
                            shouldValidate: true,
                            shouldDirty: true,
                          });
                        }}
                      />
                    </FormControl>
                    <FormDescription>
                      The number of times to run the evaluator per input. This
                      is useful for evaluating applications that have
                      non-deterministic behavior and gives you both a stronger
                      aggregate measure and a sense of the variance in the
                      results.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="maxConcurrency"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Max concurrency</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="number"
                        min={1}
                        onWheel={(e) => e.currentTarget.blur()}
                        value={field.value ?? ""}
                        placeholder="Enter max concurrency (optional)"
                        onChange={(e) => {
                          const value = e.target.value;
                          field.onChange(value === "" ? null : Number(value), {
                            shouldValidate: true,
                            shouldDirty: true,
                          });
                        }}
                      />
                    </FormControl>
                    <FormDescription>
                      The maximum number of tasks/scorers that will be run
                      concurrently. This is useful for avoiding rate limits from
                      AI providers.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="strict"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex w-full items-center gap-2">
                      <FormControl>
                        <Switch
                          {...field}
                          value={undefined}
                          checked={field.value === true ? true : false}
                          onCheckedChange={(checked) =>
                            field.onChange(checked ?? false)
                          }
                        />
                      </FormControl>
                      <FormLabel>Strict variables</FormLabel>
                    </div>
                    <FormDescription>
                      Fail evaluation if the input does not include all
                      referenced variables
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CollapsibleSection>
        </div>

        <div
          className={cn(
            "flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",
            footerClassName,
          )}
        >
          {onClose && (
            <DialogClose asChild>
              <Button size="sm">Cancel</Button>
            </DialogClose>
          )}
          <Button
            size="sm"
            variant="primary"
            type="submit"
            disabled={!form.formState.isValid}
            isLoading={form.formState.isSubmitting}
          >
            Create
          </Button>
        </div>
      </form>
    </Form>
  );
};

const runEval = async ({
  proxyUrl,
  evalRequest,
  sessionToken,
  orgName,
  org,
  onGoToExperiment,
  mutateExperiments,
  analyticsCtx,
  formData,
  orgDatasets,
  scorerFunctions,
  promptsByProjectSorted,
  track,
}: {
  proxyUrl: string;
  evalRequest: EvalRequest;
  sessionToken: BtSessionToken;
  orgName: string;
  org: { id?: string };
  onGoToExperiment: (experimentUrl: string) => void;
  mutateExperiments: () => void;
  analyticsCtx?: {
    common: {
      flowId: string;
      projectId: string;
      projectName: string;
      mode: "playground" | "single";
      datasetId?: string | null;
      sourcePage?: string;
    };
    modelProvider?: string;
    modelName?: string;
    temperature?: number;
    experimentName: string;
  };
  formData: z.infer<typeof createExperimentFormSchema>;
  orgDatasets: ProjectContextDataset[];
  scorerFunctions: Record<string, UIFunction>;
  promptsByProjectSorted: Array<{
    projectId: string;
    prompts: Array<{
      id: string;
      metadata?: Record<string, unknown> | null;
    }>;
  }>;
  track: <K extends EventName>(event: K, properties: EventProps<K>) => void;
}) => {
  if (sessionToken === "loading") {
    toast.error("Session token still loading. Try again in a few seconds.");
    return;
  }

  const toastId = toast.loading(
    evalRequest.type === "api"
      ? `Creating experiment ${evalRequest.request.experiment_name}`
      : `Creating remote experiment ${evalRequest.experiment_name}`,
    {
      duration: Infinity,
    },
  );

  let result: Response | undefined = undefined;
  let error: string | undefined = undefined;
  try {
    result = await issueEvalRequest({
      evalRequest,
      proxyUrl,
      orgName,
      sessionToken: sessionToken,
    });
  } catch (e) {
    error = `${e}`;
  }

  if (error || !result || !result.ok || !result.body) {
    toast.error("Failed to create experiment", {
      description:
        error ??
        (result ? `${result.status}: ${await result.text()}` : "Unknown error"),
      closeButton: true,
    });
    toast.dismiss(toastId);
    return;
  }

  const resultStream = result.body
    .pipeThrough(new TextDecoderStream())
    .pipeThrough(fastEventSourceParser());

  const reader = resultStream.getReader();
  let showedFreeTierError = false;
  let hasError = false;
  const createdExperiments: Array<{
    id: string;
    name: string;
    taskId?: string;
    sourceElementId?: string;
    taskType?: "prompt" | "function" | "agent" | "remote_eval";
    sourceType?: "inline" | "saved_prompt" | "function" | "remote";
    hasMetadata?: boolean;
  }> = [];
  while (true) {
    const { done, value } = await reader.read();
    if (done) {
      break;
    }

    switch (value.event) {
      case "start": {
        const metadata: ExperimentSummary = JSON.parse(value.data);
        toast.loading(`${metadata.experimentName} started`, {
          action: (
            <Button
              size="xs"
              onClick={() => {
                if (!metadata.experimentUrl) return;
                onGoToExperiment(metadata.experimentUrl);
              }}
            >
              Go to experiment
            </Button>
          ),
          id: toastId,
        });
        mutateExperiments();
        break;
      }
      case "summary": {
        const metadata: ExperimentSummary = JSON.parse(value.data);
        const completedToast = hasError ? toast.error : toast.success;
        completedToast(`${metadata.experimentName} completed`, {
          action: (
            <Button
              size="xs"
              onClick={() => {
                if (!metadata.experimentUrl) return;
                onGoToExperiment(metadata.experimentUrl);
              }}
            >
              Go to experiment
            </Button>
          ),
          closeButton: true,
        });
        // Accumulate experiment for tracking at the end
        // Find the task that corresponds to this experiment by name
        // The experiment name might have a suffix (e.g., "prompt 1-8d0b180b"), so we need to match by base name
        const baseExperimentName = metadata.experimentName.replace(
          /-v\d+$|-variant\d+$|-\d+$/,
          "",
        ); // Remove version/variant suffixes
        const currentTask =
          formData.tasks.find((task) => task.name === baseExperimentName) ||
          formData.tasks[0];
        createdExperiments.push({
          id: metadata.experimentId || "",
          name: metadata.experimentName,
          taskId: currentTask?.versionData?.id,
          sourceElementId: currentTask?.versionData?.id,
          taskType: (() => {
            const objectType = currentTask?.versionData?.objectType;
            if (
              objectType === "project_prompts" ||
              objectType === "org_prompts"
            ) {
              return "prompt";
            } else if (
              objectType === "project_functions" ||
              objectType === "org_functions"
            ) {
              return "function";
            } else {
              return "remote_eval";
            }
          })(),
          sourceType: "saved_prompt" as const,
          hasMetadata: !!(
            currentTask?.metadata &&
            Object.keys(currentTask.metadata).length > 0
          ),
        });
        toast.dismiss(toastId);
        break;
      }
      case "done": {
        toast.dismiss(toastId);
        break;
      }
      case "progress": {
        // skip progress events
        break;
      }
      case "error": {
        hasError = true;
        let errorText = `${JSON.parse(value.data)}`;
        if (
          errorText.includes("You have reached your plan limits.") ||
          // prior dataplane errors
          errorText.includes("You have hit your free tier limit")
        ) {
          console.log("Showing plan error", showedFreeTierError);
          if (showedFreeTierError) {
            // Only show this pesky error once.
            continue;
          }
          errorText =
            "You have hit your plan limit. While you can continue using the playground, you must upgrade to run experiments.";
          showedFreeTierError = true;
        }
        toast.dismiss(toastId);
        toast.error(errorText, {
          closeButton: true,
        });
        break;
      }
      default: {
        toast.dismiss(toastId);
      }
    }
  }

  // Track all experiments created in this flow (if any were created successfully)
  if (createdExperiments.length > 0 && !hasError) {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    try {
      // Use the first experiment's task information for main event properties
      const firstExperimentTask =
        formData.tasks.find(
          (task) => task.name === createdExperiments[0]?.name,
        ) || formData.tasks[0];

      // Extract model information from the actual prompt configuration
      // The model info is stored in the prompt, not in task metadata
      const promptId =
        "function_id" in firstExperimentTask
          ? firstExperimentTask.function_id
          : undefined;
      const prompt = promptId
        ? promptsByProjectSorted
            .find(
              (p) => p.projectId === firstExperimentTask.versionData?.projectId,
            )
            ?.prompts.find((p) => p.id === promptId)
        : undefined;

      // Model information can be stored in different places:
      // 1. For saved prompts: prompt_data.options.model
      // 2. For playground prompts: task.metadata.model
      const promptOptions:
        | { model?: string; params?: Record<string, unknown> }
        | undefined =
        prompt &&
        "prompt_data" in prompt &&
        prompt.prompt_data &&
        typeof prompt.prompt_data === "object" &&
        "options" in prompt.prompt_data &&
        prompt.prompt_data.options
          ? prompt.prompt_data.options
          : undefined;
      const taskMetadata = firstExperimentTask.metadata;

      // Try to get model from prompt options first, then from task metadata
      const model =
        typeof promptOptions?.model === "string"
          ? promptOptions!.model
          : typeof taskMetadata?.model === "string"
            ? taskMetadata.model
            : undefined;

      // Extract model provider from the model name (e.g., "gpt-4" -> "openai")
      const modelProvider = model ? extractModelProvider(model) : undefined;

      // Try to get temperature from prompt options first, then from task metadata
      const temperature =
        typeof promptOptions?.params?.temperature === "number"
          ? promptOptions!.params!.temperature
          : typeof taskMetadata?.temperature === "number"
            ? taskMetadata.temperature
            : undefined;

      // Try to get language from prompt options first, then from task metadata
      const language =
        (typeof promptOptions?.params?.language === "string"
          ? promptOptions!.params!.language
          : undefined) ||
        (typeof promptOptions?.params?.programming_language === "string"
          ? promptOptions!.params!.programming_language
          : undefined) ||
        (typeof promptOptions?.params?.code_language === "string"
          ? promptOptions!.params!.code_language
          : undefined) ||
        (typeof taskMetadata?.language === "string"
          ? taskMetadata.language
          : undefined) ||
        (typeof taskMetadata?.programming_language === "string"
          ? taskMetadata.programming_language
          : undefined) ||
        (typeof taskMetadata?.code_language === "string"
          ? taskMetadata.code_language
          : undefined);

      // Get dataset info
      const selectedDataset = formData.dataset
        ? orgDatasets.find((d) => d.id === formData.dataset)
        : undefined;
      const datasetName = selectedDataset?.name;

      // Prepare scorer details
      const scorerDetails = formData.scorers.map((scorer) => ({
        scorerId: scorer.type === "function" ? scorer.id : undefined,
        scorerName:
          scorer.type === "global"
            ? scorer.name
            : String(scorerFunctions[scorer.id]?.name || "Unknown"),
        /* eslint-disable @typescript-eslint/consistent-type-assertions */
        scorerType: (scorer.type === "global" ? "global" : "custom") as
          | "global"
          | "custom",
        /* eslint-enable @typescript-eslint/consistent-type-assertions */
        hasMetadata: false,
      }));

      const clientTrackingData: EventProps<"experimentCreate"> = {
        experimentId: createdExperiments[0]?.id || "",
        experimentName: analyticsCtx?.experimentName || "New Experiment",
        entryPoint: analyticsCtx?.common.sourcePage,
        flowId: analyticsCtx?.common.flowId || "",
        projectId: analyticsCtx?.common.projectId || "",
        projectName: analyticsCtx?.common.projectName || "",
        orgId: org.id || undefined,
        mode: analyticsCtx?.common.mode,
        datasetId: formData.dataset ?? undefined,
        datasetName,
        method: "ui",
        outcome: "success",
        experimentDetails: createdExperiments,
        scorerDetails,
        sourcePage: analyticsCtx?.common.sourcePage,
        modelProvider,
        model,
        temperature,
        language,
        source: "web",
      };

      track("experimentCreate", clientTrackingData);

      // Also track server-side event
      try {
        const { trackExperimentCreate } = await import(
          "#/utils/experiment-analytics"
        );
        await trackExperimentCreate({
          experimentId: createdExperiments[0]?.id || "",
          experimentName: analyticsCtx?.experimentName || "New Experiment",
          entryPoint: analyticsCtx?.common.sourcePage,
          flowId: analyticsCtx?.common.flowId || "",
          projectId: analyticsCtx?.common.projectId || "",
          projectName: analyticsCtx?.common.projectName || "",
          orgId: org.id || undefined,
          mode: analyticsCtx?.common.mode,
          datasetId: formData.dataset ?? undefined,
          datasetName,
          method: "ui",
          outcome: "success" as const,
          experimentDetails: createdExperiments,
          scorerDetails,
          sourcePage: analyticsCtx?.common.sourcePage,
          source: "server",
        });
      } catch (serverError) {
        console.error(
          "Failed to track server-side experiment create event:",
          serverError,
        );
      }
    } catch {}
  } else if (hasError) {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    try {
      // Extract model information from first task for error tracking
      const firstTask = formData.tasks[0];
      const promptId =
        "function_id" in firstTask ? firstTask.function_id : undefined;
      const prompt = promptId
        ? promptsByProjectSorted
            .find((p) => p.projectId === firstTask.versionData?.projectId)
            ?.prompts.find((p) => p.id === promptId)
        : undefined;

      // Model information can be stored in different places:
      // 1. For saved prompts: prompt_data.options.model
      // 2. For playground prompts: task.metadata.model
      const promptOptions:
        | { model?: string; params?: Record<string, unknown> }
        | undefined =
        prompt &&
        "prompt_data" in prompt &&
        prompt.prompt_data &&
        typeof prompt.prompt_data === "object" &&
        "options" in prompt.prompt_data &&
        prompt.prompt_data.options
          ? prompt.prompt_data.options
          : undefined;
      const taskMetadata = firstTask.metadata;

      // Try to get model from prompt options first, then from task metadata
      const model =
        typeof promptOptions?.model === "string"
          ? promptOptions!.model
          : typeof taskMetadata?.model === "string"
            ? taskMetadata.model
            : undefined;
      const modelProvider = model ? extractModelProvider(model) : undefined;

      // Try to get temperature from prompt options first, then from task metadata
      const temperature =
        typeof promptOptions?.params?.temperature === "number"
          ? promptOptions!.params!.temperature
          : typeof taskMetadata?.temperature === "number"
            ? taskMetadata.temperature
            : undefined;

      // Try to get language from prompt options first, then from task metadata
      const language =
        (typeof promptOptions?.params?.language === "string"
          ? promptOptions!.params!.language
          : undefined) ||
        (typeof promptOptions?.params?.programming_language === "string"
          ? promptOptions!.params!.programming_language
          : undefined) ||
        (typeof promptOptions?.params?.code_language === "string"
          ? promptOptions!.params!.code_language
          : undefined) ||
        (typeof taskMetadata?.language === "string"
          ? taskMetadata.language
          : undefined) ||
        (typeof taskMetadata?.programming_language === "string"
          ? taskMetadata.programming_language
          : undefined) ||
        (typeof taskMetadata?.code_language === "string"
          ? taskMetadata.code_language
          : undefined);

      const errorTrackingData: EventProps<"experimentCreate"> = {
        experimentId: "",
        experimentName: firstTask?.name || "New Experiment",
        entryPoint: analyticsCtx?.common.sourcePage,
        flowId: analyticsCtx?.common.flowId || "",
        projectId: analyticsCtx?.common.projectId || "",
        projectName: analyticsCtx?.common.projectName || "",
        orgId: org.id || undefined,
        mode: analyticsCtx?.common.mode,
        datasetId: formData.dataset ?? undefined,
        datasetName: formData.dataset
          ? orgDatasets.find((d) => d.id === formData.dataset)?.name
          : undefined,
        method: "ui",
        outcome: "error",
        errorMessage: "Experiment creation failed",
        sourcePage: analyticsCtx?.common.sourcePage,
        modelProvider,
        model,
        temperature,
        language,
        source: "web",
      };

      track("experimentCreate", errorTrackingData);

      // Also track server-side error event
      try {
        const { trackExperimentCreate } = await import(
          "#/utils/experiment-analytics"
        );
        await trackExperimentCreate({
          experimentId: "",
          experimentName: firstTask?.name || "New Experiment",
          entryPoint: analyticsCtx?.common.sourcePage,
          flowId: analyticsCtx?.common.flowId || "",
          projectId: analyticsCtx?.common.projectId || "",
          projectName: analyticsCtx?.common.projectName || "",
          orgId: org.id || undefined,
          mode: analyticsCtx?.common.mode,
          datasetId: formData.dataset ?? undefined,
          datasetName: formData.dataset
            ? orgDatasets.find((d) => d.id === formData.dataset)?.name
            : undefined,
          method: "ui",
          outcome: "error" as const,
          errorMessage: "Experiment creation failed",
          sourcePage: analyticsCtx?.common.sourcePage,
          source: "server",
        });
      } catch (serverError) {
        console.error(
          "Failed to track server-side experiment create error event:",
          serverError,
        );
      }
    } catch {}
  }
};

export type EvalRequest =
  | {
      type: "api";
      request: RunEvalRequest;
    }
  | {
      type: "remote";
      request: RemoteEvalData;
      data: RunEvalRequest["data"];
      scores: {
        function_id: FunctionId;
        name: string;
      }[];
      experiment_name?: string;
      project_id?: string;
      parent: RunEvalRequest["parent"];
      stream: boolean;
    };

export async function issueEvalRequest({
  evalRequest,
  proxyUrl,
  orgName,
  sessionToken,
  signal,
}: {
  evalRequest: EvalRequest;
  proxyUrl: string;
  orgName: string;
  sessionToken: LoadedBtSessionToken;
  signal?: AbortSignal;
}): Promise<Response | undefined> {
  const { sessionHeaders, sessionExtraFetchProps } =
    sessionFetchProps(sessionToken);
  return await (evalRequest.type === "api"
    ? fetch(_urljoin(normalizeProxyUrlBase(proxyUrl), "function", "eval"), {
        method: "POST",
        body: JSON.stringify(evalRequest.request),
        mode: "cors",
        headers: {
          "content-type": "application/json",
          "x-bt-org-name": orgName,
          ...sessionHeaders,
        },
        ...sessionExtraFetchProps,
        signal,
      })
    : fetch(_urljoin(evalRequest.request.endpoint, "eval"), {
        method: "POST",
        body: JSON.stringify({
          name: evalRequest.request.eval_name,
          parameters: evalRequest.request.parameters,
          data: evalRequest.data,
          scores: evalRequest.scores,
          project_id: evalRequest.project_id,
          experiment_name: evalRequest.experiment_name,
          parent: evalRequest.parent,
          stream: evalRequest.stream,
        }),
        mode: "cors",
        headers: {
          "content-type": "application/json",
          "x-bt-org-name": orgName,
          ...sessionHeaders,
        },
        ...sessionExtraFetchProps,
        signal,
      }));
}

// feeding smaller chunks into the parser shows better performance
export function fastEventSourceParser() {
  let eventSourceParser: EventSourceParser | null = null;
  return new TransformStream<string, ParsedEvent>({
    start(controller) {
      eventSourceParser = createParser(
        (event: ParsedEvent | ReconnectInterval) => {
          if (event.type === "reconnect-interval") {
            return;
          }
          controller.enqueue(event);
        },
      );
    },
    transform(chunk) {
      const chunks = chunk.split("\n");
      for (let i = 0; i < chunks.length; i++) {
        const line = chunks[i];
        eventSourceParser?.feed(line + (i === chunks.length - 1 ? "" : "\n"));
      }
    },
  });
}
