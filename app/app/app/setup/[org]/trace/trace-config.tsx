import {
  <PERSON>lama<PERSON>nde<PERSON><PERSON>ogo,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>loop<PERSON>ogo,
  VercelLogo,
  OpenrouterLogo,
  LanggraphLogo,
  OpenTelemetryLogo,
  LiteLLMLogo,
  StrandsLogo,
  LangchainLogo,
  PydanticLogo,
  CloudflareLogo,
  CrewAILogo,
  AutogenLogo,
  <PERSON><PERSON>ogo,
  <PERSON>Kit<PERSON>ogo,
} from "../../../[org]/onboarding-logos";

export const starterProjectName = "My Project";

export enum Language {
  TYPESCRIPT = "Typescript",
  PYTHON = "Python",
}

export enum VercelFramework {
  NEXT = "next",
  NODE = "node",
}

export type CodeExample = {
  snippets: string[];
  extraDependencies?: string;
};

export interface IntegrationConfig {
  name: string;
  icon?: React.ReactNode;
  environmentVariables?: Record<string, string>;
  codeExamples: Partial<
    Record<
      Language,
      CodeExample | Partial<Record<VercelFramework, CodeExample>>
    >
  >;
  supportedLanguages: Language[];
  docsUrl?: string;
}

export enum SdkIntegration {
  ANTHROPIC = "anthropic",
  OPENAI = "openai",
  CUSTOM = "custom",
  VERCEL = "vercel",
  CLAUDE_AGENT_SDK = "claude-agent-sdk",
  OPENROUTER = "openrouter",
  TRACELOOP = "traceloop",
  LLAMA_INDEX = "llamaindex",
  MASTRA = "mastra",
  LANGCHAIN = "langchain",
  LANGGRAPH = "langgraph",
  OPENTELEMETRY = "opentelemetry",
  LITELLM = "litellm",
  STRANDS = "strands",
  PYDANTIC = "pydantic",
  CLOUDFLARE = "cloudflare",
  CREWAI = "crewai",
  AUTOGEN = "autogen",
  LIVEKIT = "livekit",
}

export const getIntegrationConfigs = (
  apiKey?: string,
  apiUrl?: string,
  projectName?: string,
): Record<SdkIntegration, IntegrationConfig> => {
  const dynamicApiUrl =
    apiUrl && apiUrl !== "https://api.braintrust.dev"
      ? { BRAINTRUST_API_URL: apiUrl }
      : undefined;
  return {
    [SdkIntegration.ANTHROPIC]: {
      name: "Anthropic",
      environmentVariables: {
        ...dynamicApiUrl,
        BRAINTRUST_API_KEY: apiKey || "<YOUR_BRAINTRUST_API_KEY>",
        ANTHROPIC_API_KEY: "<YOUR_ANTHROPIC_API_KEY>",
      },
      codeExamples: {
        [Language.PYTHON]: {
          snippets: [
            `import anthropic
from braintrust import init_logger, wrap_anthropic

# Initialize the Braintrust logger and Anthropic client
client = wrap_anthropic(
  anthropic.Anthropic(api_key=os.getenv("ANTHROPIC_API_KEY"))
)
logger = init_logger(project="${projectName}")

# Enter your prompt call here
result = client.messages.create(
    model="claude-sonnet-4-20250514",
    messages=[{"role": "user", "content": "What is 1+1?"}],
    max_tokens=1024,
)

print(result)`,
          ],
          extraDependencies: "anthropic",
        },
        [Language.TYPESCRIPT]: {
          snippets: [
            `import Anthropic from "@anthropic-ai/sdk";
import { wrapAnthropic, initLogger } from "braintrust";

// Initialize the Braintrust logger and Anthropic client
const client = wrapAnthropic(
  new Anthropic({
    apiKey: process.env.ANTHROPIC_API_KEY,
  }),
);
const logger = initLogger({
  projectName: "${projectName}",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

// Enter your prompt call here
async function main() {
  const result = await client.messages.create({
    messages: [
      {
        role: "user",
        content: "What is 1+1?",
      },
    ],
    model: "claude-sonnet-4-20250514",
    max_tokens: 1024,
  });
}

main();`,
          ],
          extraDependencies: "@anthropic-ai/sdk",
        },
      },
      supportedLanguages: [Language.PYTHON, Language.TYPESCRIPT],
      docsUrl: "/docs/providers/anthropic#trace-with-anthropic",
    },
    [SdkIntegration.OPENAI]: {
      name: "OpenAI",
      environmentVariables: {
        ...dynamicApiUrl,
        BRAINTRUST_API_KEY: apiKey || "<YOUR_BRAINTRUST_API_KEY>",
        OPENAI_API_KEY: "<YOUR_OPENAI_API_KEY>",
      },
      codeExamples: {
        [Language.PYTHON]: {
          snippets: [
            `import openai
from braintrust import init_logger, wrap_openai

# Initialize the Braintrust logger and OpenAI client
client = wrap_openai(
  openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
)
logger = init_logger(project="${projectName}")

# Enter your prompt call here
result = client.chat.completions.create(
    model="gpt-4o",
    messages=[{"role": "user", "content": "What is 1+1?"}]
)

print(result)`,
          ],
          extraDependencies: "openai",
        },
        [Language.TYPESCRIPT]: {
          snippets: [
            `import { OpenAI } from "openai";
import { initLogger, wrapOpenAI } from "braintrust";

// Initialize the Braintrust logger and OpenAI client
const client = wrapOpenAI(
  new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  }),
);
const logger = initLogger({
  projectName: "${projectName}",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

// Enter your prompt call here
async function main() {
  const result = await client.chat.completions.create({
    model: "gpt-4o",
    messages: [{ role: "user", content: "What is 1+1?" }],
  });
	console.log(result)
}

main();`,
          ],
          extraDependencies: "openai",
        },
      },
      supportedLanguages: [Language.PYTHON, Language.TYPESCRIPT],
      docsUrl: "/docs/providers/openai#trace-with-openai",
    },
    [SdkIntegration.TRACELOOP]: {
      icon: <TraceloopLogo color="currentColor" size={36} />,
      name: "TraceLoop",
      environmentVariables: {
        BRAINTRUST_API_URL: apiUrl || "https://api.braintrust.dev",
        BRAINTRUST_API_KEY: apiKey || "<YOUR_BRAINTRUST_API_KEY>",
        PROJECT_NAME: projectName || "<YOUR_PROJECT_NAME>",
        OPENAI_API_KEY: "<YOUR_OPENAI_API_KEY>",
      },
      codeExamples: {
        [Language.PYTHON]: {
          snippets: [
            `from openai import OpenAI
from traceloop.sdk import Traceloop
from traceloop.sdk.decorators import workflow

# Set the TraceLoop environment variables
os.environ["TRACELOOP_HEADERS"] = (
    f"Authorization=Bearer {BRAINTRUST_API_KEY},"
    f"x-bt-parent=project_name:{PROJECT_NAME}"
)
os.environ["TRACELOOP_BASE_URL"] = f"{BRAINTRUST_API_URL}/otel"

# Initialize Traceloop client
Traceloop.init(disable_batch=True)

# This example uses OpenAI, but use your preferred LLM client
client = OpenAI()


@workflow(name="story")
def run_story_stream(client):
    # Enter your prompt call here
    completion = client.chat.completions.create(
        model="gpt-4o-mini",
        messages=[{"role": "user", "content": "Tell me a short story about LLM evals."}],
    )
    return completion.choices[0].message.content


print(run_story_stream(client))`,
          ],
          extraDependencies: "traceloop-sdk openai",
        },
        [Language.TYPESCRIPT]: {
          snippets: [
            `import { OpenAI } from "openai";
import { Traceloop } from "@traceloop/sdk";
import { workflow } from "@traceloop/sdk/decorators";

// Set the TraceLoop environment variables
process.env.TRACELOOP_HEADERS =
  \`Authorization=Bearer \${process.env.BRAINTRUST_API_KEY},x-bt-parent=project_name:\${process.env.PROJECT_NAME}\`;

process.env.TRACELOOP_BASE_URL = \`\${process.env.BRAINTRUST_API_URL}/otel\`;

// Initialize Traceloop client
Traceloop.init({ disableBatch: true });

// This example uses OpenAI, but use your preferred LLM client
const client = new OpenAI();

@workflow({ name: "story" })
async function runStoryStream(client: OpenAI) {
  // Enter your prompt call here
  const completion = await client.chat.completions.create({
    model: "gpt-4o-mini",
    messages: [{ role: "user", content: "Tell me a short story about LLM evals." }],
  });
  return completion.choices[0].message.content;
}

console.log(await runStoryStream(client));`,
          ],
          extraDependencies: "@traceloop/sdk openai",
        },
      },
      supportedLanguages: [Language.PYTHON, Language.TYPESCRIPT],
      docsUrl: "/docs/integrations/opentelemetry#traceloop",
    },
    [SdkIntegration.MASTRA]: {
      name: "Mastra",
      icon: <MastraLogo size={36} />,
      environmentVariables: {
        ...dynamicApiUrl,
        BRAINTRUST_API_KEY: apiKey || "<Your Braintrust API Key>",
        OPENAI_API_KEY: "<Your OpenAI API Key>",
        PROJECT_NAME: projectName || "<Your Project Name>",
      },
      codeExamples: {
        [Language.TYPESCRIPT]: {
          snippets: [
            `import { openai } from "@ai-sdk/openai";
import { Agent } from "@mastra/core/agent";
import { Mastra } from "@mastra/core/mastra";
import { BraintrustExporter } from "@mastra/braintrust";


// Create mastra agent
const agent = new Agent({
  name: "Demo Assistant",
  instructions:  "You help with geography. Be accurate and helpful.",
  model: openai("gpt-4o-mini"),
});

// Create mastra instance with braintrust exporter
const mastra = new Mastra({
  agents: { agent },
  observability: {
    configs: {
      braintrust: {
        serviceName: "demo-project",
        exporters: [
          new BraintrustExporter({
            apiKey: process.env.BRAINTRUST_API_KEY,
            endpoint: process.env.BRAINTRUST_API_URL,
            projectName: process.env.PROJECT_NAME,
          }),
        ],
      },
    },
  },
});

// Run the agent
async function main() {
  const agent = mastra.getAgent("agent");
  if (!agent) {
    throw new Error("agent not found");
  }

  const response = await agent.generateVNext(
    "What's the capital of France?",
  );
  console.log("response:", response.text);
}

main();`,
          ],
          extraDependencies: "@mastra/core @mastra/braintrust @ai-sdk/openai",
        },
      },
      supportedLanguages: [Language.TYPESCRIPT],
      docsUrl: "/docs/integrations/mastra",
    },
    [SdkIntegration.LLAMA_INDEX]: {
      name: "LlamaIndex",
      icon: <LlamaIndexLogo color="currentColor" size={36} />,
      environmentVariables: {
        BRAINTRUST_API_URL: apiUrl || "https://api.braintrust.dev",
        BRAINTRUST_API_KEY: apiKey || "<Your Braintrust API Key>",
        PROJECT_NAME: projectName || "<Your Project Name>",
        OPENAI_API_KEY: "<Your OpenAI API Key>",
      },
      codeExamples: {
        [Language.PYTHON]: {
          snippets: [
            `import llama_index.core
from llama_index.core.llms import ChatMessage
from llama_index.llms.openai import OpenAI

# Set the OTel environment variables
os.environ["OTEL_EXPORTER_OTLP_HEADERS"] = (
    f"Authorization=Bearer {BRAINTRUST_API_KEY}" + f"x-bt-parent=project_name:{PROJECT_NAME}"
)

llama_index.core.set_global_handler("arize_phoenix", endpoint=f"{BRAINTRUST_API_URL}/otel/v1/traces")

messages = [
    ChatMessage(role="system", content="Speak like a pirate. ARRR!"),
    ChatMessage(role="user", content="What do llamas sound like?"),
]

# This example uses OpenAI, but use your preferred LLM client
result = OpenAI().chat(messages)
print(result)`,
          ],
          extraDependencies:
            "braintrust[otel] llama-index-core llama-index-llms-openai",
        },
      },
      supportedLanguages: [Language.PYTHON],
      docsUrl: "/docs/integrations/opentelemetry#llamaindex",
    },
    [SdkIntegration.VERCEL]: {
      icon: <VercelLogo size={36} />,
      name: "AI SDK",
      environmentVariables: {
        ...dynamicApiUrl,
        BRAINTRUST_API_KEY: apiKey || "<Your Braintrust API Key>",
        OPENAI_API_KEY: "<Your OpenAI API Key>",
        PROJECT_NAME: projectName || "<Your Project Name>",
      },
      codeExamples: {
        [Language.TYPESCRIPT]: {
          [VercelFramework.NEXT]: {
            snippets: [
              `// In your instrumentation.ts file
import { registerOTel } from "@vercel/otel";
import { BraintrustExporter } from "braintrust";

export function register() {
  registerOTel({
    serviceName: "my-braintrust-app",
    traceExporter: new BraintrustExporter({
      parent: \`project_name:\${process.env.PROJECT_NAME}\`,
      filterAISpans: true, // Only send AI-related spans
    }),
  });
}`,
              `// Example usage in server side file
import { generateText } from "ai";
import { openai } from "@ai-sdk/openai";

// This example uses OpenAI, but use your preferred LLM client
const result = await generateText({
  model: openai("gpt-4o-mini"),
  prompt: "What is 2 + 2?",
  experimental_telemetry: {
    isEnabled: true,
    metadata: {
      query: "weather",
      location: "San Francisco",
    },
  },
});`,
            ],
            extraDependencies: "ai @ai-sdk/openai @vercel/otel",
          },
          [VercelFramework.NODE]: {
            snippets: [
              `import { NodeSDK } from "@opentelemetry/sdk-node";
import { generateText, tool } from "ai";
import { openai } from "@ai-sdk/openai";
import { z } from "zod";
import { BraintrustSpanProcessor } from "braintrust";

const sdk = new NodeSDK({
  spanProcessors: [
    new BraintrustSpanProcessor({
      parent: \`project_name:\${process.env.PROJECT_NAME}\`,
      filterAISpans: true,
    }),
  ],
});

sdk.start();

// This example uses OpenAI, but use your preferred LLM client
async function main() {
  const result = await generateText({
    model: openai("gpt-4o-mini"),
    messages: [
      {
        role: "user",
        content: "What are my orders and where are they? My user ID is 123",
      },
    ],
    tools: {
      listOrders: tool({
        description: "list all orders",
        parameters: z.object({ userId: z.string() }),
        execute: async ({ userId }) =>
          \`User \${userId} has the following orders: 1\`,
      }),
      viewTrackingInformation: tool({
        description: "view tracking information for a specific order",
        parameters: z.object({ orderId: z.string() }),
        execute: async ({ orderId }) =>
          \`Here is the tracking information for \${orderId}\`,
      }),
    },
    experimental_telemetry: {
      isEnabled: true,
      functionId: "my-awesome-function",
      metadata: {
        something: "custom",
        someOtherThing: "other-value",
      },
    },
    maxSteps: 10,
  });

  await sdk.shutdown();
}

main().catch(console.error);`,
            ],
            extraDependencies:
              "ai @ai-sdk/openai @opentelemetry/sdk-node @opentelemetry/sdk-trace-base zod",
          },
        },
      },
      supportedLanguages: [Language.TYPESCRIPT],
      docsUrl: "/docs/integrations/opentelemetry#vercel-ai-sdk",
    },
    [SdkIntegration.CLAUDE_AGENT_SDK]: {
      name: "Claude Agent SDK",
      icon: <ClaudeLogo color="currentColor" size={36} />,
      environmentVariables: {
        ...dynamicApiUrl,
        BRAINTRUST_API_KEY: apiKey || "<YOUR_BRAINTRUST_API_KEY>",
        ANTHROPIC_API_KEY: "<YOUR_ANTHROPIC_API_KEY>",
      },
      codeExamples: {
        [Language.PYTHON]: {
          snippets: [
            `#!/usr/bin/env python3

import asyncio
import os

from claude_agent_sdk import ClaudeAgentOptions, ClaudeSDKClient
from braintrust.wrappers.claude_agent_sdk import setup_claude_agent_sdk

# Setup Braintrust - automatically patches claude_agent_sdk for tracing
setup_claude_agent_sdk(
    project="${projectName}",
    api_key=os.environ.get("BRAINTRUST_API_KEY"),
)


async def main():
    # Configure the Claude Agent SDK options
    options = ClaudeAgentOptions(
        model="claude-sonnet-4-5-20250929",
    )

    # Execute your prompt query in a persistent client session
    async with ClaudeSDKClient(options=options) as client:
        await client.query("What is the capital of France?")
        async for message in client.receive_response():
            print(message)


if __name__ == "__main__":
    asyncio.run(main())`,
          ],
          extraDependencies: "claude-agent-sdk",
        },
        [Language.TYPESCRIPT]: {
          snippets: [
            `import { initLogger, wrapClaudeAgentSDK } from "braintrust";
import * as claudeSDK from "@anthropic-ai/claude-agent-sdk";

// Initialize the Braintrust logger
initLogger({
  projectName: "${projectName}",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

// Wrap the Claude SDK with Braintrust tracing.
// This gives us a traced version of query that we can call directly
const { query } = wrapClaudeAgentSDK(claudeSDK);

async function main() {
  // Enter your prompt call here
  for await (const message of query({
    prompt: "What is the capital of France?",
    options: {
      model: "claude-sonnet-4-5-20250929",
    },
  })) {
    console.log(message);
  }
}

main();`,
          ],
          extraDependencies: "@anthropic-ai/claude-agent-sdk",
        },
      },
      supportedLanguages: [Language.TYPESCRIPT, Language.PYTHON],
      docsUrl: "/docs/integrations/claude-agent-sdk",
    },
    [SdkIntegration.CUSTOM]: {
      name: "Custom",
      environmentVariables: {
        ...dynamicApiUrl,
        BRAINTRUST_API_KEY: apiKey || "<Your Braintrust API Key>",
      },
      codeExamples: {
        [Language.PYTHON]: {
          snippets: [
            `from braintrust import current_span, init_logger, start_span, traced

logger = init_logger(project="${projectName}")


def call_my_llm(input: str, params: dict) -> dict:
    # Replace with your custom LLM implementation
    return {
        "completion": "Hello, world!",
        "metrics": {
            "prompt_tokens": len(input),
            "completion_tokens": 10,
        },
    }


# notrace_io=True prevents logging the function arguments as input, and lets us log a more specific input format.
@traced(type="llm", name="Custom LLM", notrace_io=True)
def invoke_custom_llm(llm_input: str, params: dict):
    result = call_my_llm(llm_input, params)
    content = result["completion"]
    current_span().log(
        input=[{"role": "user", "content": llm_input}],
        output=content,
        metrics=dict(
            prompt_tokens=result["metrics"]["prompt_tokens"],
            completion_tokens=result["metrics"]["completion_tokens"],
            tokens=result["metrics"]["prompt_tokens"] + result["metrics"]["completion_tokens"],
        ),
        metadata=params,
    )
    return content


def my_route_handler(req):
    with start_span() as span:
        result = invoke_custom_llm(
            dict(
                body=req.body,
                params=dict(temperature=0.1),
            )
        )
        span.log(input=req.body, output=result)
        return result`,
          ],
        },
        [Language.TYPESCRIPT]: {
          snippets: [
            `import { initLogger, traced, wrapTraced } from "braintrust";

const logger = initLogger({
  projectName: "${projectName}",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

interface LLMCompletion {
  completion: string;
  metrics: {
    prompt_tokens: number;
    completion_tokens: number;
  };
}

async function callMyLLM(
  input: string,
  params: { temperature: number },
): Promise<LLMCompletion> {
  // Replace with your custom LLM implementation
  return {
    completion: "Hello, world!",
    metrics: {
      prompt_tokens: input.length,
      completion_tokens: 10,
    },
  };
}

export const invokeCustomLLM = wrapTraced(
  async function invokeCustomLLM(
    llmInput: string,
    params: { temperature: number },
  ) {
    return traced(async (span) => {
      const result = await callMyLLM(llmInput, params);
      const content = result.completion;
      span.log({
        input: [{ role: "user", content: llmInput }],
        output: content,
        metrics: {
          prompt_tokens: result.metrics.prompt_tokens,
          completion_tokens: result.metrics.completion_tokens,
          tokens:
            result.metrics.prompt_tokens + result.metrics.completion_tokens,
        },
        metadata: params,
      });
      return content;
    });
  },
  {
    type: "llm",
    name: "Custom LLM",
  },
);

export async function POST(req: Request) {
  return traced(async (span) => {
    const result = await invokeCustomLLM(await req.text(), {
      temperature: 0.1,
    });
    span.log({ input: req.body, output: result });
    return result;
  });
}`,
          ],
        },
      },
      supportedLanguages: [Language.TYPESCRIPT, Language.PYTHON],
      docsUrl: "/docs/guides/traces/customize#wrapping-a-custom-llm-client",
    },
    [SdkIntegration.OPENROUTER]: {
      name: "OpenRouter",
      icon: <OpenrouterLogo color="currentColor" size={36} />,
      environmentVariables: {
        ...dynamicApiUrl,
        BRAINTRUST_API_KEY: apiKey || "<Your Braintrust API Key>",
        OPENROUTER_API_KEY: "<Your OpenRouter API Key>",
      },
      codeExamples: {
        [Language.TYPESCRIPT]: {
          snippets: [
            `import OpenAI from "openai";
import { initLogger, traced, wrapTraced } from "braintrust";

// Initialize the Braintrust logger
const logger = initLogger({
  projectName: "My Project",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

// Configure OpenRouter client (OpenAI-compatible)
// This example uses OpenAI, but use your preferred SDK
const openrouter = new OpenAI({
  apiKey: process.env.OPENROUTER_API_KEY,
  baseURL: "https://openrouter.ai/api/v1",
});

interface LLMCompletion {
  completion: string;
  metrics: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// This is the custom implementation. Replace stub with OpenRouter call
async function callMyLLM(
  input: string,
  params: { temperature: number },
): Promise<LLMCompletion> {
  const response = await openrouter.chat.completions.create({
    model: "google/gemma-7b-it", //or any OpenRouter-supported model
    messages: [{ role: "user", content: input }],
    temperature: params.temperature,
  });

  const choice = response.choices[0].message?.content ?? "";
  return {
    completion: choice,
    metrics: {
      prompt_tokens: response.usage?.prompt_tokens ?? 0,
      completion_tokens: response.usage?.completion_tokens ?? 0,
      total_tokens: response.usage?.total_tokens ?? 0,
    },
  };
}

// Wrap in Braintrust tracing
export const invokeCustomLLM = wrapTraced(
  async function invokeCustomLLM(
    llmInput: string,
    params: { temperature: number },
  ) {
    return traced(async (span) => {
      const result = await callMyLLM(llmInput, params);
      span.log({
        input: [{ role: "user", content: llmInput }],
        output: result.completion,
        metrics: result.metrics,
        metadata: params,
      });
      return result.completion;
    });
  },
  {
    type: "llm",
    name: "OpenRouter LLM",
  },
);

// Example HTTP handler
export async function POST(req: Request) {
  return traced(async (span) => {
    const body = await req.text();
    const result = await invokeCustomLLM(body, { temperature: 0.1 });
    span.log({ input: body, output: result });
    return new Response(result);
  });
}`,
          ],
        },
        [Language.PYTHON]: {
          snippets: [
            `import os
from openai import OpenAI
from braintrust import current_span, init_logger, start_span, traced

# Initialize Braintrust logger
logger = init_logger(project="My Project", api_key=os.getenv("BRAINTRUST_API_KEY"))

# Configure OpenRouter client (OpenAI-compatible)
# This example uses OpenAI, but use your preferred SDK
openrouter = OpenAI(
    api_key=os.getenv("OPENROUTER_API_KEY"),
    base_url="https://openrouter.ai/api/v1",
)


# Replace custom integration stub with OpenRouter call
def call_my_llm(input: str, params: dict) -> dict:
    """Call OpenRouter (OpenAI-compatible API)."""
    response = openrouter.chat.completions.create(
        model="google/gemma-7b-it",  #or any OpenRouter-supported model
        messages=[{"role": "user", "content": input}],
        temperature=params.get("temperature", 0.7),
    )

    choice = response.choices[0].message.content if response.choices else ""
    usage = response.usage or {}

    return {
        "completion": choice,
        "metrics": {
            "prompt_tokens": usage.get("prompt_tokens", 0),
            "completion_tokens": usage.get("completion_tokens", 0),
            "tokens": usage.get("total_tokens", 0),
        },
    }


# notrace_io=True prevents logging the function arguments automatically,
# so we can log structured input/output ourselves.
@traced(type="llm", name="OpenRouter LLM", notrace_io=True)
def invoke_custom_llm(llm_input: str, params: dict):
    result = call_my_llm(llm_input, params)
    content = result["completion"]
    current_span().log(
        input=[{"role": "user", "content": llm_input}],
        output=content,
        metrics=result["metrics"],
        metadata=params,
    )
    return content


# Example route handler (Flask/FastAPI-style)
def my_route_handler(req):
    with start_span() as span:
        result = invoke_custom_llm(req.body, dict(temperature=0.1))
        span.log(input=req.body, output=result)
        return result


# Example standalone run
if __name__ == "__main__":
    class DummyReq:
        body = "What is the capital of Japan?"

    response = my_route_handler(DummyReq())
    print("Response:", response)`,
          ],
        },
      },
      supportedLanguages: [Language.TYPESCRIPT, Language.PYTHON],
      docsUrl: "/docs/guides/traces/customize#wrapping-a-custom-llm-client",
    },
    [SdkIntegration.LANGCHAIN]: {
      name: "LangChain",
      icon: <LangchainLogo color="currentColor" size={36} />,
      environmentVariables: {
        ...dynamicApiUrl,
        BRAINTRUST_API_KEY: apiKey || "<Your Braintrust API Key>",
      },
      codeExamples: {
        [Language.TYPESCRIPT]: {
          snippets: [
            `import {
  BraintrustCallbackHandler,
} from "@braintrust/langchain-js";
import { ChatOpenAI } from "@langchain/openai";
import { initLogger } from "braintrust";

// Initialize the Braintrust logger
initLogger({
  projectName: "My Project",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

// All LangChain calls will be logged to Braintrust
const handler = new BraintrustCallbackHandler();

// Enter your prompt call here
async function main() {
  // Use any LangChain supported model here
  const model = new ChatOpenAI({ modelName: "gpt-4o-mini" });

  const result = await model.invoke("What is the capital of France?", {
    callbacks: [handler],
  });

  console.log(result);
}

main();`,
          ],
          extraDependencies:
            "@braintrust/langchain-js @langchain/core @langchain/openai",
        },
        [Language.PYTHON]: {
          snippets: [
            `import asyncio
            import os

from braintrust import init_logger
from braintrust_langchain import BraintrustCallbackHandler, set_global_handler
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI


async def main():
    init_logger(project="My Project", api_key=os.getenv("BRAINTRUST_API_KEY"))

    # All LangChain calls will be logged to Braintrust
    handler = BraintrustCallbackHandler()
    set_global_handler(handler)

    # Initialize LangChain components and enter your prompt
    prompt = ChatPromptTemplate.from_template("What is 1 + {number}?")
    # Use any LangChain supported model here
    model = ChatOpenAI()

    # Create a simple chain
    chain = prompt | model
    response = await chain.ainvoke({"number": "2"})

if __name__ == "__main__":
    asyncio.run(main())`,
          ],
          extraDependencies:
            "braintrust-langchain langchain-core langchain-openai",
        },
      },
      supportedLanguages: [Language.TYPESCRIPT, Language.PYTHON],
      docsUrl: "/docs/integrations/langchain",
    },
    [SdkIntegration.LANGGRAPH]: {
      name: "LangGraph",
      icon: <LanggraphLogo color="currentColor" size={36} />,
      environmentVariables: {
        ...dynamicApiUrl,
        BRAINTRUST_API_KEY: apiKey || "<Your Braintrust API Key>",
      },
      codeExamples: {
        [Language.TYPESCRIPT]: {
          snippets: [
            `import {
  BraintrustCallbackHandler,
  setGlobalHandler,
} from "@braintrust/langchain-js";
import { END, START, StateGraph, StateGraphArgs } from "@langchain/langgraph";
import { ChatOpenAI } from "@langchain/openai";
import { initLogger } from "braintrust";

const logger = initLogger({
  projectName: "My Project",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

const handler = new BraintrustCallbackHandler({ logger });
setGlobalHandler(handler);

// Define the state structure for the graph
type HelloWorldGraphState = Record<string, any>;

const graphStateChannels: StateGraphArgs<HelloWorldGraphState>["channels"] = {};

// Use any LangChain supported model here
const model = new ChatOpenAI({
  model: "gpt-4o-mini",
});

async function sayHello(state: HelloWorldGraphState) {
  const res = await model.invoke("Say hello");
  return { message: res.content };
}

function sayBye(state: HelloWorldGraphState) {
  console.log(\`From the 'sayBye' node: Bye world!\`);
  return {};
}

async function main() {
  const graphBuilder = new StateGraph({ channels: graphStateChannels })
    .addNode("sayHello", sayHello)
    .addNode("sayBye", sayBye)
    .addEdge(START, "sayHello")
    .addEdge("sayHello", "sayBye")
    .addEdge("sayBye", END);

  const helloWorldGraph = graphBuilder.compile();

  // Execute the graph - all operations will be logged to Braintrust
  await helloWorldGraph.invoke({});
}

main();`,
          ],
          extraDependencies:
            "@braintrust/langchain-js @langchain/core @langchain/langgraph @langchain/openai",
        },
        [Language.PYTHON]: {
          snippets: [
            `import asyncio
import os
from typing import Dict

from braintrust import init_logger
from braintrust_langchain import BraintrustCallbackHandler, set_global_handler
from langchain_openai import ChatOpenAI
from langgraph.graph import END, START, StateGraph


async def main():
    init_logger(project="My Project", api_key=os.getenv("BRAINTRUST_API_KEY"))

    handler = BraintrustCallbackHandler()
    set_global_handler(handler)

    # Use any LangChain supported model here
    model = ChatOpenAI(model="gpt-4o-mini")

    def say_hello(state: Dict[str, str]):
        response = model.invoke("Say hello")
        return response.content

    def say_bye(state: Dict[str, str]):
        print("From the 'sayBye' node: Bye world!")
        return "Bye"

    # Create the state graph
    workflow = (
        StateGraph(state_schema=Dict[str, str])
        .add_node("sayHello", say_hello)
        .add_node("sayBye", say_bye)
        .add_edge(START, "sayHello")
        .add_edge("sayHello", "sayBye")
        .add_edge("sayBye", END)
    )

    graph = workflow.compile()

    # Execute the graph - all operations will be logged to Braintrust
    await graph.ainvoke({})


if __name__ == "__main__":
    asyncio.run(main())`,
          ],
          extraDependencies: "braintrust-langchain langchain-openai langgraph",
        },
      },
      supportedLanguages: [Language.TYPESCRIPT, Language.PYTHON],
      docsUrl: "/docs/integrations/langgraph",
    },
    [SdkIntegration.OPENTELEMETRY]: {
      name: "OpenTelemetry",
      icon: <OpenTelemetryLogo color="currentColor" size={36} />,
      environmentVariables: {
        ...dynamicApiUrl,
        BRAINTRUST_API_KEY: apiKey || "<Your Braintrust API Key>",
        PROJECT_NAME: projectName || "<Your Project Name>",
      },
      codeExamples: {
        [Language.TYPESCRIPT]: {
          snippets: [
            `import { NodeSDK } from "@opentelemetry/sdk-node";
import { BraintrustSpanProcessor } from "braintrust";
import { generateText } from "ai";

process.env.BRAINTRUST_PARENT = \`project_name:\${process.env.PROJECT_NAME}\`;

// Create and start the SDK with a span processor
const sdk = new NodeSDK({
  serviceName: "my-service",
  spanProcessor: new BraintrustSpanProcessor({
    apiKey: process.env.BRAINTRUST_API_KEY,
  }),
});
sdk.start();

// Enter your prompt call here
async function main() {
  const result = await generateText({
    model: openai("gpt-4o-mini"),
    prompt: "What is 1 + 1?",
  });

  console.log(result);
  await sdk.shutdown();
}

main().catch(console.error);
`,
          ],
          extraDependencies:
            "@opentelemetry/api @opentelemetry/sdk-node @opentelemetry/sdk-trace-base",
        },
        [Language.PYTHON]: {
          snippets: [
            `from braintrust.otel import BraintrustSpanProcessor
from opentelemetry import trace
from opentelemetry.sdk.trace import TracerProvider

os.environ["BRAINTRUST_PARENT"] = f"project_name:{os.getenv('PROJECT_NAME')}"

# Configure the global OTel tracer provider
provider = TracerProvider()
trace.set_tracer_provider(provider)

# Send spans to Braintrust
provider.add_span_processor(BraintrustSpanProcessor(
    api_key=os.getenv("BRAINTRUST_API_KEY"),
))

# Enter your prompt call here
result = completion(
    model="gpt-4o-mini",
    messages=[{"role": "user", "content": "What is 1+1?"}]
)

print(result)
`,
          ],
          extraDependencies: "braintrust[otel]",
        },
      },
      supportedLanguages: [Language.TYPESCRIPT, Language.PYTHON],
      docsUrl: "/docs/integrations/opentelemetry",
    },
    [SdkIntegration.LITELLM]: {
      name: "LiteLLM",
      icon: <LiteLLMLogo color="currentColor" size={36} />,
      environmentVariables: {
        ...dynamicApiUrl,
        BRAINTRUST_API_KEY: apiKey || "<Your Braintrust API Key>",
        BRAINTRUST_API_BASE: `${apiUrl}/v1`,
        OPENAI_API_KEY: "<Your OpenAI API Key>",
      },
      codeExamples: {
        [Language.TYPESCRIPT]: {
          snippets: [
            `import litellm from "litellm";
import { wrapOpenAI } from "braintrust";

// Set callback for LiteLLM to send data to braintrust
litellm.callbacks = ["braintrust"];

// This example uses OpenAI, but use your preferred LLM client
const client = wrapOpenAI(
  new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  }),
);

// Enter your prompt call here
const result = await client.chat.completions.create({
  model: "gpt-4o-mini",
  store: true,
  messages: [{ role: "user", content: "What is 1+1?" }],
  metadata: {
    project_name: "${projectName}",
  },
});

console.log(result);
`,
          ],
          extraDependencies: "litellm",
        },
        [Language.PYTHON]: {
          snippets: [
            `import litellm
from braintrust import wrap_openai

# Set callback for LiteLLM to send data to braintrust
litellm.callbacks = ["braintrust"]

# This example uses OpenAI, but use your preferred LLM client
client = wrap_openai(
  openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
)

# Enter your prompt call here
result = litellm.completion(
  model="gpt-4o-mini",
  store=True,
  messages=[
    {"role": "user", "content": "What is 1+1?"}
  ],
  metadata={ "project_name": "${projectName}"}
)

print(result)`,
          ],
          extraDependencies: "litellm",
        },
      },
      supportedLanguages: [Language.TYPESCRIPT, Language.PYTHON],
      docsUrl: "https://docs.litellm.ai/docs/observability/braintrust",
    },
    [SdkIntegration.STRANDS]: {
      name: "Strands",
      icon: <StrandsLogo color="currentColor" size={36} />,
      environmentVariables: {
        BRAINTRUST_API_URL: apiUrl || "https://api.braintrust.dev",
        BRAINTRUST_API_KEY: apiKey || "<Your Braintrust API Key>",
        PROJECT_NAME: projectName || "<Your Project Name>",
      },
      codeExamples: {
        [Language.PYTHON]: {
          snippets: [
            `import os
from braintrust.otel import BraintrustSpanProcessor
from opentelemetry import trace
from opentelemetry.sdk.trace import TracerProvider
from strands import Agent
from strands.models.litellm import LiteLLMModel
from strands_tools import calculator

# Set up environment variables
os.environ.setdefault("BRAINTRUST_PARENT", f"project_name:{os.getenv('PROJECT_NAME')}")

# Configure the global OTel tracer provider
provider = TracerProvider()
trace.set_tracer_provider(provider)

# Send spans to Braintrust using the built-in span processor
provider.add_span_processor(BraintrustSpanProcessor())

# Use any Strands supported model (this example uses LiteLLM)
model = LiteLLMModel(model_id="openai/gpt-4o")
agent = Agent(
    model=model,
    system_prompt="You are an AI agent. Help answer questions with the tools at your disposal.",
    tools=[calculator],
)

result = agent("What is 123987 * 23498234?")
print(f"Result: {result}")
`,
          ],
          extraDependencies:
            "braintrust[otel] strands strands-tools opentelemetry-sdk",
        },
      },
      supportedLanguages: [Language.PYTHON],
      docsUrl: "https://strandsagents.com/latest/documentation/docs/",
    },
    [SdkIntegration.PYDANTIC]: {
      name: "Pydantic",
      icon: <PydanticLogo color="currentColor" size={36} />,
      environmentVariables: {
        BRAINTRUST_API_URL: apiUrl || "https://api.braintrust.dev",
        BRAINTRUST_API_KEY: apiKey || "<Your Braintrust API Key>",
        PROJECT_NAME: projectName || "<Your Project Name>",
      },
      codeExamples: {
        [Language.PYTHON]: {
          snippets: [
            `import random
from braintrust.otel import BraintrustSpanProcessor
from opentelemetry import trace
from opentelemetry.sdk.trace import TracerProvider
from pydantic_ai import RunContext
from pydantic_ai.agent import Agent

# Set up tracing for the agent to automatically log to Braintrust
provider = TracerProvider()
trace.set_tracer_provider(provider)

provider.add_span_processor(BraintrustSpanProcessor())

Agent.instrument_all()

# Create your agent
agent = Agent(
    "openai:gpt-4o",
    system_prompt=(
        "You are a dice game host. Roll the dice for the player and check if their guess matches. "
        "Always include the player's name in the response."
    ),
)

# Define tool calls for your agent
@agent.tool_plain
def roll_dice() -> str:
    """Roll a six-sided die and return the result."""
    return str(random.randint(1, 6))

@agent.tool
def get_player_name(ctx: RunContext[str]) -> str:
    """Get the player's name."""
    return ctx.deps

# \`deps\` is a way to inject any extra data your tools might need during this run
dice_result = agent.run_sync("My guess is 4", deps="Anne")
print(dice_result)`,
          ],
          extraDependencies: "braintrust[otel] pydantic-ai opentelemetry-sdk",
        },
      },
      supportedLanguages: [Language.PYTHON],
      docsUrl: "https://ai.pydantic.dev/agents/",
    },
    [SdkIntegration.CLOUDFLARE]: {
      name: "Cloudflare",
      icon: <CloudflareLogo color="currentColor" size={36} />,
      environmentVariables: {
        ...dynamicApiUrl,
        BRAINTRUST_API_KEY: apiKey || "<YOUR_BRAINTRUST_API_KEY>",
        OPENAI_API_KEY: "<YOUR_OPENAI_API_KEY>",
        CLOUDFLARE_ACCOUNT_ID: "<YOUR_CLOUDFLARE_ACCOUNT_ID>",
        CLOUDFLARE_AI_GATEWAY_NAME: "<YOUR_AI_GATEWAY_NAME>",
      },
      codeExamples: {
        [Language.PYTHON]: {
          snippets: [
            `import os
import openai
from braintrust import init_logger, wrap_openai

# Set the Cloudflare unified API gateway URL
# OpenAI client automatically adds /chat/completions to the end
base_url = f"https://gateway.ai.cloudflare.com/v1/{os.getenv('CLOUDFLARE_ACCOUNT_ID')}/{os.getenv('CLOUDFLARE_AI_GATEWAY_NAME')}/compat"

# Initialize the Braintrust logger and OpenAI client
client = wrap_openai(
  openai.OpenAI(
    # We use OpenAI here but choose your provider of choice
    api_key=os.getenv("OPENAI_API_KEY"),
    base_url=base_url
  )
)
logger = init_logger(project="${projectName}")

# Enter your prompt call here
result = client.chat.completions.create(
    # Specify your model of choice with {provider}/{model}
    model="openai/gpt-4o",
    messages=[{"role": "user", "content": "What is 1+1?"}]
)

print(result)`,
          ],
          extraDependencies: "openai",
        },
        [Language.TYPESCRIPT]: {
          snippets: [
            `import { OpenAI } from "openai";
import { initLogger, wrapOpenAI } from "braintrust";

// Set the Cloudflare unified API gateway URL
// OpenAI client automatically adds /chat/completions to the end
const baseURL = \`https://gateway.ai.cloudflare.com/v1/\${process.env.CLOUDFLARE_ACCOUNT_ID}/\${process.env.CLOUDFLARE_AI_GATEWAY_NAME}/compat\`;

// Initialize the Braintrust logger and OpenAI client
const client = wrapOpenAI(
  new OpenAI({
    // We use OpenAI here but choose your provider of choice
    apiKey: process.env.OPENAI_API_KEY,
    baseURL: baseURL,
  }),
);
const logger = initLogger({
  projectName: "${projectName}",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

// Enter your prompt call here
async function main() {
  const result = await client.chat.completions.create({
    // Specify your model of choice with {provider}/{model}
    model: "openai/gpt-4o",
    messages: [{ role: "user", content: "What is 1+1?" }],
  });
  console.log(result);
}

main();`,
          ],
          extraDependencies: "openai",
        },
      },
      supportedLanguages: [Language.PYTHON, Language.TYPESCRIPT],
      docsUrl: "/docs/integrations/cloudflare",
    },
    [SdkIntegration.CREWAI]: {
      name: "CrewAI",
      icon: <CrewAILogo color="currentColor" size={36} />,
      environmentVariables: {
        ...dynamicApiUrl,
        BRAINTRUST_API_KEY: apiKey || "<YOUR_BRAINTRUST_API_KEY>",
        BRAINTRUST_PARENT: `project_name:${projectName || "<YOUR_PROJECT_NAME>"}`,
        OPENAI_API_KEY: "<YOUR_OPENAI_API_KEY>",
      },
      codeExamples: {
        [Language.PYTHON]: {
          snippets: [
            `import os
from typing import Any, Dict

from braintrust.otel import BraintrustSpanProcessor
from crewai import Agent, Crew, Task
from crewai.llm import LLM
from opentelemetry import trace
from opentelemetry.instrumentation.crewai import CrewAIInstrumentor
from opentelemetry.instrumentation.openai import OpenAIInstrumentor
from opentelemetry.sdk.trace import TracerProvider


# Get or create the tracer provider
current_provider = trace.get_tracer_provider()
if isinstance(current_provider, TracerProvider):
    provider = current_provider
else:
    provider = TracerProvider()
    trace.set_tracer_provider(provider)

# Add the Braintrust span processor
provider.add_span_processor(BraintrustSpanProcessor())

# Instrument the CrewAI and OpenAI libraries
CrewAIInstrumentor().instrument(tracer_provider=provider)
OpenAIInstrumentor().instrument(tracer_provider=provider)

# Create the CrewAI crew
llm = LLM(model="gpt-4o-mini")
coder = Agent(
    role="Software developer",
    goal="Write clear, concise code on demand",
    backstory="An expert coder with a keen eye for software trends.",
    verbose=True,
    llm=llm,
)
task = Task(
    description="Define the HTML for making a simple website with heading- Hello World! Braintrust monitors your CrewAI agent!",
    expected_output="A clear and concise HTML code",
    agent=coder,
)
crew = Crew(
    agents=[coder],
    tasks=[task],
    verbose=True,
)

# Run the CrewAI crew
result = crew.kickoff()`,
          ],
          extraDependencies:
            "braintrust[otel] crewai opentelemetry-instrumentation-openai opentelemetry-instrumentation-crewai python-dotenv",
        },
      },
      supportedLanguages: [Language.PYTHON],
      docsUrl: "/docs/integrations/crew-ai",
    },
    [SdkIntegration.AUTOGEN]: {
      name: "Autogen",
      icon: <AutogenLogo color="currentColor" size={36} />,
      environmentVariables: {
        ...dynamicApiUrl,
        BRAINTRUST_API_KEY: apiKey || "<YOUR_BRAINTRUST_API_KEY>",
        BRAINTRUST_PARENT: `project_name:${projectName || "<YOUR_PROJECT_NAME>"}`,
        OPENAI_API_KEY: "<YOUR_OPENAI_API_KEY>",
      },
      codeExamples: {
        [Language.PYTHON]: {
          snippets: [
            `import asyncio
from autogen_agentchat.agents import AssistantAgent
from autogen_ext.models.openai import OpenAIChatCompletionClient
from braintrust.otel import BraintrustSpanProcessor
from opentelemetry import trace
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.instrumentation.openai import OpenAIInstrumentor


def setup_tracing() -> None:
    # Setup OTel tracer provider with openai instrumentor
    provider = TracerProvider()
    provider.add_span_processor(BraintrustSpanProcessor())
    trace.set_tracer_provider(provider)
    OpenAIInstrumentor().instrument(tracer_provider=provider)


async def main() -> None:
    setup_tracing()
    tracer = trace.get_tracer("autogen-demo-bot")

    # Create agent
    agent = AssistantAgent(
        "assistant",
        OpenAIChatCompletionClient(model="gpt-4o"),
    )

    # Wrap agent run in a trace
    with tracer.start_as_current_span("run_team"):
        print(await agent.run(task="Say 'Hello World!'"))


asyncio.run(main())`,
          ],
          extraDependencies:
            "braintrust[otel] autogen-agentchat opentelemetry-instrumentation-openai",
        },
      },
      supportedLanguages: [Language.PYTHON],
      docsUrl: "/docs/integrations/autogen",
    },
    [SdkIntegration.LIVEKIT]: {
      name: "LiveKit Agents",
      icon: <LiveKitLogo color="currentColor" size={36} />,
      environmentVariables: {
        ...dynamicApiUrl,
        BRAINTRUST_API_KEY: apiKey || "<YOUR_BRAINTRUST_API_KEY>",
        BRAINTRUST_PARENT: `project_name:${projectName || "<YOUR_PROJECT_NAME>"}`,
        OPENAI_API_KEY: "<YOUR_OPENAI_API_KEY>",
      },
      codeExamples: {
        [Language.PYTHON]: {
          snippets: [
            `from livekit import agents
from livekit.agents import AgentSession, Agent, RoomInputOptions
from livekit.agents.telemetry import set_tracer_provider
from livekit.plugins import openai, noise_cancellation
from braintrust.otel import BraintrustSpanProcessor
from opentelemetry.sdk.trace import TracerProvider

def setup_braintrust_telemetry():
    trace_provider = TracerProvider()
    trace_provider.add_span_processor(BraintrustSpanProcessor())
    set_tracer_provider(trace_provider)

class Assistant(Agent):
    def __init__(self) -> None:
        super().__init__(instructions="You are a helpful voice AI assistant.")

async def entrypoint(ctx: agents.JobContext):
    # Setup braintrust tracing via OTel
    setup_braintrust_telemetry()

    # Create agent session with OpenAI realtime model
    session = AgentSession(
        llm=openai.realtime.RealtimeModel(voice="coral")
    )

    # Start the agent session with assistant agent
    await session.start(
        room=ctx.room,
        agent=Assistant(),
        room_input_options=RoomInputOptions(
            noise_cancellation=noise_cancellation.BVC(),
        ),
    )

# Run script locally with "python livekit_agent.py console"
if __name__ == "__main__":
    agents.cli.run_app(agents.WorkerOptions(entrypoint_fnc=entrypoint))`,
          ],
          extraDependencies:
            "braintrust[otel] livekit-agents livekit-plugins-openai opentelemetry-sdk",
        },
      },
      supportedLanguages: [Language.PYTHON],
      docsUrl: "/docs/integrations/livekit-agents",
    },
  };
};

export const isCodeExample = (obj: unknown): obj is CodeExample => {
  return (
    !!obj &&
    typeof obj === "object" &&
    "snippets" in obj &&
    Array.isArray(obj.snippets)
  );
};

type FrameworkRecord<T extends string> = Record<T, CodeExample>;

export const isFrameworkRecord = <T extends string>(
  obj: unknown,
): obj is FrameworkRecord<T> => {
  return (
    !!obj &&
    typeof obj === "object" &&
    !Array.isArray(obj) &&
    !("snippets" in obj && Array.isArray(obj.snippets))
  );
};

function getFrameworkExample<T extends string>(
  examples: unknown,
  framework: T,
): CodeExample | undefined {
  if (isFrameworkRecord<T>(examples) && framework in examples) {
    const example = examples[framework];
    return isCodeExample(example) ? example : undefined;
  }
  return undefined;
}

export function getCurrentCodeExample(
  provider: SdkIntegration,
  language: Language,
  vercelFramework?: VercelFramework,
  projectName?: string,
): CodeExample | undefined {
  const providerConfig = getIntegrationConfigs(
    undefined,
    undefined,
    projectName,
  )[provider];
  const examples = providerConfig?.codeExamples?.[language];

  // Try framework-specific examples first
  if (provider === SdkIntegration.VERCEL && vercelFramework) {
    return getFrameworkExample(examples, vercelFramework);
  }

  // Fallback to direct code example
  return isCodeExample(examples) ? examples : undefined;
}
