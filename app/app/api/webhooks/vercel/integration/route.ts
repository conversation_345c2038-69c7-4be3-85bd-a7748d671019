import { type NextRequest, NextResponse } from "next/server";
import { createHmac } from "crypto";
import {
  webhookEventSchema,
  unknownWebhookEventSchema,
  type WebhookEvent,
  type UnknownWebhookEvent,
  type MarketplaceMemberChangedEvent,
} from "#/lib/vercel/schemas";
import { withTx, getVercelWebhookObjects } from "#/lib/partner/vercel";
import { JsonHTTPError } from "#/lib/vercel/_request_util";

// Handle marketplace.member.changed event
async function handleMemberChanged(
  event: MarketplaceMemberChangedEvent,
): Promise<void> {
  const { installationId, memberId, role } = event.payload;

  return await withTx(async (client) => {
    const objects = await getVercelWebhookObjects(event, client);
    if (!objects) {
      console.log(`No organization found for installation: ${installationId}`);
      throw new JsonHTTPError(404, {
        error: {
          code: "bad_request",
          message: `No installation id ${installationId} found`,
        },
      });
    }

    const { org, user, is_last_owner } = objects;
    if (!user) {
      console.log(`No user found for Vercel member ID: ${memberId}`);
      // This could be a new member - we'll handle them when they first authenticate
      return;
    }

    if (is_last_owner && role !== "ADMIN") {
      throw new JsonHTTPError(400, {
        error: {
          code: "bad_request",
          message: `Cannot remove last owner from installation ${installationId}`,
        },
      });
    }

    console.log(
      `Member ${user.email} (${user.id}) role changed to ${role} for org ${org.name} (${org.id})`,
    );

    if (is_last_owner && role === "ADMIN") {
      console.log(
        `Member ${user.email} (${user.id}) is already in Owners group in org ${org.name}, skipping event`,
      );
      return;
    }

    // Handle different role changes
    if (role === "NONE") {
      // Member is being removed from the organization
      console.log(`Removing member ${user.email} from org ${org.name}`);

      // Evict user from org
      await client.query(
        `
        select remove_member_from_org(
          user_to_remove_id => $1,
          organization_id => $2,
          actor_auth_id => $3)`,
        [user.id, org.id, user.auth_id],
      );

      console.log(`Member ${user.email} removed from org ${org.name}`);
    } else if (role === "ADMIN") {
      // Add user to Owners group
      console.log(
        `Adding member ${user.email} to Owners group in org ${org.name}`,
      );

      // Evict user from org and re-add as Owner
      await client.query(
        `
        select remove_member_from_org(
          user_to_remove_id => $1,
          organization_id => $2,
          actor_auth_id => $3)`,
        [user.id, org.id, user.auth_id],
      );

      // Add user to Owners group
      await client.query(
        `SELECT add_member_to_org_unchecked($1::uuid, $2::uuid, array [get_group_id($2::uuid, $3)])`,
        [user.id, org.id, "Owners"],
      );

      console.log(`Member ${user.email} added to Owners group`);
    } else if (role === "USER") {
      // Add user to Viewers group and remove from Owners if present
      console.log(`Setting member ${user.email} as Viewer in org ${org.name}`);

      // Evict user from org and re-add as Viewer
      await client.query(
        `
        select remove_member_from_org(
          user_to_remove_id => $1,
          organization_id => $2,
          actor_auth_id => $3)`,
        [user.id, org.id, user.auth_id],
      );

      // Add user to Viewers group
      await client.query(
        `SELECT add_member_to_org_unchecked($1::uuid, $2::uuid, array [get_group_id($2::uuid, $3)])`,
        [user.id, org.id, "Viewers"],
      );

      console.log(
        `Member ${user.email} set as Viewer (removed from Owners if applicable)`,
      );
    }
  });
}

// Route webhook event to appropriate handler
async function handleWebhookEvent(event: WebhookEvent): Promise<void> {
  switch (event.type) {
    case "marketplace.member.changed":
      await handleMemberChanged(event);
      return;
    case "integration-configuration.removed":
      console.log("Integration configuration removed:", {
        eventId: event.id,
        configurationId: event.payload.configuration.id,
        createdAt: event.createdAt,
      });
      // TODO: Implement cleanup logic
      return;
    default:
      // Log unregistered but known event types
      console.log(`No handler registered for webhook event: ${event.type}`, {
        eventId: event.id,
        eventType: event.type,
        createdAt: event.createdAt,
      });
  }
}

// Main webhook handler for Vercel integration webhooks:
// https://vercel.com/docs/webhooks#integration-webhooks
//
// Note that this is distinct from Vercel account webhooks:
// https://vercel.com/docs/webhooks#account-webhooks
//
// This uses VERCEL_INTEGRATION_CLIENT_SECRET to secure the endpoint
// rather than the generated webhook secret mentioned in the docs:
// https://vercel.com/docs/webhooks#enter-your-endpoint-url
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Get webhook secret from environment
    const webhookSecret = process.env.VERCEL_INTEGRATION_CLIENT_SECRET;
    if (!webhookSecret) {
      console.error("VERCEL_INTEGRATION_CLIENT_SECRET not configured");
      return NextResponse.json(
        { error: "VERCEL_INTEGRATION_CLIENT_SECRET not configured" },
        { status: 500 },
      );
    }

    // Get raw body for signature verification
    const rawBody = await request.text();
    const rawBodyBuffer = Buffer.from(rawBody, "utf-8");
    const bodySignature = createHmac("sha1", webhookSecret)
      .update(rawBodyBuffer)
      .digest("hex");
    if (bodySignature !== request.headers.get("x-vercel-signature")) {
      return NextResponse.json(
        { code: "invalid_signature", error: "Invalid signature" },
        { status: 401 },
      );
    }

    // Parse body as JSON
    let body: unknown;
    try {
      body = JSON.parse(rawBody);
    } catch {
      return NextResponse.json({ error: "Invalid JSON" }, { status: 400 });
    }

    // Parse webhook event with schema validation
    let event: WebhookEvent | UnknownWebhookEvent;
    const knownEventResult = webhookEventSchema.safeParse(body);

    if (knownEventResult.success) {
      event = knownEventResult.data;
      await handleWebhookEvent(event);
      return NextResponse.json({ success: true }, { status: 200 });
    } else {
      // Try parsing as unknown event
      const unknownEventResult = unknownWebhookEventSchema.safeParse(body);
      if (unknownEventResult.success) {
        event = unknownEventResult.data;
        console.log(`Received unknown webhook event: ${event.type}`, body);
        return NextResponse.json(
          { error: "Unknown webhook event" },
          { status: 400 },
        );
      } else {
        // Log validation errors for debugging
        console.error("Failed to parse webhook event:", {
          errors: knownEventResult.error.errors,
          body,
        });
        return NextResponse.json(
          { error: "Invalid webhook event format" },
          { status: 400 },
        );
      }
    }
  } catch (error) {
    console.error("Webhook processing error:", error);
    if (error instanceof JsonHTTPError) {
      return NextResponse.json(error.body, { status: error.code });
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
