import { getRedirectParam } from "#/utils/auth/redirect";
import { trackSegmentEvent } from "#/utils/segment/segment";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { SignOutButton } from "@clerk/nextjs";
import { auth } from "@clerk/nextjs/server";
import Link from "next/link";
import { redirect } from "next/navigation";
import { z } from "zod";
import {
  upsertBraintrustUser,
  syncClerkAuthMetadata,
} from "#/lib/auth/user-helpers";
import { sql } from "@braintrust/btql/planner";

const domainMappingSchema = z.object({
  auto_add_domain_mapping_ids: z.string().array().nullish(),
});

export default async function AfterSignIn(props: {
  params: Promise<{ slug: string }>;
  searchParams: Promise<Record<string, string | string[]>>;
}) {
  const searchParams = await props.searchParams;
  const { userId, sessionClaims } = await auth();

  const email = sessionClaims?.primaryEmail ?? null;
  const domain = email ? email.split("@")[1] : null;
  const samlGroups = sessionClaims?.publicMetadata?.groups;

  if (samlGroups && samlGroups.length > 0) {
    console.log(`User ${email} has SAML groups: ${samlGroups.join(", ")}`);
  }

  let authId: string;
  let btUserId: string;
  let autoAddDomainMappingIds: string[] | null | undefined;

  if (userId) {
    const supabase = getServiceRoleSupabase();

    try {
      // Use the shared helper to upsert the user
      const { auth_id, id } = await upsertBraintrustUser({
        email,
        clerkId: userId,
        firstName: sessionClaims?.firstName,
        lastName: sessionClaims?.lastName,
        avatarUrl: sessionClaims?.imageUrl,
      });

      authId = auth_id;
      btUserId = id;

      // Handle domain mapping separately
      if (domain) {
        const template = sql` WITH existing_user_member_orgs as (
            SELECT org_id
            FROM members WHERE user_id = ${btUserId}
          )
          SELECT array_agg(domain_mappings.id::text) auto_add_domain_mapping_ids
          FROM domain_mappings
          WHERE
            domain = ${domain}
            AND org_id NOT IN (SELECT org_id FROM existing_user_member_orgs)
            AND (saml_group IS NULL ${samlGroups && samlGroups.length > 0 ? sql`OR saml_group = ANY(${samlGroups})` : sql``})
          `;
        const { query, params } = template.toNumericParamQuery();
        const { rows } = await supabase.query(query, params);
        const rowsParsed = domainMappingSchema.array().parse(rows);
        autoAddDomainMappingIds = rowsParsed[0]?.auto_add_domain_mapping_ids;
      }
    } catch (e) {
      console.error("Failed to update user", e);
      return (
        <div>
          Failed to update user. Please contact{" "}
          <Link href="mailto:<EMAIL>">
            <EMAIL>
          </Link>{" "}
          for help.
          <br />
          <br />
          <SignOutButton />
        </div>
      );
    }

    if (autoAddDomainMappingIds?.length) {
      // RBAC_DISCLAIMER: In general, group modification is only allowed by users who have
      // update permissions on the group. But since this codepath is only activated for
      // newly-added users, and presumably the org administrator has selected this group
      // for new users, we are skipping the RBAC check.
      const { rows: toAddOrgRows } = await supabase.query(
        `
        WITH add_orgs AS (
          SELECT org_id, group_id FROM domain_mappings
          WHERE id = ANY($1)
        )
        SELECT
            org_id,
            group_id,
            add_member_to_org_unchecked($2, org_id, array [group_id])
          FROM add_orgs
        `,
        [autoAddDomainMappingIds, btUserId],
      );
      if (toAddOrgRows.length > 0) {
        console.log(
          `Added ${email} to ${toAddOrgRows
            .map((r) => `org: ${r.org_id}, group: ${r.group_id ?? "none"}`)
            .join(", ")}`,
        );
      }
    }

    if (authId !== sessionClaims?.publicMetadata?.bt_auth_id) {
      try {
        await syncClerkAuthMetadata({
          clerkUserId: userId,
          btAuthId: authId,
          groups: samlGroups,
        });
      } catch (error) {
        console.error(
          "Failed to sync Clerk auth metadata during after-signin",
          {
            userId,
            authId,
            error,
          },
        );
      }
    }

    trackSegmentEvent({
      userId: btUserId,
      event: "signin",
      properties: {
        email: sessionClaims?.primaryEmail,
      },
    });
  }

  const [redirectPath, redirectParamsString] = (
    getRedirectParam(searchParams) || "/app"
  ).split("?");
  const redirectParams = new URLSearchParams(redirectParamsString);
  redirectParams.append("reset-user", "1");
  return redirect(`${redirectPath}?${redirectParams.toString()}`);
}
