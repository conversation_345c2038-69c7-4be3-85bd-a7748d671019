import { z } from "zod";
import { redirect } from "next/navigation";
import { exchangeCodeForToken, verifyToken } from "#/lib/vercel/auth";
import {
  getVercelCallbackObjects,
  createBtAndClerkUser,
} from "#/lib/partner/vercel";
import { clerkClient } from "@clerk/nextjs/server";
import { findClerkUserByEmail } from "#/lib/auth/user-helpers";
import { getServiceRoleSupabase } from "#/utils/supabase";

const VercelCallbackParamsSchema = z.object({
  code: z.string(),
  state: z.string().optional(), // Generated by Vercel for CSRF protection
  configurationId: z.string().optional(), // The integration configuration ID
  next: z.string().optional(), // URL to redirect to after completion
  source: z.string().optional(), // Where the installation originated (e.g., "marketplace")
  teamId: z.string().optional(), // Vercel team ID
  resource_id: z.string().optional(),
  project_id: z.string().optional(),
  invoice_id: z.string().optional(),
  check_id: z.string().optional(),
  support: z.string().optional(),
});

export default async function VercelCallback({
  searchParams,
}: {
  searchParams: Promise<Record<string, string | string[]>>;
}) {
  let redirectUrl: string;

  try {
    const params = VercelCallbackParamsSchema.parse(await searchParams);

    // 1. Exchange + verify token
    const idToken = await exchangeCodeForToken(params.code, params.state);
    const claims = await verifyToken(idToken);

    // 2. Check what exists
    const context = await getVercelCallbackObjects(claims, params.resource_id);
    if (!context) {
      throw new Error("Installation not found");
    }

    const { org, project, user } = context;

    // Handle email from claims or existing user
    // If user doesn't exist (e.g., email changed), we'll create them below
    const email = claims.user_email || user?.email;
    if (!email) {
      throw new Error("No email available from claims or user");
    }

    const supabase = getServiceRoleSupabase();

    // 3. Use createBtAndClerkUser to handle everything (Clerk + BT user creation)
    await createBtAndClerkUser({
      claims,
      email,
      orgId: org.id,
      client: supabase,
    });

    // 4. Get the Clerk user that was created
    const clerkUser = await findClerkUserByEmail(email);
    if (!clerkUser) {
      throw new Error("Clerk user not found after creation");
    }

    // 5. Create a sign-in token for the user
    const clerk = await clerkClient();
    const signInToken = await clerk.signInTokens.createSignInToken({
      userId: clerkUser.id,
      expiresInSeconds: 60 * 60,
    });

    // 6. Set success redirect URL
    const finalDestination = `/app/${org.name}${project ? `/p/${project.name}` : ""}`;
    redirectUrl = `/signin?__clerk_ticket=${signInToken.token}&redirect_url=${encodeURIComponent(finalDestination)}`;
  } catch (error) {
    console.error("Vercel SSO callback error:", error);

    const errorMessage =
      error instanceof Error ? error.message : "Authentication failed";
    redirectUrl = `/signin?error=${encodeURIComponent(errorMessage)}`;
  }

  redirect(redirectUrl);
}
