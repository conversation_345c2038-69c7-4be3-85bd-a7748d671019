import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "#/ui/basic-table";
import { type VercelIntegrationDetails } from "#/app/admin/actions";

export function VercelIntegrations({
  installations,
}: VercelIntegrationDetails) {
  return (
    <div>
      {installations.map(({ installation, account, resources }) => (
        <div key={installation.installation_id}>
          <div className="block font-mono text-sm">
            <div>Integration id: {installation.installation_id}</div>
            <div>
              Integration contact: {account.contact.name} (
              {account.contact.email})
            </div>
          </div>

          <Table className="mt-6 table-auto text-left">
            <TableHeader>
              <TableRow>
                <TableHead className="w-88">Project id</TableHead>
                <TableHead className="w-64">Name</TableHead>
                <TableHead className="w-24">Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {resources.map((row) => (
                <TableRow key={row.partnerId} className="py-2">
                  <TableCell className="w-88">
                    <span className="truncate">{row.partnerId}</span>
                  </TableCell>
                  <TableCell className="w-64">
                    <span className="truncate">{row.name}</span>
                  </TableCell>
                  <TableCell className="w-24">
                    <span className="truncate">{row.status}</span>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      ))}
    </div>
  );
}
