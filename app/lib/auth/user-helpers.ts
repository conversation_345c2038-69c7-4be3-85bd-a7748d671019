import { getServiceRoleSupabase } from "#/utils/supabase";
import { clerkClient, type User as ClerkUser } from "@clerk/nextjs/server";
import { type BtPgClient } from "@braintrust/local/bt-pg";
import { z } from "zod";

const userInfoSchema = z.object({
  auth_id: z.string(),
  id: z.string(),
});

export async function findClerkUserByEmail(
  email: string,
): Promise<ClerkUser | null> {
  const client = await clerkClient();
  const users = await client.users.getUserList({
    emailAddress: [email],
    limit: 1,
  });
  return users.totalCount > 0 ? users.data[0] : null;
}

export async function upsertBraintrustUser({
  email,
  clerkId,
  firstName,
  lastName,
  avatarUrl,
  metadata,
  client,
}: {
  email: string | null;
  clerkId: string;
  firstName?: string | null;
  lastName?: string | null;
  avatarUrl?: string | null;
  metadata?: Record<string, unknown>;
  client?: BtPgClient;
}): Promise<{ auth_id: string; id: string }> {
  const isTransaction = !!client;
  const supabase = client ?? getServiceRoleSupabase();

  // Don't allow failure to poison the transaction
  if (isTransaction) {
    await supabase.query("SAVEPOINT before_clerk_upsert");
  }

  try {
    // There are two dup-keys: clerk_id and email. In G-suite, if you have multiple domains mapped to the
    // same org, you could have multiple emails/aliases mapping to the same underlying user (clerk_id). The opposite
    // should not occur (two emails with different clerk_ids), UNLESS you invite a user in terms of an email that
    // is already registered to a different clerk_id.
    //
    // For now, we reconcile these cases by pivoting around the clerk_id as the dup key, and asking the user to contact us
    // if they notice an error.
    //
    // Also note that we support the (legacy) auth_id by just generating a random uuid if needed
    const { rows } = await supabase.query(
      `
      INSERT INTO users
        (email, auth_id, clerk_id, given_name, family_name, avatar_url, internal_metadata)
      VALUES
        ($1, gen_random_uuid(), $2, $3, $4, $5, $6)
      ON CONFLICT (clerk_id) DO UPDATE
        SET
          email = EXCLUDED.email,
          -- Do not clobber existing values
          auth_id = COALESCE(users.auth_id, EXCLUDED.auth_id),
          given_name = COALESCE(users.given_name, EXCLUDED.given_name),
          family_name = COALESCE(users.family_name, EXCLUDED.family_name),
          avatar_url = COALESCE(users.avatar_url, EXCLUDED.avatar_url),
          -- Merge metadata (not relevant for regular signin path)
          internal_metadata = COALESCE(users.internal_metadata, '{}') || EXCLUDED.internal_metadata
      RETURNING auth_id, id
      `,
      [
        email,
        clerkId,
        firstName,
        lastName,
        avatarUrl,
        metadata ? JSON.stringify(metadata) : "{}",
      ],
    );

    if (isTransaction) {
      await supabase.query("RELEASE SAVEPOINT before_clerk_upsert");
    }

    return userInfoSchema.parse(rows[0]);
  } catch (e) {
    if (isTransaction) {
      await supabase.query("ROLLBACK TO SAVEPOINT before_clerk_upsert");
    }
    console.error("Failed to update by clerk_id, will try email", e);
  }

  try {
    // Fall back to email as the conflict key
    const { rows } = await supabase.query(
      `
      INSERT INTO users
        (email, auth_id, clerk_id, given_name, family_name, avatar_url, internal_metadata)
      VALUES
        ($1, gen_random_uuid(), $2, $3, $4, $5, $6)
      ON CONFLICT (email) DO UPDATE
        SET
          clerk_id = EXCLUDED.clerk_id,
          -- Do not clobber existing values
          auth_id = COALESCE(users.auth_id, EXCLUDED.auth_id),
          given_name = COALESCE(users.given_name, EXCLUDED.given_name),
          family_name = COALESCE(users.family_name, EXCLUDED.family_name),
          avatar_url = COALESCE(users.avatar_url, EXCLUDED.avatar_url),
          -- Merge metadata, special handling for vercel.user_ids array
          internal_metadata = CASE
            WHEN users.internal_metadata->'vercel'->'user_ids' IS NOT NULL
                 AND EXCLUDED.internal_metadata->'vercel'->'user_ids' IS NOT NULL THEN
              jsonb_set(
                COALESCE(users.internal_metadata, '{}') || EXCLUDED.internal_metadata,
                '{vercel,user_ids}',
                (SELECT jsonb_agg(DISTINCT value)
                 FROM (
                   SELECT jsonb_array_elements(users.internal_metadata->'vercel'->'user_ids') AS value
                   UNION
                   SELECT jsonb_array_elements(EXCLUDED.internal_metadata->'vercel'->'user_ids') AS value
                 ) AS combined_ids)
              )
            ELSE
              COALESCE(users.internal_metadata, '{}') || EXCLUDED.internal_metadata
          END
      RETURNING auth_id, id
      `,
      [
        email,
        clerkId,
        firstName,
        lastName,
        avatarUrl,
        metadata ? JSON.stringify(metadata) : "{}",
      ],
    );

    return userInfoSchema.parse(rows[0]);
  } catch (e) {
    console.error("Failed to update by email", e);
    throw e;
  }
}

export async function syncClerkAuthMetadata({
  clerkUserId,
  btAuthId,
  groups,
}: {
  clerkUserId: string;
  btAuthId: string;
  groups: string[] | undefined;
}): Promise<void> {
  try {
    const client = await clerkClient();
    await client.users.updateUser(clerkUserId, {
      publicMetadata: {
        bt_auth_id: btAuthId,
        groups,
      },
    });
  } catch (error) {
    console.error("Failed to sync Clerk auth metadata", {
      clerkUserId,
      btAuthId,
      error,
    });
    throw error;
  }
}
