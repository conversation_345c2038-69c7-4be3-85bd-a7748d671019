import { z } from "zod";
import { v4 as uuid4 } from "uuid";
import {
  vercelInstallationSchema,
  userDetailsSchema,
  projectDetailsSchema,
  serviceAccountDetailsSchema,
  serviceTokenDetailsSchema,
} from "#/lib/vercel/schemas";
import { isObject } from "#/utils/object";
import type {
  OrgDetails,
  UserDetails,
  ProjectDetails,
  ServiceAccountDetails,
  ServiceTokenDetails,
  Integration,
  BillingPlan,
  GetBillingPlansResponse,
  GetResourceResponse,
  InstallIntegrationRequest,
  InstallationResponse,
  DeleteIntegrationResponse,
  ProvisionResourceRequest,
  ProvisionResourceResponse,
  Resource,
  UpdateResourceRequest,
  UpdateResourceResponse,
  MarketplaceMemberChangedEvent,
} from "#/lib/vercel/schemas";
import {
  type AnyClaims,
  type OidcClaims,
  type SystemOidcClaims,
} from "#/lib/vercel/auth";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { throwUdfError } from "#/pages/api/_invoke_supabase_udf";
import { JsonHTTPError } from "#/lib/vercel/_request_util";
import { addMembers } from "#/pages/api/organization/member_actions";
import { helper as updateProject } from "#/pages/api/project/patch_id";
import { SqlQueryParams } from "#/utils/sql-query-params";
import { type BtPgClient } from "@braintrust/local/bt-pg";
import {
  findClerkUserByEmail,
  upsertBraintrustUser,
  syncClerkAuthMetadata,
} from "#/lib/auth/user-helpers";
import { clerkClient, type User as ClerkUser } from "@clerk/nextjs/server";
import {
  encryptMessage,
  encryptedMessageSchema,
  decryptMessage,
} from "@braintrust/proxy/utils";
import { sha256 } from "#/utils/hash";
import { createServiceTokenHelper } from "#/pages/api/service_token/register";
import { deleteServiceTokenHelper } from "#/pages/api/service_token/delete_id";
import { deleteObjects } from "#/pages/api/_object_crud_util";
import { batchUpdateHelper } from "#/pages/api/acl/batch_update";
import { removeMembers } from "#/pages/api/organization/member_actions";

// XXX use real billing plan data
const billingPlans: BillingPlan[] = [
  {
    id: "free",
    scope: "installation",
    name: "Free",
    cost: "Free",
    description: "This is the free plan",
    type: "subscription",
    paymentMethodRequired: false,
    details: [{ label: "Free", value: "coolio" }],
    requiredPolicies: [
      { id: "1", name: "Terms of Service", url: "https://partner/toc" },
    ],
  },
  {
    id: "pro",
    scope: "installation",
    name: "Pro",
    cost: "$250/mo",
    description: "Pro plan",
    type: "subscription",
    paymentMethodRequired: true,
    details: [
      { label: "Max storage size", value: "20G" },
      { label: "Max queries per day", value: "100K" },
    ],
    highlightedDetails: [
      { label: "High availability", value: "Single zone" },
      { label: "Dataset size", value: "100Mb" },
    ],
    requiredPolicies: [
      { id: "1", name: "Terms of Service", url: "https://partner/toc" },
    ],
  },
];

const billingPlanMap = new Map(billingPlans.map((plan) => [plan.id, plan]));

// Get encryption key from environment
function getEncryptionKey(): string {
  const key = process.env.SECRET_ENCRYPTION_KEY;
  if (!key) {
    throw new Error("SECRET_ENCRYPTION_KEY environment variable is not set");
  }
  // Hash the key to get a consistent base64 256-bit key
  return sha256(key, "base64");
}

export async function withTx<T>(
  f: (client: BtPgClient) => Promise<T>,
  passthroughClient?: BtPgClient,
) {
  const passthrough = !!passthroughClient;

  let client = passthroughClient;
  if (!client) {
    client = await getServiceRoleSupabase().connect();
  }

  if (!passthrough) {
    await client.query("BEGIN");
  }

  try {
    const resp = await f(client);

    if (!passthrough) {
      await client.query("COMMIT");
    }

    return resp;
  } catch (error) {
    if (!passthrough) {
      await client.query("ROLLBACK");
    }
    throw error;
  } finally {
    if (!passthrough) {
      await client.end();
    }
  }
}

export async function createBtAndClerkUser({
  claims,
  email,
  orgId,
  client,
  testingSkipClerk,
}: {
  claims: OidcClaims;
  email: string;
  orgId?: string;
  client: BtPgClient;
  testingSkipClerk?: boolean;
}): Promise<UserDetails> {
  const name = claims.user_name ?? "";

  // Find or create Clerk user (skip in testing)
  let clerkUser: ClerkUser | null = null;
  let clerkId: string | null = null;
  if (!testingSkipClerk) {
    clerkUser = await findClerkUserByEmail(email);
    if (!clerkUser) {
      const clerk = await clerkClient();
      clerkUser = await clerk.users.createUser({
        emailAddress: [email],
        password: `${uuid4()}${uuid4()}`,
        firstName: name,
        lastName: "",
      });
    }
    clerkId = clerkUser.id;
  } else {
    // Use some uuid for clerk_id here so we can contain this test-specific hack
    clerkId = uuid4();
  }

  // Use the shared helper to upsert BT user (without Vercel metadata)
  const { auth_id, id } = await upsertBraintrustUser({
    email,
    clerkId,
    firstName: name,
    lastName: "",
    client,
  });

  // Add Vercel user mapping
  await client.query(
    `INSERT INTO vercel_user_mappings (user_id, vercel_user_id)
     VALUES ($1, $2)
     ON CONFLICT (user_id, vercel_user_id) DO NOTHING`,
    [id, claims.user_id],
  );

  // Sync Clerk metadata
  if (!testingSkipClerk) {
    if (!clerkUser) {
      throw new Error("Clerk user not created");
    }

    await syncClerkAuthMetadata({
      clerkUserId: clerkUser.id,
      btAuthId: auth_id,
      groups: undefined,
    });
  }

  // Add to org if specified
  if (orgId) {
    const group = claims.user_role === "ADMIN" ? "Owners" : "Viewers";
    await addMemberToOrg(orgId, id, group, client);
  }

  return userDetailsSchema.parse({
    id,
    auth_id,
    email,
    given_name: name,
    family_name: "",
    vercel_user_ids: [claims.user_id],
  });
}

async function addMemberToOrg(
  orgId: string,
  userId: string,
  group: "Owners" | "Viewers",
  client: BtPgClient,
) {
  try {
    await client.query(
      `SELECT add_member_to_org_unchecked($1::uuid, $2::uuid, array [get_group_id($2::uuid, $3)])`,
      [userId, orgId, group],
    );
  } catch (error) {
    throwUdfError(error);
  }
}

async function upsertBtOrg({
  claims,
  user,
  orgName,
  integrationType,
  accessToken,
  client,
}: {
  claims: OidcClaims;
  user: UserDetails;
  orgName: string;
  integrationType: "marketplace" | "external";
  accessToken: string;
  client: BtPgClient;
}): Promise<OrgDetails> {
  const installationId = claims.installation_id;
  const accountId = claims.account_id;

  // Try to find existing org with same name and account_id
  let existingOrg = await getBtOrg({ accountId, name: orgName }, client);
  if (!existingOrg) {
    // Try to find existing org with same name with account_id suffix and account_id
    existingOrg = await getBtOrg(
      {
        accountId,
        name: `${orgName}-${accountId}`,
      },
      client,
    );
  }

  // Encrypt the token
  const encrypted = await encryptMessage(getEncryptionKey(), accessToken);
  if (!encrypted) {
    throw new Error("Failed to encrypt access token");
  }
  const encryptedToken = JSON.stringify(encrypted);

  let orgId: string;
  let finalOrgName: string | null = null;

  if (!existingOrg) {
    // If no matching org found, create an org using register_org
    try {
      // Don't allow failure to poison the transaction
      await client.query("SAVEPOINT before_register_org");

      orgId = z
        .string()
        .parse(
          (
            await client.query(`SELECT register_org($1, $2) as org_id`, [
              user.auth_id,
              orgName,
            ])
          ).rows[0].org_id,
        );
      finalOrgName = orgName;

      await client.query("RELEASE SAVEPOINT before_register_org");
    } catch (error) {
      await client.query("ROLLBACK TO SAVEPOINT before_register_org");

      if (isObject(error) && error.code === "23505") {
        // Name conflict, try with account suffix
        try {
          const orgNameAccountSuffix = `${orgName}-${accountId}`;
          orgId = z
            .string()
            .parse(
              (
                await client.query(`SELECT register_org($1, $2) as org_id`, [
                  user.auth_id,
                  orgNameAccountSuffix,
                ])
              ).rows[0].org_id,
            );
          finalOrgName = orgNameAccountSuffix;
        } catch (error) {
          throwUdfError(error);
        }
      } else {
        throwUdfError(error);
      }
    }
  } else {
    orgId = existingOrg.id;
    finalOrgName = existingOrg.name;

    await addMemberToOrg(orgId, user.id, "Owners", client);
  }

  // Insert to vercel_installations
  const rows = (
    await client.query(
      `INSERT INTO vercel_installations (installation_id, account_id, org_id, integration_type, access_token)
     VALUES ($1, $2, $3, $4, $5)
     ON CONFLICT (installation_id) DO UPDATE
     SET account_id = $2, org_id = $3, integration_type = $4, access_token = $5
     RETURNING *`,
      [installationId, accountId, orgId, integrationType, encryptedToken],
    )
  ).rows;
  if (rows.length !== 1) {
    throw new Error("Failed to update vercel installation details");
  }
  const installation = vercelInstallationSchema.parse(rows[0]);

  return {
    id: orgId,
    name: finalOrgName,
    vercel_installation: installation,
  };
}

export async function installIntegration(
  claims: OidcClaims,
  request: InstallIntegrationRequest & {
    type: "marketplace" | "external";
  },
  testingOnlyVercelAuthOverride?: boolean,
): Promise<InstallationResponse> {
  if (request.type === "external") {
    throw new Error("TODO: unimplemented");
  }

  if (!request.account.name) {
    throw new JsonHTTPError(400, {
      error: {
        code: "missing_account_details",
        message: "Request is missing account name",
      },
    });
  }
  const orgName = request.account.name;

  // Register user with the given email and name
  const email = request.account.contact.email;

  // Create or update user in database
  return await withTx(async (client) => {
    const user = await createBtAndClerkUser({
      claims,
      email,
      client,
      testingSkipClerk: testingOnlyVercelAuthOverride,
    });

    // Create organization for this integration and add user as Owner
    await upsertBtOrg({
      claims,
      user: user,
      orgName,
      integrationType: request.type,
      accessToken: request.credentials.access_token,
      client,
    });

    return {
      billingPlan: billingPlans[0],
    };
  });
}

const orgWithInstallationRowSchema = z
  .object({
    id: z.string(),
    name: z.string(),
    vi_id: z.string(),
    installation_id: z.string(),
    account_id: z.string(),
    org_id: z.string(),
    integration_type: z.enum(["marketplace", "external"]),
    created: z.string(),
  })
  .transform((row) => ({
    id: row.id,
    name: row.name,
    vercel_installation: {
      id: row.vi_id,
      installation_id: row.installation_id,
      account_id: row.account_id,
      org_id: row.org_id,
      integration_type: row.integration_type,
      created: row.created,
    },
  }));

type VercelOrgIdentifier =
  | {
      installationId: string;
    }
  | {
      accountId: string;
      name: string;
    };

async function getBtOrg(
  params: VercelOrgIdentifier,
  client: BtPgClient,
): Promise<OrgDetails | null> {
  const queryParams = new SqlQueryParams();

  const filter =
    "installationId" in params
      ? `vi.installation_id = ${queryParams.add(params.installationId)}`
      : `vi.account_id = ${queryParams.add(params.accountId)} and o.name = ${queryParams.add(params.name)}`;

  const result = await client.query(
    `SELECT o.id, o.name,
            vi.id as vi_id,
            vi.installation_id,
            vi.account_id,
            vi.org_id,
            vi.integration_type,
            vi.created
     FROM vercel_installations vi
     JOIN organizations o ON vi.org_id = o.id
     WHERE ${filter}`,
    queryParams.params,
  );

  if (result.rows.length === 0) {
    return null;
  } else if (result.rows.length > 1) {
    throw new Error("Multiple organizations found for installation");
  }

  return orgWithInstallationRowSchema.parse(result.rows[0]);
}

type VercelCallbackContext = {
  org: OrgDetails;
  project: ProjectDetails | null;
  user: UserDetails | null;
};

export async function getVercelCallbackObjects(
  claims: OidcClaims,
  resourceId?: string,
): Promise<VercelCallbackContext | null> {
  let org: OrgDetails | null = null;
  let project: ProjectDetails | null = null;
  let user: UserDetails | null = null;

  return await withTx(async (client) => {
    if (resourceId) {
      const btObjects = await getBtResourceObjects(
        claims.installation_id,
        resourceId,
        client,
      );
      if (!btObjects) {
        return null;
      }
      org = btObjects.org;
      project = btObjects.project;
    } else {
      org = await getBtOrg({ installationId: claims.installation_id }, client);
      if (!org) {
        return null;
      }
    }

    user = await getBtUser(claims, org, client);

    return { org, project, user };
  });
}

export async function getVercelWebhookObjects(
  event: MarketplaceMemberChangedEvent,
  client: BtPgClient,
): Promise<{
  org: OrgDetails;
  user?: UserDetails;
  is_last_owner?: boolean;
} | null> {
  const { installationId, memberId } = event.payload;

  const org = await getBtOrg({ installationId: installationId }, client);
  if (!org) {
    return null;
  }

  // Fetch the specified user
  const queryParams = new SqlQueryParams();
  const query = `
    SELECT u.id, u.auth_id, u.email, u.given_name, u.family_name, vum.vercel_user_id
    FROM members m
    JOIN users u ON u.id = m.user_id
    JOIN vercel_user_mappings vum ON vum.user_id = m.user_id
    WHERE m.org_id = ${queryParams.add(org.id)} AND
        vum.vercel_user_id = ${queryParams.add(memberId)}
    LIMIT 1`;

  const rows = (await client.query(query, queryParams.params)).rows;
  if (rows.length === 0) {
    return { org };
  }

  const user = userDetailsSchema.parse({
    ...rows[0],
    vercel_user_ids: [rows[0].vercel_user_id],
  });

  // Determine if the user is the last owner of the org
  const ownerResult = await client.query(
    `
    with
      is_org_owner as (
        select exists(
          select 1
          from group_users gu
          join groups g on g.id = gu.group_id
          join users u on u.id = gu.user_id
          where g.org_id = $1 and g.name = 'Owners' and u.id = $2
        )
      ),
      owner_count as (
        select count(*) as count
        from group_users gu
        join groups g on g.id = gu.group_id
        where g.org_id = $1 and g.name = 'Owners'
      )
    select is_org_owner.exists and owner_count.count = 1 as is_last_owner
    from owner_count join is_org_owner on true
  `,
    [org.id, user.id],
  );

  return {
    org,
    user: user,
    is_last_owner: z.boolean().parse(ownerResult.rows[0].is_last_owner),
  };
}

// If user claims, find the specified user. If system claims, find the first owner
// in the organization.
async function getBtUser(
  claims: AnyClaims,
  org: OrgDetails,
  client: BtPgClient,
): Promise<UserDetails | null> {
  const isUserAuth = "user_id" in claims;

  // Fetch the specified user (or any owner in the org if system auth)
  const queryParams = new SqlQueryParams();
  const query = `
    SELECT u.id, u.auth_id, u.email, u.given_name, u.family_name, vum.vercel_user_id
    FROM groups g
    JOIN group_users gu ON g.id = gu.group_id
    JOIN users u ON u.id = gu.user_id
    JOIN vercel_user_mappings vum ON vum.user_id = gu.user_id
    WHERE g.org_id = ${queryParams.add(org.id)} AND ${
      isUserAuth
        ? `vum.vercel_user_id = ${queryParams.add(claims.user_id)}`
        : `g.name = 'Owners'`
    }
    LIMIT 1`;

  const rows = (await client.query(query, queryParams.params)).rows;
  if (rows.length === 0) {
    return null;
  }

  const user = userDetailsSchema.parse({
    ...rows[0],
    vercel_user_ids: [rows[0].vercel_user_id],
  });

  // Check if email has changed
  if (isUserAuth && claims.user_email && user.email !== claims.user_email) {
    throw new JsonHTTPError(403, {
      error: {
        code: "user_email_changed",
        fields: null,
        message: "User authentication failed due to email change",
        user: {
          message:
            "Your email address has changed. Please contact support for assistance.",
          url: null,
        },
      },
    });
  }

  return user;
}

async function getProjectByResourceId(
  resourceId: string, // NOTE: we use braintrust project id as the resource id provided to vercel
  installationId: string,
  client: BtPgClient,
): Promise<ProjectDetails | null> {
  const projectRows = (
    await client.query(
      `SELECT id, name, internal_metadata FROM projects WHERE
      id = $1 AND
      internal_metadata->'vercel'->>'installation_id' = $2`,
      [resourceId, installationId],
    )
  ).rows;

  if (projectRows.length === 0) {
    return null;
  } else if (projectRows.length > 1) {
    throw new Error("Multiple projects found for resource");
  }

  const project = projectDetailsSchema.parse(projectRows[0]);

  // Check if resource is deleted
  if (!project || project.internal_metadata?.vercel.deleted_at) {
    return null;
  }

  return project;
}

async function getServiceTokenByResourceId(
  orgId: string,
  resourceId: string,
  installationId: string,
  client: BtPgClient,
): Promise<ServiceTokenDetails | null> {
  const tokenRows = (
    await client.query(
      `SELECT id, name, internal_metadata FROM api_keys WHERE
      org_id = $1
      AND internal_metadata->'vercel'->>'resource_id' = $2
      AND internal_metadata->'vercel'->>'installation_id' = $3`,
      [orgId, resourceId, installationId],
    )
  ).rows;

  if (tokenRows.length === 0) {
    return null;
  } else if (tokenRows.length > 1) {
    throw new Error("Multiple service tokens found for resource");
  }

  return serviceTokenDetailsSchema.parse(tokenRows[0]);
}

async function getServiceAccountByResourceId(
  orgId: string,
  resourceId: string,
  installationId: string,
  client: BtPgClient,
): Promise<ServiceAccountDetails | null> {
  const accountRows = (
    await client.query(
      `SELECT id, given_name as name, internal_metadata FROM users u
      JOIN members m ON u.id = m.user_id
      WHERE
      m.org_id = $1
      AND u.user_type = 'service_account'
      AND u.internal_metadata->'vercel'->>'resource_id' = $2
      AND internal_metadata->'vercel'->>'installation_id' = $3`,
      [orgId, resourceId, installationId],
    )
  ).rows;

  if (accountRows.length === 0) {
    return null;
  } else if (accountRows.length > 1) {
    throw new Error("Multiple service accounts found for resource");
  }

  return serviceAccountDetailsSchema.parse(accountRows[0]);
}

export async function getInstallation(
  _claims: AnyClaims,
  installationId: string,
): Promise<Integration | null> {
  return await withTx(async (client) => {
    const org = await getBtOrg({ installationId }, client);
    if (!org) {
      return null;
    }

    const installation = org.vercel_installation;
    return {
      installation_id: installationId,
      integration_type: installation.integration_type,
      billing_plan_id: "free", // TODO: Stub for now
    };
  });
}

export async function getInstallationAccessToken(
  installationId: string,
): Promise<string | null> {
  return await withTx(async (client) => {
    const org = await getBtOrg({ installationId }, client);
    if (!org) {
      return null;
    }

    const rows = (
      await client.query(
        `SELECT access_token FROM vercel_installations WHERE installation_id = $1`,
        [installationId],
      )
    ).rows;
    if (rows.length === 0) {
      return null;
    } else if (rows.length > 1) {
      throw new Error("Multiple installations found for installation id");
    }

    const encryptedTokenParsed = encryptedMessageSchema.parse(
      JSON.parse(z.string().parse(rows[0].access_token)),
    );
    const decrypted = await decryptMessage(
      getEncryptionKey(),
      encryptedTokenParsed.iv,
      encryptedTokenParsed.data,
    );
    return decrypted ?? null;
  });
}

export async function updateInstallation(
  claims: OidcClaims,
  _billingPlanId: string,
  passedClient?: BtPgClient,
): Promise<void> {
  return await withTx(async (client) => {
    const org = await getBtOrg(
      { installationId: claims.installation_id },
      client,
    );
    if (!org) {
      throw new JsonHTTPError(403, {
        error: {
          code: "forbidden",
          fields: null,
          message: "Access denied to this installation",
          user: {
            message: "Access denied to this installation",
            url: null,
          },
        },
      });
    }

    // TODO: Implement billing plan updates when ready
    // For now, billing plans are stubbed out
  }, passedClient);
}

export async function uninstallInstallation(
  claims: AnyClaims,
  installationId: string,
  cascadeResourceDeletion: boolean,
): Promise<DeleteIntegrationResponse> {
  return await withTx(async (client) => {
    const org = await getBtOrg({ installationId }, client);
    if (!org) {
      return undefined;
    }

    if (cascadeResourceDeletion) {
      let user: UserDetails | null = null;
      // NOTE: the integration can be uninstalled with "system" auth
      // where we don't have a user id in the claims. In this case
      // getBtUser will select an arbitrary org owner.
      user = await getBtUser(claims, org, client);
      if (!user) {
        throw new Error("User not found");
      }
      const authLookup = {
        org_id: org.id,
        user_id: user.id,
        auth_id: user.auth_id,
      };

      // Delete all service tokens with this installation_id
      const serviceTokenQueryParams = new SqlQueryParams();
      const serviceTokenQuery = `
        SELECT id FROM api_keys WHERE
        org_id = ${serviceTokenQueryParams.add(authLookup.org_id)}
        AND internal_metadata->'vercel'->>'installation_id' = ${serviceTokenQueryParams.add(
          installationId,
        )}`;

      await deleteObjects({
        fullResultsQueryOverride: serviceTokenQuery,
        baseTableOverride: "api_key",
        startingParams: serviceTokenQueryParams,
        fullResultsSize: undefined,
      });

      // Evict all service accounts with this installation_id
      const accountRows = (
        await client.query(
          `SELECT u.id FROM users u
          JOIN members m ON m.user_id = u.id
          WHERE m.org_id = $1
          AND u.internal_metadata->'vercel'->>'installation_id' = $2
          AND u.user_type = 'service_account'`,
          [authLookup.org_id, installationId],
        )
      ).rows;

      const rows = z.array(z.object({ id: z.string() })).parse(accountRows);
      if (rows.length > 0) {
        await removeMembers(
          {
            orgId: authLookup.org_id!,
            users: { ids: rows.map((row) => row.id) },
          },
          authLookup,
          { client },
        );
      }

      // Mark the project as deleted but don't actually delete it
      await client.query(
        `UPDATE projects
          SET internal_metadata = jsonb_insert(internal_metadata, '{vercel, deleted_at}', to_jsonb(now()))
          WHERE org_id = $1
          AND internal_metadata->'vercel'->>'installation_id' = $2`,
        [authLookup.org_id, installationId],
      );
    }

    // Delete the installation from vercel_installations table
    await client.query(
      `DELETE FROM vercel_installations WHERE installation_id = $1`,
      [installationId],
    );

    // Add suffix to org so it is not used again on re-installation
    await client.query(`UPDATE organizations SET name = $2 WHERE id = $1`, [
      org.id,
      `${org.name}-uninstalled-${uuid4()}`,
    ]);

    // XXX Installation is finalized immediately if it's on a free plan.
    // XXX otherwise cancel the plan (it has a 100% off coupon since we bill through vercel)
    const billingPlan = billingPlans[0];
    return { finalized: billingPlan?.paymentMethodRequired === false };
  });
}

export async function provisionResource(
  claims: OidcClaims,
  request: ProvisionResourceRequest,
): Promise<ProvisionResourceResponse> {
  const billingPlan = billingPlanMap.get(request.billingPlanId);
  if (!billingPlan) {
    throw new JsonHTTPError(400, {
      error: {
        code: "validation_error",
        fields: null,
        message: `Unknown billing plan: ${request.billingPlanId}`,
        user: {
          message: `Unknown billing plan: ${request.billingPlanId}`,
          url: null,
        },
      },
    });
  }

  return await withTx(async (client) => {
    // Get org and owner for this installation
    const org = await getBtOrg(
      { installationId: claims.installation_id },
      client,
    );
    if (!org) {
      throw new JsonHTTPError(403, {
        error: {
          code: "forbidden",
          fields: null,
          message: "No organization found for this installation",
          user: {
            message: "No organization found for this installation",
            url: null,
          },
        },
      });
    }

    const user = await getBtUser(claims, org, client);

    if (!user) {
      throw new Error("User not found");
    }
    const authLookup = {
      org_id: org.id,
      user_id: user.id,
      auth_id: user.auth_id,
    };

    // Create project using the resource name
    const projectResult = await client.query(
      `SELECT register_project($1, $2, $3) as project`,
      [user.auth_id, request.name, org.id],
    );

    if (projectResult.rows.length === 0) {
      throw new JsonHTTPError(500, {
        error: {
          code: "project_creation_failed",
          fields: null,
          message: "Failed to create project",
          user: {
            message: "Failed to create project",
            url: null,
          },
        },
      });
    }

    const project = z
      .object({ project: projectDetailsSchema })
      .parse(projectResult.rows[0].project).project;

    await updateInstallation(claims, request.billingPlanId, client);

    // Store resource ID in project metadata for bi-directional lookup
    await updateProject(
      project.id,
      {
        internal_metadata: {
          ...project.internal_metadata,
          vercel: {
            ...project.internal_metadata?.vercel,
            installation_id: claims.installation_id,
          },
        },
      },
      authLookup,
      client,
    );

    // Create service account for this resource
    const serviceAccountName = `vercel-account-${request.name}-${uuid4().substring(0, 8)}`;
    const addMembersResult = await addMembers(
      {
        orgId: org.id,
        users: {
          service_accounts: [
            {
              name: serviceAccountName,
              token_name: null, // Don't create token yet
            },
          ],
        },
      },
      authLookup,
      { client },
    );

    if (
      !addMembersResult.added_users ||
      addMembersResult.added_users.length === 0
    ) {
      throw new JsonHTTPError(500, {
        error: {
          code: "service_account_creation_failed",
          fields: null,
          message: "Failed to create service account",
          user: {
            message: "Failed to create service account",
            url: null,
          },
        },
      });
    }

    const serviceAccountId = addMembersResult.added_users[0].id;

    // Update service account metadata
    await client.query(
      `UPDATE users
       SET internal_metadata = COALESCE(internal_metadata, '{}') || $2
       WHERE id = $1`,
      [
        serviceAccountId,
        JSON.stringify({
          vercel: {
            installation_id: claims.installation_id,
            resource_id: project.id,
          },
        }),
      ],
    );

    // Add project-scoped ACLs
    const aclResult = await batchUpdateHelper(
      {
        add_acls: [
          {
            object_type: "project",
            object_id: project.id,
            user_id: serviceAccountId,
            permission: "create",
          },
          {
            object_type: "project",
            object_id: project.id,
            user_id: serviceAccountId,
            permission: "read",
          },
          {
            object_type: "project",
            object_id: project.id,
            user_id: serviceAccountId,
            permission: "update",
            restrict_object_type: "project_log",
          },
        ],
      },
      authLookup,
      client,
    );

    if (!aclResult) {
      throw new JsonHTTPError(500, {
        error: {
          code: "acl_creation_failed",
          fields: null,
          message: "Failed to create ACLs",
          user: {
            message: "Failed to create ACLs",
            url: null,
          },
        },
      });
    }

    // Create service token
    const tokenName = `vercel-token-${request.name}`;
    const tokenResult = await createServiceTokenHelper(
      {
        service_account_id: serviceAccountId,
        service_token_name: tokenName,
      },
      authLookup,
      client,
    );

    // Update service token metadata
    await client.query(
      `UPDATE api_keys
       SET internal_metadata = COALESCE(internal_metadata, '{}') || $2
       WHERE id = $1`,
      [
        tokenResult.id,
        JSON.stringify({
          vercel: {
            installation_id: claims.installation_id,
            resource_id: project.id,
          },
        }),
      ],
    );

    return {
      id: project.id,
      status: "ready",
      name: request.name,
      billingPlan,
      metadata: {
        org_id: org.id,
        user_id: user.id,
        project_id: project.id,
      },
      productId: request.productId,
      secrets: [
        {
          name: "BRAINTRUST_API_KEY",
          value: tokenResult.key,
        },
        {
          name: "BRAINTRUST_PROJECT_ID",
          value: project.id,
        },
      ],
    };
  });
}

type BtResourceObjects = {
  org: OrgDetails;
  project: ProjectDetails;
  service_account?: ServiceAccountDetails;
  service_token?: ServiceTokenDetails;
};

async function getBtResourceObjects(
  installationId: string,
  resourceId: string,
  client: BtPgClient,
): Promise<BtResourceObjects | null> {
  const org = await getBtOrg({ installationId }, client);
  if (!org) {
    return null;
  }
  const project = await getProjectByResourceId(
    resourceId,
    installationId,
    client,
  );
  if (!project) {
    return null;
  }
  const service_account = await getServiceAccountByResourceId(
    org.id,
    resourceId,
    installationId,
    client,
  );
  const service_token = await getServiceTokenByResourceId(
    org.id,
    resourceId,
    installationId,
    client,
  );

  return {
    org,
    project,
    service_account: service_account ?? undefined,
    service_token: service_token ?? undefined,
  };
}

export async function getResource(
  _claims: SystemOidcClaims,
  installationId: string,
  resourceId: string,
): Promise<GetResourceResponse | null> {
  return await withTx(async (client) => {
    const resourceObjects = await getBtResourceObjects(
      installationId,
      resourceId,
      client,
    );
    if (!resourceObjects) {
      return null;
    }
    return buildResource(resourceObjects);
  });
}

function buildResource({ org, project }: BtResourceObjects): Resource {
  return {
    id: project.id,
    status: "ready",
    name: project.name,
    productId: "traces",
    billingPlan: billingPlans[0], // XXX fix
    metadata: {
      org_id: org.id,
      project_id: project.id,
    },
  };
}

export async function updateResource(
  claims: OidcClaims,
  resourceId: string,
  request: UpdateResourceRequest,
): Promise<UpdateResourceResponse> {
  return await withTx(async (client) => {
    const btObjects = await getBtResourceObjects(
      claims.installation_id,
      resourceId,
      client,
    );
    if (!btObjects) {
      throw new JsonHTTPError(404, {
        error: {
          code: "not_found",
          fields: null,
          message: `Resource ${resourceId} not found`,
          user: {
            message: `Resource ${resourceId} not found`,
            url: null,
          },
        },
      });
    }

    const resource = buildResource(btObjects);

    // XXX Ignore billingPlanId since we use installation-level billing
    const {
      name,
      billingPlanId: _billingPlanId,
      // NOTE: ignore vercel request metadata since everything is tracked
      // internally in org/user/project metadata
      metadata: _metadata,
      ...updatedFields
    } = request;

    const newName = name ?? resource.name;
    const nextResource = {
      ...resource,
      ...updatedFields,
      // Keep existing billing plan (installation-level billing)
      billingPlan: resource.billingPlan,
      name: newName,
    };

    // If name changed, update the underlying Braintrust project name
    if (newName != resource.name) {
      const { org, project } = btObjects;
      const user = await getBtUser(claims, org, client);
      if (!user) {
        throw new Error("User not found");
      }
      const authLookup = {
        org_id: org.id,
        user_id: user.id,
        auth_id: user.auth_id,
      };

      await updateProject(
        project.id,
        { name: request.name },
        authLookup,
        client,
      );
    }

    return nextResource;
  });
}

export async function deleteResource(
  claims: OidcClaims,
  resourceId: string,
): Promise<void> {
  return await withTx(async (client) => {
    const btObjects = await getBtResourceObjects(
      claims.installation_id,
      resourceId,
      client,
    );
    if (!btObjects) {
      throw new JsonHTTPError(404, {
        error: {
          code: "not_found",
          fields: null,
          message: `Resource ${resourceId} not found`,
          user: {
            message: `Resource ${resourceId} not found`,
            url: null,
          },
        },
      });
    }

    const { org, project, service_account, service_token } = btObjects;
    const user = await getBtUser(claims, org, client);
    if (!user) {
      throw new Error("User not found");
    }
    const authLookup = {
      org_id: org.id,
      user_id: user.id,
      auth_id: user.auth_id,
    };

    // Delete the service token
    if (service_token) {
      await deleteServiceTokenHelper(
        { id: service_token.id },
        authLookup,
        client,
      );
    }

    // Remove service account from organization
    if (service_account) {
      await removeMembers(
        {
          orgId: org.id,
          users: { ids: [service_account.id] },
        },
        authLookup,
        { client },
      );
    }

    if (project?.internal_metadata?.vercel.deleted_at) {
      return;
    }

    // Mark project as deleted by adding deleted_at timestamp to metadata
    const now = new Date().toISOString();
    await updateProject(
      project.id,
      {
        internal_metadata: {
          ...project.internal_metadata,
          vercel: {
            ...project.internal_metadata?.vercel,
            deleted_at: now,
          },
        },
      },
      authLookup,
      client,
    );
  });
}

export async function getAllBillingPlans(
  _installationId: string,
  _experimental_metadata?: Record<string, unknown>,
): Promise<GetBillingPlansResponse> {
  return { plans: billingPlans };
}
