import { getInstallationAccessToken } from "#/lib/partner/vercel";

export async function fetchVercelApi<T>(
  path: string,
  init?: RequestInit & { installationId?: string; data?: unknown },
): Promise<T> {
  const options = init || {};

  if (options.installationId) {
    const accessToken = await getInstallationAccessToken(
      options.installationId,
    );

    if (!accessToken) {
      throw new Error(
        `Unable to fetch installation access token or installation not found`,
      );
    }

    options.headers = {
      Authorization: `Bearer ${accessToken}`,
    };
  }

  if (options.data) {
    options.body = JSON.stringify(options.data);
    options.headers = {
      ...options.headers,
      "content-type": "application/json",
    };
  }

  const url = `https://vercel.com/api${path}`;

  console.log(`>> ${options.method || "GET"} ${url}`);
  const res = await fetch(url, options);

  console.log(
    `<< ${options.method || "GET"} ${url} ${res.status} ${
      res.statusText
    } ${res.headers.get("X-Vercel-Id")}`,
  );

  if (!res.ok) {
    const errorText = await res.text();
    throw new Error(
      `Request to Vercel API failed: ${res.status} ${
        res.statusText
      } ${errorText}`,
    );
  }

  if (res.headers.get("content-type")?.includes("application/json")) {
    return await res.json();
  }

  const errorText = await res.text();
  throw new Error(`Unexpected response: ${errorText}`);
}
