import type { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";
import { HTTPError } from "#/utils/server-util";
import {
  vercelAuthTypeSchema,
  type VercelAuthType,
  verifyToken,
  verifySystemToken,
  OidcClaimsSchema,
  SystemOidcClaimsSchema,
  type OidcClaims,
  type SystemOidcClaims,
  testingOnlyVercelAuthRequest,
} from "#/lib/vercel/auth";

// JsonHTTPError represents a JSON serializable error message
export class JsonHTTPError extends HTTPError {
  body: unknown;

  constructor(code: number, body: unknown, headers?: Record<string, string>) {
    super(code, JSON.stringify(body), headers);

    this.body = body;
  }

  toString(): string {
    return `HTTPError (code ${this.code}): ${this.message}`;
  }
}

async function checkVercelAuth(
  req: NextApiRequest,
  allowedAuth?: VercelAuthType,
): Promise<OidcClaims | SystemOidcClaims> {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    throw new HTTPError(401, "Missing or invalid authorization header");
  }
  const vercelAuthType = vercelAuthTypeSchema.safeParse(
    req.headers["x-vercel-auth"],
  );
  if (!vercelAuthType.success) {
    throw new HTTPError(401, "Missing or invalid x-vercel-auth header");
  }

  const authType = vercelAuthType.data;
  if (allowedAuth && authType !== allowedAuth) {
    throw new HTTPError(401, "Invalid authentication type");
  }

  const token = authHeader.substring(7);
  const test_overrideAuth = testingOnlyVercelAuthRequest(req);

  try {
    if (authType === "user") {
      if (test_overrideAuth) {
        return OidcClaimsSchema.parse(JSON.parse(test_overrideAuth));
      }

      return await verifyToken(token);
    } else {
      if (test_overrideAuth) {
        return SystemOidcClaimsSchema.parse(JSON.parse(test_overrideAuth));
      }

      return await verifySystemToken(token);
    }
  } catch (error) {
    throw new HTTPError(
      401,
      `Token verification failed: ${error instanceof Error ? error.message : "Unknown error"}`,
    );
  }
}

export async function checkVercelUserAuth(
  req: NextApiRequest,
): Promise<OidcClaims> {
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  return await (checkVercelAuth(req, "user") as Promise<OidcClaims>);
}

export async function checkVercelSystemAuth(
  req: NextApiRequest,
): Promise<SystemOidcClaims> {
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  return await (checkVercelAuth(req, "system") as Promise<SystemOidcClaims>);
}

export function getVercelRequestParams<T extends z.ZodType>(
  params: unknown,
  schema: T,
): z.infer<T> {
  try {
    return schema.parse(params);
  } catch (e) {
    if (e instanceof z.ZodError) {
      throw new JsonHTTPError(400, e.errors);
    } else {
      throw new JsonHTTPError(400, `${e}`);
    }
  }
}

export function parseVercelRequest<T extends z.ZodType>(
  req: NextApiRequest,
  claims: OidcClaims | SystemOidcClaims,
  schema: T,
): z.infer<T> {
  const params = getVercelRequestParams({ ...req.query, ...req.body }, schema);
  if (
    req.method != "GET" &&
    "user_role" in claims &&
    claims.user_role !== "ADMIN"
  ) {
    throw new JsonHTTPError(403, {
      error: {
        code: "forbidden",
        fields: null,
        message: "Access denied to this operation",
        user: {
          message: "Access denied to this operation",
          url: null,
        },
      },
    });
  }

  // Verify the token's installation_id matches the requested one (if not null)
  if (
    claims.installation_id &&
    "installationId" in params &&
    claims.installation_id !== params.installationId
  ) {
    throw new JsonHTTPError(403, {
      error: {
        code: "forbidden",
        fields: null,
        message: "Access denied to this installation",
        user: {
          message: "Access denied to this installation",
          url: null,
        },
      },
    });
  }

  return params;
}

export async function vercelHttpHandleError({
  error,
  res,
}: {
  error: unknown;
  res: NextApiResponse;
}) {
  if (error instanceof HTTPError) {
    res.status(error.code);
    if (error instanceof JsonHTTPError) {
      res.json(error.body);
    } else {
      res.send(error.message);
    }
  } else {
    console.error(`Internal error:\n`, error);
    res.status(500).send(`Internal error`);
  }
}

const requestMethodSchema = z.union([
  z.literal("GET"),
  z.literal("PUT"),
  z.literal("POST"),
  z.literal("PATCH"),
  z.literal("DELETE"),
]);

type RequestMethod = z.infer<typeof requestMethodSchema>;

type RequestOpts<T extends z.ZodType> = {
  paramsSchema: T;
  authType?: VercelAuthType;
  handler: (
    claims: OidcClaims | SystemOidcClaims,
    params: z.infer<T>,
  ) => Promise<unknown>;
  outputSchema: z.ZodType;
};

export async function runVercelRequest<
  Get extends z.ZodType,
  Put extends z.ZodType,
  Post extends z.ZodType,
  Patch extends z.ZodType,
  Delete extends z.ZodType,
>(
  req: NextApiRequest,
  res: NextApiResponse,
  opts: {
    GET?: RequestOpts<Get>;
    PUT?: RequestOpts<Put>;
    POST?: RequestOpts<Post>;
    PATCH?: RequestOpts<Patch>;
    DELETE?: RequestOpts<Delete>;
  },
) {
  const methodParsed = requestMethodSchema.safeParse(req.method);
  let method: RequestMethod | null = null;
  if (!methodParsed.success) {
    res.setHeader("Allow", Object.keys(opts));
    res.status(405).json({ error: `Method ${req.method} not allowed` });
    return;
  }
  method = methodParsed.data;
  const methodOpts = opts[method];
  if (!methodOpts) {
    res.setHeader("Allow", Object.keys(opts));
    res.status(405).json({ error: `Method ${req.method} not allowed` });
    return;
  }
  const { authType, paramsSchema, handler, outputSchema } = methodOpts;

  try {
    const claims = await checkVercelAuth(req, authType);
    const params = parseVercelRequest(req, claims, paramsSchema);
    const rawOutput = await handler(claims, params);
    const output = outputSchema.parse(rawOutput);
    const statusCode =
      method === "POST" ? 201 : output === undefined ? 204 : 200;
    res.status(statusCode).json(output);
  } catch (error) {
    await vercelHttpHandleError({ error, res });
  }
  res.end();
}
