import type { NextApiRequest } from "next";
import { createRemoteJWKSet, jwtVerify } from "jose";
import { z } from "zod";

const TESTING_ONLY_VERCEL_AUTH_OVERRIDE =
  process.env.TESTING_ONLY_VERCEL_AUTH_OVERRIDE == "true";
export const testingOnlyVercelAuthRequest = (req: NextApiRequest) => {
  if (TESTING_ONLY_VERCEL_AUTH_OVERRIDE) {
    const parsed = z
      .string()
      .safeParse(req.headers["x-bt-testing-only-vercel-auth-override"]);
    return parsed.success ? parsed.data : null;
  }

  return null;
};

const VERCEL_MARKETPLACE_URL = "https://marketplace.vercel.com";
const VERCEL_API_URL = "https://api.vercel.com";
const JWKS = createRemoteJWKSet(
  new URL(`${VERCEL_MARKETPLACE_URL}/.well-known/jwks`),
);

export const vercelAuthTypeSchema = z.union([
  z.literal("user"),
  z.literal("system"),
]);
export type VercelAuthType = z.infer<typeof vercelAuthTypeSchema>;

// System auth schema - no user fields, installation_id can be null
export const SystemOidcClaimsSchema = z.object({
  sub: z.string(),
  aud: z.string(),
  iss: z.string(),
  exp: z.number(),
  iat: z.number(),
  account_id: z.string(),
  installation_id: z.string().nullable(),
  type: z.enum(["access_token", "id_token"]).optional(),
});

// User auth schema - includes all system fields plus user fields
export const OidcClaimsSchema = z.object({
  sub: z.string(),
  aud: z.string(),
  iss: z.string(),
  exp: z.number(),
  iat: z.number(),
  account_id: z.string(),
  installation_id: z.string(),
  type: z.enum(["access_token", "id_token"]).optional(),
  user_id: z.string(),
  user_role: z.enum(["ADMIN", "USER"]),
  user_name: z.string().optional(),
  user_avatar_url: z.string().optional(),
  user_email: z.string().optional(),
});

export type SystemOidcClaims = z.infer<typeof SystemOidcClaimsSchema>;
export type OidcClaims = z.infer<typeof OidcClaimsSchema>;
export type AnyClaims = OidcClaims | SystemOidcClaims;

export async function verifyToken(token: string): Promise<OidcClaims> {
  const clientId = process.env.VERCEL_INTEGRATION_CLIENT_ID;

  if (!clientId) {
    throw new Error(
      "Missing VERCEL_INTEGRATION_CLIENT_ID environment variable.",
    );
  }

  const { payload } = await jwtVerify(token, JWKS, {
    audience: clientId,
    issuer: VERCEL_MARKETPLACE_URL,
  });

  return OidcClaimsSchema.parse(payload);
}

export async function verifySystemToken(
  token: string,
): Promise<SystemOidcClaims> {
  const clientId = process.env.VERCEL_INTEGRATION_CLIENT_ID;

  if (!clientId) {
    throw new Error(
      "Missing VERCEL_INTEGRATION_CLIENT_ID environment variable.",
    );
  }

  const { payload } = await jwtVerify(token, JWKS, {
    audience: clientId,
    issuer: VERCEL_MARKETPLACE_URL,
  });

  return SystemOidcClaimsSchema.parse(payload);
}

const TokenExchangeResponseSchema = z.object({
  id_token: z.string(),
});

const _tokenRequestSchema = z.object({
  code: z.string(),
  client_id: z.string(),
  client_secret: z.string(),
  state: z.string().optional(),
  redirect_uri: z.string().url().optional(),
});

type TokenRequestSchema = z.infer<typeof _tokenRequestSchema>;

export async function exchangeCodeForToken(
  code: string,
  state?: string | null,
  redirectUri?: string,
): Promise<string> {
  const clientId = process.env.VERCEL_INTEGRATION_CLIENT_ID;
  const clientSecret = process.env.VERCEL_INTEGRATION_CLIENT_SECRET;

  if (!clientId || !clientSecret) {
    throw new Error(
      "Missing Vercel integration credentials. Please set VERCEL_INTEGRATION_CLIENT_ID and VERCEL_INTEGRATION_CLIENT_SECRET environment variables.",
    );
  }

  const tokenRequest: TokenRequestSchema = {
    code,
    client_id: clientId,
    client_secret: clientSecret,
  };

  // Only include state if it's provided
  if (state) {
    tokenRequest.state = state;
  }

  // Include redirect_uri if provided
  if (redirectUri) {
    tokenRequest.redirect_uri = redirectUri;
  }

  console.log("Attempting token exchange with Vercel...", {
    url: `${VERCEL_API_URL}/v1/integrations/sso/token`,
    hasState: !!state,
  });

  const response = await fetch(`${VERCEL_API_URL}/v1/integrations/sso/token`, {
    method: "POST",
    headers: {
      "content-type": "application/json",
    },
    body: JSON.stringify(tokenRequest),
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error("Token exchange failed:", {
      status: response.status,
      statusText: response.statusText,
      error: errorText,
      request: {
        code: code.substring(0, 5) + "...",
        state,
        client_id: clientId.substring(0, 5) + "...",
      },
    });
    throw new Error(
      `Failed to exchange code for token: ${response.status} ${response.statusText} - ${errorText}`,
    );
  }

  const data = await response.json();
  const parsed = TokenExchangeResponseSchema.parse(data);

  return parsed.id_token;
}
