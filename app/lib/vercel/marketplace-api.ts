import { fetchVercelApi } from "./api";
import type {
  ResourceStatus,
  BillingData,
  CreateInvoiceRequest,
  ImportResourceRequest,
  ImportResourceResponse,
  Invoice,
  InvoiceDiscount,
  RefundInvoiceRequest,
} from "./schemas";

interface InstallationUpdatedEvent {
  type: "installation.updated";
  billingPlanId?: string;
}

interface ResourceUpdatedEvent {
  type: "resource.updated";
  productId: string;
  resourceId: string;
}

type IntegrationEvent = InstallationUpdatedEvent | ResourceUpdatedEvent;

export async function dispatchEvent(
  installationId: string,
  event: IntegrationEvent,
): Promise<void> {
  await fetchVercelApi(`/v1/installations/${installationId}/events`, {
    installationId,
    method: "POST",
    data: { event },
  });
}

export type AccountInfo = {
  name: string;
  contact: {
    email: string;
    name: string;
  };
};

export async function getAccountInfo(
  installationId: string,
): Promise<AccountInfo> {
  return await fetchVercelApi<AccountInfo>(
    `/v1/installations/${installationId}/account`,
    {
      installationId,
    },
  );
}

export type Resource = {
  partnerId: string;
  productId: string;
  name: string;
  metadata: Record<string, unknown>;
  status: ResourceStatus;
};

export async function getIntegrationResources(
  installationId: string,
): Promise<{ resources: Resource[] }> {
  return await fetchVercelApi<{ resources: Resource[] }>(
    `/v1/installations/${installationId}/resources`,
    {
      installationId,
    },
  );
}

export type Project = {
  id: string;
  name: string;
  accountId: string;
};

export async function getProject(
  installationId: string,
  projectId: string,
): Promise<Project> {
  return await fetchVercelApi<Project>(`/v9/projects/${projectId}`, {
    installationId,
  });
}

export type Check = {
  name: string;
  id: string;
  isRerequestable: boolean;
  requires: "build-ready" | "deployment-url" | "none";
  targets?: ("preview" | "production" | string)[];
  blocks?:
    | "build-start"
    | `deployment-start`
    | "deployment-alias"
    | "deployment-promotion"
    | "none";
  timeout?: number; // default to 5 mins
};

export async function createCheck(
  installation_id: string,
  resource_id: string,
  projectId: string,
  name: string,
  isRerequestable: string,
  requires: string,
  blocks: string,
  targets: string,
  timeout: number,
) {
  await fetchVercelApi(`/v2/projects/${projectId}/checks`, {
    method: "POST",
    installationId: installation_id,
    data: {
      source: { kind: "integration", externalResourceId: resource_id },
      name,
      isRerequestable: isRerequestable === "on",
      requires,
      blocks,
      targets: targets.split(",").map((target) => target.trim()),
      timeout,
    },
  });
}

export async function updateCheckRun(
  installation_id: string,
  checkRunId: string,
  deploymentId: string,
  updates: {
    status: "queued" | "running" | "completed";
    conclusion?:
      | "canceled"
      | "skipped"
      | "timeout"
      | "failed"
      | "neutral"
      | "succeeded";
    externalId?: string;
    externalUrl?: string;
    output?: unknown;
  },
) {
  await fetchVercelApi(
    `/v2/deployments/${deploymentId}/check-runs/${checkRunId}`,
    {
      method: "PATCH",
      installationId: installation_id,
      data: updates,
    },
  );
}

export async function updateSecrets(
  _installationId: string,
  _resourceId: string,
  _secrets: {
    name: string;
    value: string;
    environmentOverrides?: Record<string, string>;
  }[],
): Promise<void> {
  throw new Error("TODO: unimplemented");
}

export async function importResource(
  installationId: string,
  resourceId: string,
  request: ImportResourceRequest,
): Promise<ImportResourceResponse> {
  return await fetchVercelApi<ImportResourceResponse>(
    `/v1/installations/${installationId}/resources/${resourceId}`,
    {
      installationId,
      method: "PUT",
      data: request,
    },
  );
}

export async function sendBillingData(
  installationId: string,
  data: BillingData,
): Promise<void> {
  await fetchVercelApi(`/v1/installations/${installationId}/billing`, {
    installationId,
    method: "POST",
    data,
  });
}

export async function getInvoice(
  installationId: string,
  invoiceId: string,
): Promise<Invoice> {
  return await fetchVercelApi<Invoice>(
    `/v1/installations/${installationId}/billing/invoices/${invoiceId}`,
    {
      installationId,
    },
  );
}

export async function submitInvoice(
  installationId: string,
  opts?: { test?: boolean; maxAmount?: number; discountPercent?: number },
): Promise<{ invoiceId: string }> {
  const test = opts?.test ?? false;
  const maxAmount = opts?.maxAmount ?? undefined;

  // XXX use actual billing data
  const billingData: BillingData = {
    timestamp: new Date().toISOString(),
    eod: new Date().toISOString(),
    period: {
      start: new Date().toISOString(),
      end: new Date().toISOString(),
    },
    billing: [],
    usage: [],
  };

  let items = billingData.billing.filter((item) => Boolean(item.resourceId));
  if (maxAmount !== undefined) {
    const total = items.reduce((acc, item) => acc + parseFloat(item.total), 0);
    if (total > maxAmount) {
      const ratio = maxAmount / total;
      items = items.map((item) => ({
        ...item,
        quantity: item.quantity * ratio,
        total: (parseFloat(item.total) * ratio).toFixed(2),
      }));
    }
  }

  const discounts: InvoiceDiscount[] = [];
  if (opts?.discountPercent !== undefined && opts.discountPercent > 0) {
    const total = items.reduce((acc, item) => acc + parseFloat(item.total), 0);
    if (total > 0) {
      const discount = total * opts.discountPercent;
      discounts.push({
        resourceId: undefined,
        billingPlanId: items[0].billingPlanId,
        name: "Discount1",
        amount: discount.toFixed(2),
      });
    }
  }

  const invoiceRequest: CreateInvoiceRequest = {
    test: test ? { result: "paid", validate: false } : undefined,
    externalId: new Date().toISOString().replace(/[^0-9]/g, ""),
    invoiceDate: new Date().toISOString(),
    period: billingData.period,
    items:
      items.length > 0
        ? items.map((item) => ({
            resourceId: item.resourceId!,
            billingPlanId: item.billingPlanId,
            name: item.name,
            price: item.price,
            quantity: item.quantity,
            units: item.units,
            total: item.total,
          }))
        : [
            {
              billingPlanId: "pro200",
              name: "Lone item. Maybe final invoice?",
              price: "1.80",
              quantity: 1,
              units: "n/a",
              total: "1.80",
            },
          ],
    discounts: discounts.map((discount) => ({
      resourceId: discount.resourceId!,
      billingPlanId: discount.billingPlanId,
      name: discount.name,
      amount: discount.amount,
    })),
  };
  console.log("Submitting invoice:", invoiceRequest);
  return await fetchVercelApi<{ invoiceId: string }>(
    `/v1/installations/${installationId}/billing/invoices`,
    {
      installationId,
      method: "POST",
      data: invoiceRequest,
    },
  );
}

export async function refundInvoice(
  installationId: string,
  invoiceId: string,
  total: string,
  reason: string,
): Promise<{ invoiceId: string }> {
  return await fetchVercelApi<{ invoiceId: string }>(
    `/v1/installations/${installationId}/billing/invoices/${invoiceId}/actions`,
    {
      installationId,
      method: "POST",
      data: {
        action: "refund",
        total,
        reason,
      } satisfies RefundInvoiceRequest,
    },
  );
}
