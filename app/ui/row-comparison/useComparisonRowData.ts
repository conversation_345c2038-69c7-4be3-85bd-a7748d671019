import { useMemo } from "react";
import { type ComparisonExperimentSpanSummary } from "#/app/app/[org]/p/[project]/experiments/[experiment]/(queries)/useExperiment";
import {
  useRowComparisonParams,
  useComparisonExperimentId,
} from "#/ui/query-parameters";
import { dbQuery, useDuckDB } from "#/utils/duckdb";
import { makeComparisonKeySQL } from "@braintrust/local/query";
import { useQuery } from "@tanstack/react-query";
import { Vector } from "apache-arrow";
import { z } from "zod";

export function useComparisonRowData<TsData>({
  sourceData,
  comparisonKey,
  comparisonExperimentData,
  experimentIndexOverride,
}: {
  sourceData:
    | {
        type: "rowOriginal";
        row: TsData;
      }
    | {
        type: "query";
        enabled: boolean;
      }
    | undefined;
  comparisonKey: string;
  comparisonExperimentData?: ComparisonExperimentSpanSummary[];
  experimentIndexOverride?: number;
}) {
  const [selectedComparisonParams, setSelectedComparisonParams] =
    useRowComparisonParams();

  const [comparisonExperimentId, setComparisonExperimentId] =
    useComparisonExperimentId();

  const comparisonExperimentIndex =
    experimentIndexOverride ??
    Math.max(
      comparisonExperimentData?.findIndex(
        ({ id }) => id === comparisonExperimentId,
      ) ?? -1,
      0,
    );
  const comparisonExperiment =
    comparisonExperimentData?.[comparisonExperimentIndex];

  const shouldQuery =
    sourceData && sourceData.type === "query" && sourceData.enabled;
  const scan =
    comparisonExperiment?.summary === undefined
      ? comparisonExperiment?.scan
      : comparisonExperiment?.summary?.scan;

  const query =
    scan && comparisonKey && shouldQuery
      ? `
    SELECT root_span_id FROM (
        SELECT root_span_id FROM (${scan}) comparison
        WHERE ${comparisonExperiment?.summary === undefined ? "comparison.is_root" : "true"} AND COALESCE(MD5(${makeComparisonKeySQL({ comparisonKey: null, alias: "comparison" })}), '<empty>') = '${comparisonKey}'
    ) ORDER BY root_span_id
  `
      : null;

  const duck = useDuckDB();
  const queryEnabled = !!duck && !!comparisonExperiment && !!query;
  const { data, isPending } = useQuery({
    queryKey: [
      [comparisonExperiment?.id, comparisonExperiment?.tempTableName],
      "rowComparisonQuery",
      query,
    ],
    queryFn: async ({ signal }: { signal: AbortSignal }) => {
      const conn = await duck!.connect();
      const result = await dbQuery(conn, signal, query);
      return result?.toArray().map((r) => `${r?.root_span_id}`);
    },
    placeholderData: undefined,
    staleTime: Infinity,
    enabled: queryEnabled,
  });

  const comparisonRootSpanIds: string[] = useMemo(
    () =>
      queryEnabled
        ? (data ?? [])
        : sourceData?.type === "rowOriginal"
          ? parseRowOriginalCompareIds(
              sourceData.row,
              `e${comparisonExperimentIndex + 2}`,
            )
          : [],
    [queryEnabled, data, sourceData, comparisonExperimentIndex],
  );

  const urlComparisonRootSpanId =
    selectedComparisonParams?.[comparisonKey]?.[comparisonExperiment?.id ?? ""];

  const comparisonRootSpanId: string | undefined =
    comparisonRootSpanIds.find((id) => id === urlComparisonRootSpanId) ??
    comparisonRootSpanIds[0];

  return {
    comparisonExperiment,
    comparisonExperimentIndex,
    comparisonRootSpanIds,
    comparisonRootSpanId,
    urlComparisonRootSpanId,
    isPending,
    queryEnabled,
    setSelectedComparisonParams,
    setComparisonExperimentId,
  };
}

const btInternalSchema = z.object({
  __bt_internal: z.record(
    z.string(),
    z.object({
      compareIds: z.custom<Vector>((data) => data instanceof Vector).nullish(),
    }),
  ),
});

function parseRowOriginalCompareIds<TsData>(
  rowOriginal: TsData,
  experimentAlias: string,
): string[] {
  const parsed = btInternalSchema.safeParse(rowOriginal);
  if (!parsed.success) {
    return [];
  }

  return (
    parsed.data.__bt_internal[experimentAlias]?.compareIds?.toArray() ?? []
  );
}
