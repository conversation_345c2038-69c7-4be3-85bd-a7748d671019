import { <PERSON><PERSON> } from "#/ui/button";
import { ActionButton } from "./ActionButton";
import {
  ArrowDown,
  ArrowUp,
  Fullscreen,
  Minimize,
  Search,
  XIcon,
} from "lucide-react";
import { CopyToClipboardButton } from "#/ui/copy-to-clipboard-button";
import { type PropsWithChildren } from "react";
import { useTraceFullscreen } from "./use-trace-fullscreen";
import { cn } from "#/utils/classnames";
import { TraceLayoutDropdown } from "./trace-layout-dropdown";

export const TraceHeader = ({
  onPrevRow,
  onNextRow,
  onToggleSearch,
  onClose,
  rootSpanId,
  rowIndex,
  isDataset,
  hideNavButtons,
  children,
}: PropsWithChildren<{
  onPrevRow?: (opts?: { withTransition?: boolean }) => void;
  onNextRow?: (opts?: { withTransition?: boolean }) => void;
  onToggleSearch?: VoidFunction;
  onClose: VoidFunction;
  rootSpanId: string | null;
  rowIndex?: number;
  hideNavButtons?: boolean;
  isDataset?: boolean;
}>) => {
  const { isTraceFullscreen, isNarrowViewport, setFullscreenState } =
    useTraceFullscreen();
  return (
    <div className="@container flex flex-col border-b border-primary-200/70">
      <div className={cn("flex h-12 flex-none items-center gap-1 px-3")}>
        {!isNarrowViewport && (
          <ActionButton
            buttonVariant={isTraceFullscreen ? "default" : "border"}
            actionHandler={() => {
              setFullscreenState(isTraceFullscreen ? null : "1");
            }}
            tooltipText="Toggle fullscreen trace"
            hotkey={["Shift+F"]}
            icon={
              isTraceFullscreen ? (
                <Minimize className="size-3" />
              ) : (
                <Fullscreen className="size-3" />
              )
            }
          />
        )}
        {!hideNavButtons && (
          <>
            <Button
              size="xs"
              variant="ghost"
              disabled={!onPrevRow}
              onClick={() => onPrevRow?.({ withTransition: true })}
              Icon={ArrowUp}
            />
            <Button
              size="xs"
              variant="ghost"
              disabled={!onNextRow}
              onClick={() => onNextRow?.({ withTransition: true })}
              Icon={ArrowDown}
            />
          </>
        )}
        {rootSpanId && (
          <div className="flex grow items-baseline gap-2 truncate">
            <CopyToClipboardButton
              size="xs"
              variant="ghost"
              textToCopy={rootSpanId}
              copyMessage={`Copy root_span_id to clipboard`}
              copiedMessage={`Copied root_span_id!`}
              className="inline-block max-w-full truncate px-1 font-mono text-xs text-primary-500"
            >
              {rootSpanId}
            </CopyToClipboardButton>
          </div>
        )}
        <Button
          size="xs"
          onClick={() => {
            onClose();
            setFullscreenState(null);
          }}
          Icon={XIcon}
        />
      </div>
      <div className="flex gap-1 px-3 pb-2">
        {isDataset ? (
          <div className="flex flex-1 items-center text-xs font-medium">
            Edit dataset row {rowIndex}
          </div>
        ) : (
          <TraceLayoutDropdown variant="tabs" />
        )}
        {children}
        {onToggleSearch && (
          <ActionButton
            hotkey={["Mod+F"]}
            buttonVariant="ghost"
            className="text-primary-500"
            labelClassName="hidden @lg:block"
            label="Find"
            tooltipText={isDataset ? "Find in dataset row" : "Find in trace"}
            icon={<Search className="size-3" />}
            actionHandler={onToggleSearch}
          />
        )}
      </div>
    </div>
  );
};
