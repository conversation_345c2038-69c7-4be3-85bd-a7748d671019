import {
  createContext,
  type Dispatch,
  type SetStateAction,
  useCallback,
  useContext,
  useDeferredValue,
  useEffect,
  useMemo,
  useState,
} from "react";
import { useDebouncedCallback } from "#/utils/useDebouncedCallback";
import { isObject, spanTypeAttributeValues } from "braintrust/util";
import {
  fetchBtql,
  fetchBtqlPaginated,
  useFetchBtqlOptions,
} from "#/utils/btql/btql";
import { useBtqlQueryBuilder } from "#/utils/btql/use-query-builder";
import { type DataObjectType } from "#/utils/btapi/btapi";
import {
  useInfiniteQuery,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { type Expr } from "@braintrust/btql/parser";
import { z } from "zod";
import { getSpanDisplayConfig } from "./span-display";
import {
  type SpanData,
  type Span,
  spanAttributesSchema,
} from "@braintrust/local";
import { type SpanIdsMap } from "./graph";
import { unknownToString } from "./search-utils";
import { processFullSpanData, useRealtimeSpans } from "./use-load-full-spans";
import { makeFullSpanQueryKey } from "./loading/query-utils";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";

export type SearchResultField = {
  spanRowId: string;
  primarySpanId?: string;
  field: string;
};

export type TraceSearchResult = {
  id: string;
  comparison?: {
    /**
     * In the case of diff mode, span ids in the diffed trace are keyed off the primary trace's span id.
     * So for comparison search results, we need to know the primary trace's span id.
     *
     * In the case where the span exists only in the comparison trace, the span_id will correspond
     * to the comparison trace's span id, so this can be omitted
     */
    primarySpanId?: string;
    index: number;
    name?: string;
  };
  data: SearchResult;
  type: string;
  matches: {
    field: string;
    value: string;
  }[];
};

type SearchQueryProps = {
  isPending: boolean;
  isLoading: boolean;
  isFetching: boolean;
  hasNextPage: boolean;
  fetchNextPage: () => void;
};

const TraceSearchSettersContext = createContext<{
  setSearchQuery: Dispatch<SetStateAction<string>>;
  setSearchOpen: Dispatch<SetStateAction<boolean>>;
  setResultIndex: Dispatch<SetStateAction<number | null>>;
  setSearchResults: Dispatch<SetStateAction<TraceSearchResult[] | undefined>>;
  setSearchQueryProps: Dispatch<SetStateAction<SearchQueryProps>>;
  setSelectedIndex: Dispatch<SetStateAction<number>>;
  setActiveSpans: Dispatch<SetStateAction<SpanData[]>>;
  setSelectedSpanIds: Dispatch<SetStateAction<Set<string>>>;
  setSpanFieldsToSearch: Dispatch<SetStateAction<Record<string, boolean>>>;
  setSpanTypesToSearch: Dispatch<SetStateAction<Record<string, boolean>>>;
  resetSearch: () => void;
}>({
  setSearchQuery: () => {},
  setSearchOpen: () => {},
  setResultIndex: () => {},
  setSearchResults: () => {},
  setSearchQueryProps: () => {},
  setSelectedIndex: () => {},
  setActiveSpans: () => {},
  setSelectedSpanIds: () => {},
  setSpanFieldsToSearch: () => {},
  setSpanTypesToSearch: () => {},
  resetSearch: () => {},
});

const TraceSearchGettersContext = createContext<{
  searchQuery: string;
  isSearchOpen: boolean;
  /** used for span section highlighting */
  searchResultFields: SearchResultField[];
  /** this is the selected span in the search result box */
  resultIndex: number | null;
  searchResults: TraceSearchResult[] | undefined;
  searchQueryProps: {
    isPending: boolean;
    isLoading: boolean;
    isFetching: boolean;
    hasNextPage: boolean;
    fetchNextPage: () => void;
  };
  selectedIndex: number;
  /**
   * this is used to preserve the selected span at the top of the list
   * when a new search query is entered
   */
  activeSpans: SpanData[];
  selectedSpanIds: Set<string>;
  spanFieldsToSearch: Record<string, boolean>;
  spanTypesToSearch: Record<string, boolean>;
}>({
  searchQuery: "",
  isSearchOpen: false,
  searchResultFields: [],
  resultIndex: null,
  searchQueryProps: {
    isPending: true,
    isLoading: false,
    isFetching: false,
    hasNextPage: false,
    fetchNextPage: () => {},
  },
  searchResults: [],
  selectedIndex: 0,
  activeSpans: [],
  selectedSpanIds: new Set(),
  spanFieldsToSearch: {},
  spanTypesToSearch: {},
});

export const TraceSearchProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [spanFieldsToSearch, setSpanFieldsToSearch] = useState<
    Record<string, boolean>
  >(
    Object.fromEntries(
      Object.keys(searchResultQueryDataSchema.shape).map((key) => [key, true]),
    ),
  );

  const [spanTypesToSearch, setSpanTypesToSearch] = useState<
    Record<string, boolean>
  >(Object.fromEntries(searchSpanTypes.map((type) => [type, true])));
  // this is the text that's shown in the UI
  const [searchText, setSearchText] = useState("");
  // this is a debounced value for querying
  const [searchQueryRaw, _setSearchQuery] = useState("");
  const debouncedSetSearchQuery = useDebouncedCallback(_setSearchQuery, 700);

  const setSearchQuery = useCallback(
    (v: SetStateAction<string>) => {
      setSearchText(v);
      debouncedSetSearchQuery(v);
    },
    [setSearchText, debouncedSetSearchQuery],
  );
  const searchQuery = useDeferredValue(searchQueryRaw);
  const [isSearchOpen, setSearchOpen] = useState(false);
  const [activeSpans, setActiveSpans] = useState<SpanData[]>([]);
  // this is the selected span in the search result box
  const [resultIndex, setResultIndex] = useState<number | null>(null);
  const [searchResultsRaw, setSearchResults] = useState<
    TraceSearchResult[] | undefined
  >(undefined);
  const searchResults = useDeferredValue(searchResultsRaw);
  const searchResultFields = useMemo(
    () =>
      (searchResults ?? []).flatMap(({ data, comparison, matches }) =>
        (matches ?? []).map(({ field }) => ({
          spanRowId: data.span_id,
          primarySpanId: comparison?.primarySpanId,
          field,
        })),
      ),
    [searchResults],
  );
  const [searchQueryProps, setSearchQueryProps] = useState<SearchQueryProps>({
    isPending: true,
    isLoading: false,
    isFetching: false,
    hasNextPage: false,
    fetchNextPage: () => {},
  });

  // This is the keyboard interaction index
  const [selectedIndex, setSelectedIndex] = useState<number>(0);
  const [selectedSpanIds, setSelectedSpanIds] = useState<Set<string>>(
    new Set(),
  );

  const resetSearch = useCallback(() => {
    setSearchQuery("");
    setSearchOpen(false);
    setResultIndex(null);
    setSearchResults([]);
    setSelectedIndex(0);
  }, [setResultIndex, setSearchResults, setSelectedIndex, setSearchQuery]);

  useEffect(() => {
    if (!isSearchOpen) {
      resetSearch();
    }
  }, [isSearchOpen, resetSearch]);

  return (
    <TraceSearchSettersContext.Provider
      value={useMemo(
        () => ({
          setSearchQuery,
          setSearchOpen,
          setResultIndex,
          setSearchResults,
          setSearchQueryProps,
          setSelectedIndex,
          setActiveSpans,
          setSelectedSpanIds,
          setSpanFieldsToSearch,
          setSpanTypesToSearch,
          resetSearch,
        }),
        [
          setSearchQuery,
          setSearchOpen,
          setResultIndex,
          setSearchResults,
          setSearchQueryProps,
          setSelectedIndex,
          setActiveSpans,
          setSelectedSpanIds,
          setSpanFieldsToSearch,
          setSpanTypesToSearch,
          resetSearch,
        ],
      )}
    >
      <TraceSearchGettersContext.Provider
        value={useMemo(
          () => ({
            searchText,
            searchQuery,
            isSearchOpen,
            searchResultFields,
            resultIndex,
            searchResults,
            searchQueryProps,
            selectedIndex,
            activeSpans,
            selectedSpanIds,
            spanFieldsToSearch,
            spanTypesToSearch,
          }),
          [
            isSearchOpen,
            resultIndex,
            searchText,
            searchQuery,
            searchResultFields,
            searchResults,
            searchQueryProps,
            selectedIndex,
            activeSpans,
            selectedSpanIds,
            spanFieldsToSearch,
            spanTypesToSearch,
          ],
        )}
      >
        {children}
      </TraceSearchGettersContext.Provider>
    </TraceSearchSettersContext.Provider>
  );
};

export const useTraceSearchGetters = () => {
  return useContext(TraceSearchGettersContext);
};

export const useTraceSearchSetters = () => {
  return useContext(TraceSearchSettersContext);
};

export const useTraceSearch = () => {
  return {
    ...useTraceSearchGetters(),
    ...useTraceSearchSetters(),
  };
};

const searchSpanTypes = [...spanTypeAttributeValues, "other"];

const searchResultQueryDataSchema = z.object({
  name: z.string().nullish(),
  scores: z.record(z.string(), z.number().nullable()).nullish(),
  input: z.any().nullish(),
  output: z.any().nullish(),
  expected: z.any().nullish(),
  metadata: z.any().nullish(),
  tags: z.array(z.string()).nullish(),
});

const _searchResultSchema = z
  .object({
    id: z.string(),
    span_id: z.string(),
    root_span_id: z.string().nullish(),
    span_attributes: spanAttributesSchema.nullish(),
  })
  .extend(searchResultQueryDataSchema.shape);

type SearchResult = z.infer<typeof _searchResultSchema> &
  Record<string, unknown>;

type SearchParams = {
  objectType: DataObjectType;
  objectId: string;
  rootSpanIds: string[];
};

export function useTraceSearchQuery({
  primarySearchParams,
  comparisonSearch,
  spanIdsMap,
}: {
  primarySearchParams?: SearchParams;
  comparisonSearch?: {
    params: SearchParams;
    comparisonExperimentIndex: number;
  };
  spanIdsMap?: SpanIdsMap;
}) {
  const {
    isSearchOpen,
    searchQuery,
    spanFieldsToSearch,
    spanTypesToSearch,
    setSearchResults,
    setSearchQueryProps,
    activeSpans,
  } = useTraceSearch();
  const queryClient = useQueryClient();
  useEffect(() => {
    if (!isSearchOpen || !searchQuery) {
      queryClient.removeQueries({
        queryKey: ["traceSearch"],
      });
    }
  }, [isSearchOpen, searchQuery, queryClient]);

  useEffect(() => {
    return () => {
      queryClient.resetQueries({ queryKey: ["traceSearch"] });
    };
  }, [queryClient]);

  const { result: primaryQueryResult, enabled: primaryQueryEnabled } =
    usePaginatedSearchQuery({
      searchParams: primarySearchParams,
      spanFieldsToSearch,
      spanTypesToSearch,
      searchQuery,
      isSearchOpen,
    });

  const { result: comparisonQueryResult, enabled: comparisonQueryEnabled } =
    usePaginatedSearchQuery({
      searchParams: comparisonSearch?.params,
      spanFieldsToSearch,
      spanTypesToSearch,
      searchQuery,
      isSearchOpen,
    });

  const enabled = primaryQueryEnabled || comparisonQueryEnabled;
  const searchData = useMemo(() => {
    if (!enabled) {
      return [];
    }
    return [
      ...(primaryQueryResult.data ?? []),
      ...(comparisonQueryResult.data ?? []),
    ].flatMap(prepareSearchData);
  }, [enabled, primaryQueryResult.data, comparisonQueryResult.data]);

  const searchDataWithActiveSpans = useMemo(() => {
    if (!enabled) {
      return [];
    }
    // put active span at search time to the top of the list
    return [
      ...activeSpans.flatMap((s) => prepareSearchData(s)),
      ...searchData.filter((d) => {
        return !activeSpans.some((s) => s.id === d.data.id);
      }),
    ];
  }, [enabled, searchData, activeSpans]);

  const searchResults = useMemo(() => {
    const fields = Object.keys(spanFieldsToSearch).filter(
      (f) => spanFieldsToSearch[f],
    );

    const searchQueryLower = searchQuery.toLowerCase();
    return searchDataWithActiveSpans.flatMap(({ data, type, stringValues }) => {
      if (!spanTypesToSearch[data.span_attributes?.type ?? "other"]) {
        return [];
      }
      const matches = fields.flatMap((f) => {
        const value = stringValues[f];
        if (value.indexOf(searchQueryLower) === -1) {
          return [];
        }
        return [{ field: f, value }];
      });

      if (matches.length === 0) {
        return [];
      }
      return [
        {
          id: data.id,
          spanId: data.span_id,
          rootSpanId: data.root_span_id,
          data,
          type,
          matches,
        },
      ];
    });
  }, [
    searchDataWithActiveSpans,
    spanFieldsToSearch,
    spanTypesToSearch,
    searchQuery,
  ]);

  const searchResultsWithComparisonInfo: TraceSearchResult[] = useMemo(() => {
    return searchResults.map((result) => {
      const isComparisonSpan = comparisonSearch?.params?.rootSpanIds.includes(
        result.data.root_span_id ?? "",
      );
      const primarySpanId = isComparisonSpan
        ? spanIdsMap?.[result.data.span_id]?.spanId
        : undefined;
      return {
        ...result,
        comparison: comparisonSearch
          ? {
              primarySpanId,
              index: isComparisonSpan
                ? comparisonSearch.comparisonExperimentIndex
                : 0,
            }
          : undefined,
      };
    });
  }, [searchResults, comparisonSearch, spanIdsMap]);

  useEffect(() => {
    setSearchResults(enabled ? searchResultsWithComparisonInfo : undefined);
  }, [enabled, searchResultsWithComparisonInfo, setSearchResults]);

  const fetchNextPrimary = primaryQueryResult.fetchNextPage;
  const fetchNextComparison = comparisonQueryResult.fetchNextPage;
  const fetchNextPage = useCallback(() => {
    if (!searchQuery) {
      return;
    }
    // https://stackoverflow.com/a/72893761
    if (primaryQueryEnabled) {
      fetchNextPrimary({ cancelRefetch: false });
    }
    if (comparisonQueryEnabled) {
      fetchNextComparison({ cancelRefetch: false });
    }
  }, [
    searchQuery,
    primaryQueryEnabled,
    comparisonQueryEnabled,
    fetchNextPrimary,
    fetchNextComparison,
  ]);

  const isPending =
    primaryQueryResult.isPending || comparisonQueryResult.isPending;
  const isLoading =
    primaryQueryResult.isLoading || comparisonQueryResult.isLoading;
  const isFetching =
    primaryQueryResult.isFetching || comparisonQueryResult.isFetching;
  const hasNextPage =
    primaryQueryResult.hasNextPage || comparisonQueryResult.hasNextPage;

  const searchQueryProps = useMemo(() => {
    return {
      isPending,
      isLoading,
      isFetching,
      hasNextPage,
      fetchNextPage,
    };
  }, [isPending, isLoading, hasNextPage, isFetching, fetchNextPage]);

  useEffect(() => {
    setSearchQueryProps(searchQueryProps);
  }, [searchQueryProps, setSearchQueryProps]);
}

function parseSpanAttributes(spanAttributesValue: unknown) {
  try {
    const spanAttributes = spanAttributesSchema.parse(
      typeof spanAttributesValue === "string"
        ? JSON.parse(spanAttributesValue)
        : spanAttributesValue,
    );
    return spanAttributes;
  } catch {
    return undefined;
  }
}

function prepareSearchData(data: SearchResult | null | undefined) {
  if (!data) {
    return [];
  }
  const spanAttributes = parseSpanAttributes(data.span_attributes);
  return [
    {
      data,
      type: spanAttributes?.type ?? "other",
      stringValues: Object.fromEntries(
        Object.keys(searchResultQueryDataSchema.shape).map((k) => {
          const value = k === "name" ? spanAttributes?.name : data[k];
          const stringValue =
            k === "tags" && Array.isArray(value)
              ? (value.join(", ") ?? "")
              : unknownToString(value);
          return [k, stringValue.toLowerCase()];
        }),
      ),
    },
  ];
}

const idSchema = z.object({
  id: z.string(),
});

const dataSchema = z.record(z.string(), z.unknown());

const PAGE_SIZE = 5;

function usePaginatedSearchQuery({
  searchParams,
  spanFieldsToSearch,
  spanTypesToSearch,
  searchQuery,
  isSearchOpen,
}: {
  searchParams?: SearchParams;
  spanFieldsToSearch: Record<string, boolean>;
  spanTypesToSearch: Record<string, boolean>;
  searchQuery: string;
  isSearchOpen: boolean;
}) {
  const btqlOptions = useFetchBtqlOptions();
  const builder = useBtqlQueryBuilder({});

  const fields = Object.keys(spanFieldsToSearch).filter(
    (t) => spanFieldsToSearch[t],
  );
  const spanTypes = Object.keys(spanTypesToSearch).filter(
    (t) => spanTypesToSearch[t],
  );

  const queryEnabled =
    !!searchParams &&
    !!isSearchOpen &&
    fields.length > 0 &&
    spanTypes.length > 0 &&
    !!searchQuery &&
    searchParams.objectType !== "dataset";
  const idsQueryResult = useInfiniteQuery({
    queryKey: [
      "traceSearchIds",
      searchParams?.objectType,
      searchParams?.objectId,
      searchParams?.rootSpanIds,
      fields,
      spanTypes,
      searchQuery,
    ],
    queryFn: async ({ signal, pageParam: cursor }) => {
      return await fetchBtql({
        args: {
          query: {
            from: builder.from(
              searchParams!.objectType,
              searchParams!.objectId ? [searchParams!.objectId] : [],
              "spans",
            ),
            select: [
              {
                alias: "id",
                expr: { btql: "id" },
              },
            ],
            filter: builder.and(
              builder.or(
                ...searchParams!.rootSpanIds.map((rootSpanId) => ({
                  op: "eq" as const,
                  left: { btql: "root_span_id" },
                  right: {
                    op: "literal" as const,
                    value: rootSpanId,
                  },
                })),
              ),
              builder.or(
                ...fields.map((f) => ({
                  btql: `${f} ILIKE '%${searchQuery}%'`,
                })),
              ),
              builder.or(
                ...spanTypes.map(
                  (t): Expr =>
                    t === "other"
                      ? builder.or(
                          ...[
                            {
                              op: "isnull" as const,
                              expr: builder.ident("span_attributes", "type"),
                            },
                            builder.and(
                              ...spanTypeAttributeValues.map(
                                (t): Expr => ({
                                  op: "ne",
                                  left: builder.ident(
                                    "span_attributes",
                                    "type",
                                  ),
                                  right: {
                                    op: "literal",
                                    value: t,
                                  },
                                }),
                              ),
                            ),
                          ],
                        )
                      : {
                          op: "eq",
                          left: builder.ident("span_attributes", "type"),
                          right: {
                            op: "literal",
                            value: t,
                          },
                        },
                ),
              ),
            ),
            limit: PAGE_SIZE,
            ...(cursor ? { cursor } : {}),
          },
          brainstoreRealtime: true,
          useColumnstore: false,
        },
        ...btqlOptions,
        signal,
        schema: idSchema,
      });
    },
    getNextPageParam: (lastPage) => {
      return lastPage.data.length === PAGE_SIZE ? lastPage.cursor : undefined;
    },
    placeholderData: undefined,
    initialPageParam: "",
    enabled: queryEnabled,
    staleTime: Infinity,
  });

  const searchIdsData = idsQueryResult.data;
  const searchIds = useMemo(() => {
    if (searchIdsData?.pages && searchIdsData?.pages.length === 0) {
      return [];
    }

    return searchIdsData?.pages.flatMap((p) => p.data.map((r) => r.id));
  }, [searchIdsData?.pages]);

  const isSpanQueryEnabled = queryEnabled && searchIds && searchIds.length > 0;
  const { projectId } = useContext(ProjectContext);
  const queryClient = useQueryClient();
  const dataQueryResult = useQuery({
    queryKey: [
      "traceSearchSpans",
      searchParams?.objectType,
      searchParams?.objectId,
      searchParams?.rootSpanIds,
      projectId,
      searchIds,
    ],
    queryFn: async ({ signal }: { signal: AbortSignal }) => {
      const { existing, idsToFetch } = (searchIds ?? []).reduce(
        (acc: { existing: SpanData[]; idsToFetch: string[] }, id) => {
          const existing = queryClient.getQueryData(
            makeFullSpanQueryKey(
              searchParams?.objectType,
              searchParams?.objectId,
              projectId,
              id,
            ),
          );
          if (existing && isObject(existing) && "data" in existing) {
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
            acc.existing.push(existing.data as SpanData);
          } else {
            acc.idsToFetch.push(id);
          }
          return acc;
        },
        { existing: [], idsToFetch: [] },
      );
      const res =
        idsToFetch.length > 0
          ? await fetchBtqlPaginated(
              {
                args: {
                  query: {
                    from: builder.from(
                      searchParams!.objectType,
                      searchParams!.objectId ? [searchParams!.objectId] : [],
                      "spans",
                    ),
                    select: [{ op: "star" }],
                    filter: builder.or(
                      ...idsToFetch.map(
                        (id): Expr => ({
                          op: "eq",
                          left: builder.ident("id"),
                          right: {
                            op: "literal",
                            value: id,
                          },
                        }),
                      ),
                    ),
                  },
                  brainstoreRealtime: true,
                  useColumnstore: false,
                },
                ...btqlOptions,
                schema: dataSchema,
                signal,
              },
              PAGE_SIZE,
            )
          : undefined;

      return {
        data: [
          ...existing,
          ...idsToFetch.map((id) =>
            // future: we can search on custom columns as well
            processFullSpanData(
              searchParams!.objectType,
              id,
              res?.data ?? [],
              false,
            ),
          ),
        ],
        realtimeState: res?.realtime_state,
      };
    },
    enabled: isSpanQueryEnabled,
    staleTime: Infinity,
  });

  const queriedData = isSpanQueryEnabled ? dataQueryResult.data : undefined;
  const dataToWatch = useMemo(() => {
    return (queriedData?.data ?? []).map((d) => ({
      data: d,
      customColumnsData: null,
      realtimeState: queriedData?.realtimeState,
    }));
  }, [queriedData]);

  useRealtimeSpans({
    idsToWatch: searchIds ?? [],
    objectType: searchParams?.objectType,
    objectId: searchParams?.objectId,
    queriedSpanData: dataToWatch,
  });

  return useMemo(
    () => ({
      result: {
        data: queriedData?.data,
        fetchNextPage: idsQueryResult.fetchNextPage,
        isPending: idsQueryResult.isPending || dataQueryResult.isPending,
        isLoading: idsQueryResult.isLoading || dataQueryResult.isLoading,
        isFetching: idsQueryResult.isFetching || dataQueryResult.isFetching,
        hasNextPage: idsQueryResult.hasNextPage,
      },
      enabled: queryEnabled,
    }),
    [
      queriedData?.data,
      idsQueryResult.fetchNextPage,
      idsQueryResult.isPending,
      idsQueryResult.isLoading,
      idsQueryResult.isFetching,
      idsQueryResult.hasNextPage,
      dataQueryResult.isPending,
      dataQueryResult.isLoading,
      dataQueryResult.isFetching,
      queryEnabled,
    ],
  );
}

export type UseTraceSearchQueryProps = ReturnType<typeof useTraceSearchQuery>;

export function flattenSearchResults(
  searchResults: TraceSearchResult[] | undefined,
  // deprecated -- search results should hold all relevant info
  spans?: Record<string, Span>,
) {
  return (searchResults ?? []).flatMap((result) => {
    if (spans != null && !spans[result.data.span_id]) {
      return [];
    }
    const spanType = result.data.span_attributes?.type ?? "other";
    const spanConfig = getSpanDisplayConfig({
      type: result.data.span_attributes?.type ?? "other",
      cached: false,
      remote: false,
      hasError: false,
    });
    return (result.matches ?? []).flatMap(({ field, value }) => {
      return !field || !value
        ? []
        : [
            {
              id: result.id,
              spanId: result.data.span_id,
              field,
              value,
              searchData: result.data,
              type: spanType,
              spanConfig,
              comparison: result.comparison,
            },
          ];
    });
  });
}
