import {
  <PERSON><PERSON><PERSON>,
  db<PERSON><PERSON>y,
  type TransactionId,
  TransactionIdField,
  type UpdateLog,
  useDBQuery,
  useDuckDB,
  useParquetView,
} from "#/utils/duckdb";
import { isEmpty } from "#/utils/object";
import { AuditLogSchemaHints, parseShallowStringJSON } from "#/utils/schema";
import { singleQuote } from "#/utils/sql-utils";
import { type AuditLogRow } from "@braintrust/local/api-schema";
import { type Table } from "apache-arrow";
import { useEffect, useMemo, useRef, useState } from "react";
import {
  type DataObjectType,
  type DataObjectSearch,
} from "#/utils/btapi/btapi";
import { makeComparisonKeySQL } from "@braintrust/local/query";
import {
  type CustomColumnsRowParams,
  useExpandedRowCustomColumns,
} from "#/utils/custom-columns/use-expanded-row-custom-columns";
import { keepPreviousData, useQueries, useQuery } from "@tanstack/react-query";
import { combineResults } from "#/utils/react-query";
import {
  getOrderedDiffObjectValues,
  type DiffObjectType,
} from "#/utils/diffs/diff-objects";
import {
  buildTrace,
  diffTraces,
  type LoadedTrace,
  type PreviewTrace,
  traceByRightIds,
} from "./graph";
import { isDiffModeRowId } from "./RowComparisonSelection";
import { useBtqlQueryBuilder } from "#/utils/btql/use-query-builder";

export type ExpandedRowState =
  | "initially_loading"
  | "fully_loaded"
  | "changing_rows"
  | "reloading_row";

export interface UseExpandedRowParams {
  rowId: string | null;
  scan: string | null;
  ready: number[];
  channel: UpdateLog | null;
  dynamicObjectId: string | null;
  objectType: DataObjectType;
  modelSpecScan?: string | null;
  allowEmpty?: boolean;
  customColumnsParams?: CustomColumnsRowParams;
  isFastSummaryEnabled?: boolean;
}

export function useExpandedRow({
  rowId,
  scan: staticScan,
  ready: staticReady,
  channel: staticChannel,
  dynamicObjectId,
  objectType,
  modelSpecScan,
  allowEmpty,
  customColumnsParams,
  isFastSummaryEnabled,
}: UseExpandedRowParams) {
  const [loadingState, setLoadingState] =
    useState<ExpandedRowState>("initially_loading");

  useEffect(() => {
    setLoadingState((curr) => {
      if (curr === "initially_loading") {
        return "initially_loading";
      } else {
        return "changing_rows";
      }
    });
  }, [rowId]);

  // If the dynamicObjectId is set, load the trace directly over the network.
  const traceSearch = useMemo(
    (): DataObjectSearch | undefined =>
      dynamicObjectId
        ? {
            id: dynamicObjectId,
            filters: {
              sql: [],
              btql: [
                {
                  op: "eq",
                  left: { op: "ident", name: ["id"] },
                  right: { op: "literal", value: rowId },
                },
              ],
            },
            shape: "traces",
          }
        : undefined,
    [dynamicObjectId, rowId],
  );

  const {
    refreshed: dynamicRefreshed,
    scan: dynamicScan,
    channel: dynamicChannelFn,
  } = useParquetView({
    objectType,
    search: traceSearch,
    disableCache: true, // We could theoretically cache these, but they'll just gunk up indexeddb
  });

  const [scan, ready, channel] = useMemo(
    () =>
      dynamicObjectId
        ? [dynamicScan, [dynamicRefreshed], dynamicChannelFn()]
        : [staticScan, staticReady, staticChannel],
    [
      dynamicObjectId,
      dynamicScan,
      dynamicRefreshed,
      dynamicChannelFn,
      staticScan,
      staticReady,
      staticChannel,
    ],
  );

  const rootSpanScan = rootSpanScanFn(rowId, scan, true);

  const {
    data: rootSpans,
    hasLoaded: hasRootSpanQueryLoaded,
    loading: isRootSpanQueryLoading,
  } = useDBQuery(rootSpanScan, ready, {
    skipTicketing: true,
  });

  const rootSpansArray = rootSpans?.toArray().map((row) => row.root_span_id);
  const comparisonKey = rootSpans
    ?.toArray()
    .find((row) => row.comparison_key)?.comparison_key;

  const whereClause = whereClauseFn(rootSpansArray);

  const innerSpanScan =
    rowId && scan && whereClause
      ? `
      SELECT ${[
        "*",
        ...(objectType !== "dataset" && modelSpecScan
          ? [`JSON_EXTRACT_STRING("metadata", '$."model"') AS "model"`]
          : []),
      ].join(
        ", ",
      )} FROM (${scan.replaceAll("/*RE_PUSHDOWN_FILTER_SUB*/", whereClause)}) t
      `
      : null;

  const spanScanWithCost =
    objectType !== "dataset" && modelSpecScan && innerSpanScan
      ? `WITH model_specs AS (${modelSpecScan})
    SELECT *,
        (COALESCE(CAST(JSON_EXTRACT("metrics", '$."prompt_tokens"') AS INTEGER), 0) -
         COALESCE(CAST(JSON_EXTRACT("metrics", '$."prompt_cached_tokens"') AS INTEGER), 0) -
         COALESCE(CAST(JSON_EXTRACT("metrics", '$."prompt_cache_creation_tokens"') AS INTEGER), 0)) * "input_cost_per_mil_tokens" / 1000000 +
        COALESCE(CAST(JSON_EXTRACT("metrics", '$."prompt_cached_tokens"') AS INTEGER), 0) * COALESCE("input_cache_read_cost_per_mil_tokens", "input_cost_per_mil_tokens") / 1000000 +
        COALESCE(CAST(JSON_EXTRACT("metrics", '$."prompt_cache_creation_tokens"') AS INTEGER), 0) * COALESCE("input_cache_write_cost_per_mil_tokens", "input_cost_per_mil_tokens") / 1000000 AS "prompt_cost",
        CAST(JSON_EXTRACT("metrics", '$."completion_tokens"') AS INTEGER) * "output_cost_per_mil_tokens" / 1000000 as "completion_cost"
    FROM (
      ${innerSpanScan}
    ) spans LEFT JOIN model_specs ON spans.model = model_specs.model`
      : null;

  const spanScan = spanScanWithCost ?? innerSpanScan;

  const customCols = useExpandedRowCustomColumns({
    rowId,
    params: customColumnsParams,
    whereClause,
    objectType,
    objectId: dynamicObjectId,
    isFastSummaryEnabled,
  });

  // Skip ticketing here because if data loading runs concurrently while we are
  // trying to display an expanded row (e.g. streaming new rows through
  // realtime), each data load will re-trigger the query and cancel out old
  // results. Since we expect the results of loading a single row ID to be
  // unlikely to flicker, we skip ticketing.
  const { data, hasLoaded, loading } = useDBQuery(spanScan, ready, {
    skipTicketing: true,
  });

  const [returnedData, setReturnedData] = useState<Table | null>(null);
  const lastXactId = useRef<TransactionId | null>(null);
  const lastRowId = useRef<string | null>(null);
  const lastColCount = useRef<number | null>(null);

  useEffect(() => {
    if (isEmpty(data)) {
      if (allowEmpty) {
        lastRowId.current = null;
        lastXactId.current = null;
        setReturnedData(null);
      }
      return;
    }

    // If neither the row id, nor the highest transaction value has changed, then this result
    // set is guaranteed to be the same. Note that we're able to make this optimization because we
    // know that this query is just filtering rows (and not, eg. aggregating them).
    //
    // By memoizing the data this way, we avoid recomputing the span constantly as new data arrives
    // externally (e.g. in the logs view, as the table updates). This keeps the trace from re-rendering.
    const dataArray = data.toArray();
    const maxTransactionId = dataArray.reduce((acc, row) => {
      const rowXactId = row.toJSON()[TransactionIdField];
      if (rowXactId > acc) {
        return rowXactId;
      } else {
        return acc;
      }
    }, "");

    const dataRowId = (dataArray.find((r) => r.id === rowId) ?? {}).id;
    if (
      dataRowId == null ||
      (lastXactId.current &&
        lastXactId.current >= maxTransactionId &&
        lastRowId.current &&
        lastRowId.current === dataRowId &&
        lastColCount.current === data.schema.fields.length)
    ) {
      return;
    }

    lastColCount.current = data.schema.fields.length;
    lastRowId.current = dataRowId;
    lastXactId.current = maxTransactionId;
    setReturnedData(data);
    channel?.openRow({ id: dataRowId });
  }, [channel, data, returnedData, rowId, scan, allowEmpty]);

  // If there are no root spans, the spanScan query is never run. We should
  // still consider this data loaded
  const hasFullyLoaded =
    hasLoaded ||
    (hasRootSpanQueryLoaded && rootSpansArray && rootSpansArray.length === 0);

  const isLoading = isRootSpanQueryLoading || loading;

  useEffect(() => {
    if (hasFullyLoaded && !isLoading) {
      setLoadingState("fully_loaded");
    }
  }, [hasFullyLoaded, isLoading]);

  return {
    data: returnedData,
    comparisonKey,
    hasLoaded: hasFullyLoaded,
    isLoading,
    expandedRowState: loadingState,
    customColumns: customCols,
  };
}

const rootSpanScanFn = (
  rowId: string | null,
  scan: string | null,
  withComparisonKey?: boolean,
) => {
  // DuckDB can't push filters down to parquet files unless they are simple, literal
  // equalities. This code tries to optimize the scan queries to take advantage of that.
  // Technically, if there are multiple root spans, the second query will be less optimal, but
  // we don't even handle that in the UI, so it's very unlikely to happen.
  return (
    rowId &&
    scan &&
    `
      SELECT
        root_span_id,
        ${withComparisonKey ? `COALESCE(MD5(${makeComparisonKeySQL({ comparisonKey: null })}), '<empty>') AS comparison_key,` : ""}
       FROM (${scan.replaceAll(
         "/*RE_PUSHDOWN_FILTER_SUB*/",
         `WHERE id = ${singleQuote(rowId)}`,
       )}) base`
  );
};

export const whereClauseFn = (rootSpansArray: string[] | undefined) => {
  return rootSpansArray && rootSpansArray.length === 1
    ? `WHERE root_span_id = ${singleQuote(rootSpansArray[0])}`
    : rootSpansArray && rootSpansArray.length > 1
      ? `WHERE root_span_id IN (${rootSpansArray.map(singleQuote).join(", ")})`
      : null;
};

const spanScanFn = ({
  whereClause,
  objectType,
  scan,
  modelSpecScan,
}: {
  whereClause: string | null;
  objectType: DataObjectType;
  scan: string | null;
  modelSpecScan?: string | null;
}) => {
  const innerSpanScan =
    scan && whereClause
      ? `
      SELECT ${[
        "*",
        ...(objectType !== "dataset" && modelSpecScan
          ? [`JSON_EXTRACT_STRING("metadata", '$."model"') AS "model"`]
          : []),
      ].join(
        ", ",
      )} FROM (${scan.replaceAll("/*RE_PUSHDOWN_FILTER_SUB*/", whereClause)}) t
      `
      : null;

  const spanScanWithCost =
    objectType !== "dataset" && modelSpecScan && innerSpanScan
      ? `WITH model_specs AS (${modelSpecScan})
    SELECT *,
        (COALESCE(CAST(JSON_EXTRACT("metrics", '$."prompt_tokens"') AS INTEGER), 0) -
         COALESCE(CAST(JSON_EXTRACT("metrics", '$."prompt_cached_tokens"') AS INTEGER), 0) -
         COALESCE(CAST(JSON_EXTRACT("metrics", '$."prompt_cache_creation_tokens"') AS INTEGER), 0)) * "input_cost_per_mil_tokens" / 1000000 +
        COALESCE(CAST(JSON_EXTRACT("metrics", '$."prompt_cached_tokens"') AS INTEGER), 0) * COALESCE("input_cache_read_cost_per_mil_tokens", "input_cost_per_mil_tokens") / 1000000 +
        COALESCE(CAST(JSON_EXTRACT("metrics", '$."prompt_cache_creation_tokens"') AS INTEGER), 0) * COALESCE("input_cache_write_cost_per_mil_tokens", "input_cost_per_mil_tokens") / 1000000 AS "prompt_cost",
        CAST(JSON_EXTRACT("metrics", '$."completion_tokens"') AS INTEGER) * "output_cost_per_mil_tokens" / 1000000 as "completion_cost"
    FROM (
      ${innerSpanScan}
    ) spans LEFT JOIN model_specs ON spans.model = model_specs.model`
      : null;

  return spanScanWithCost ?? innerSpanScan;
};

export function useRowAuditLog({
  auditLogScan: staticAuditLogScan,
  auditLogReady: staticAuditLogReady,
  rowId,
  dynamicObjectId,
  objectType,
}: {
  auditLogScan: string | null;
  auditLogReady: number[];
  rowId: string | null;
  dynamicObjectId: string | null;
  objectType: DataObjectType;
}) {
  // If the dynamicObjectId is set, load the trace directly over the network.
  const traceSearch = useMemo(
    (): DataObjectSearch | undefined =>
      dynamicObjectId
        ? {
            audit_log: true,
            id: dynamicObjectId,
            filters: {
              sql: [],
              btql: [
                {
                  op: "eq",
                  left: { op: "ident", name: ["id"] },
                  right: { op: "literal", value: rowId },
                },
              ],
            },
          }
        : undefined,
    [dynamicObjectId, rowId],
  );

  const { refreshed: dynamicRefreshed, scan: dynamicScan } = useParquetView({
    objectType,
    search: traceSearch,
    disableCache: true, // We could theoretically cache these, but they'll just gunk up indexeddb
  });

  const [auditLogScan, auditLogReady] = useMemo(
    () =>
      dynamicObjectId
        ? [dynamicScan, [dynamicRefreshed]]
        : [staticAuditLogScan, staticAuditLogReady],
    [
      dynamicObjectId,
      dynamicScan,
      dynamicRefreshed,
      staticAuditLogScan,
      staticAuditLogReady,
    ],
  );

  const duck = useDuckDB();
  const { data: auditLogData } = useQuery({
    queryKey: ["auditLogData", auditLogScan, auditLogReady, rowId],
    queryFn: async ({ signal }: { signal: AbortSignal }) => {
      const conn = await duck!.connect();
      const auditLogRawData = await dbQuery(
        conn,
        signal,
        `
          SELECT * FROM (${auditLogScan!}) "t"
          WHERE t.origin->>'id' = ${singleQuote(rowId!)}
          ORDER BY ${CreatedField} DESC, ${TransactionIdField} DESC
        `,
      );

      return (
        auditLogRawData?.toArray().map(
          (row) =>
            // TODO: Use zod to validate the audit log schema
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
            parseShallowStringJSON(AuditLogSchemaHints, row) as AuditLogRow,
        ) ?? []
      );
    },
    placeholderData: keepPreviousData,
    enabled: !!auditLogScan && !!rowId && !!duck,
  });

  return {
    auditLogData: auditLogData ?? [],
  };
}

export function useExpandedRowWithComparisons({
  enabled,
  objectId,
  rowId,
  objectType,
  modelSpecScan,
}: {
  enabled: boolean;
  objectId?: string;
  rowId: string | DiffObjectType<string>;
  objectType: DataObjectType;
  modelSpecScan?: string | null;
}) {
  const flattenedRowIds = useMemo(() => {
    if (typeof rowId === "string") {
      return [rowId];
    }
    return getOrderedDiffObjectValues<string>(rowId);
  }, [rowId]);

  const builder = useBtqlQueryBuilder({});

  const traceSearch = useMemo(
    (): DataObjectSearch | undefined =>
      objectId && enabled
        ? {
            id: objectId,
            filters: {
              sql: [],
              btql: [
                builder.or(
                  ...flattenedRowIds.map((id) => ({
                    op: "eq" as const,
                    left: { op: "ident" as const, name: ["id"] },
                    right: { op: "literal" as const, value: id },
                  })),
                ),
              ],
            },
            shape: "traces",
          }
        : undefined,
    [objectId, enabled, flattenedRowIds, builder],
  );

  const { scan, refreshed } = useParquetView({
    objectType,
    search: traceSearch,
    disableCache: true, // We could theoretically cache these, but they'll just gunk up indexeddb
  });

  const duck = useDuckDB();

  const {
    data,
    isLoading: areSpansLoading,
    isPending,
  } = useQueries({
    queries: flattenedRowIds.map((id) => ({
      queryKey: [
        "playgroundSpans",
        objectType,
        scan,
        modelSpecScan,
        refreshed,
        id,
      ],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
        const conn = await duck!.connect();

        const rootSpans = await dbQuery(
          conn,
          signal,
          rootSpanScanFn(id, scan, true),
        );
        const rootSpanIds =
          rootSpans?.toArray()?.map((row) => row.root_span_id) ?? [];
        const whereClause = whereClauseFn(rootSpanIds);
        const spans =
          (await dbQuery(
            conn,
            signal,
            spanScanFn({ whereClause, objectType, scan, modelSpecScan }),
          )) ?? null;

        return spans ? buildTrace(spans) : null;
      },
      gcTime: 0,
      enabled: !!duck && !!scan && refreshed > 0 && flattenedRowIds.length > 0,
    })),
    combine: combineResults,
  });

  const currentTraces = useMemo(() => {
    const [base, ...comparisons] = data;

    if (!base) return [];
    if (isDiffModeRowId(rowId) && comparisons.every((c) => c != null)) {
      const diffedTraces = comparisons.map(
        (c) =>
          diffTraces(
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
            c as unknown as PreviewTrace,
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
            base as unknown as PreviewTrace,
          ).trace,
      );
      return [base, ...diffedTraces];
    } else {
      const comparisonTraces = comparisons.map((c) =>
        traceByRightIds(
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
          c as unknown as PreviewTrace,
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
          base as unknown as PreviewTrace,
        ),
      );
      return [base, ...comparisonTraces];
    }
  }, [data, rowId]);

  // Hacky fix to prevent flashing when changing traces. currentTraces changes as the trace for each prompt is loaded. We
  // want to update the displayed array of traces once all traces are loaded. Doing this in an effect with state
  // would be more "correct", but this would trigger an unnecessary re-render. Instead, we rely on the re-render triggered
  // by the last loaded trace to ensure the ref update is "picked up"
  const traces = useRef<LoadedTrace[]>([]);
  if (currentTraces.length === flattenedRowIds.length) {
    // eslint-disable-next-line react-compiler/react-compiler, @typescript-eslint/consistent-type-assertions
    traces.current = currentTraces as unknown as LoadedTrace[];
  }

  return {
    // eslint-disable-next-line react-compiler/react-compiler
    traces: traces.current,
    numTraces: flattenedRowIds.length,
    traceRowIds: flattenedRowIds,
    hasLoaded:
      !areSpansLoading && currentTraces.length === flattenedRowIds.length,
    isPending,
  };
}
