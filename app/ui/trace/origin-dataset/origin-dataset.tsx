import { db<PERSON><PERSON>y, useD<PERSON><PERSON><PERSON>y, useParquetView } from "#/utils/duckdb";
import { type AsyncDuckDBConnection } from "@duckdb/duckdb-wasm";
import { useMemo, useState } from "react";
import { buildTrace, type LoadedTrace } from "#/ui/trace/graph";
import { DiffRightField, isDiffObject } from "#/utils/diffs/diff-objects";
import { SINGLETON_DATASET_ID } from "#/app/app/[org]/p/[project]/playgrounds/[playground]/playx/stream";
import { SINGLETON_DATASET_SCAN } from "#/app/app/[org]/prompt/[prompt]/mounted-object";
import { type RowId } from "#/ui/query-parameters";

export type OriginDataset = {
  id: string;
  rowId: string;
  originXactId?: string | null;
};

export function getOriginDataset(
  rowId: RowId | null,
): OriginDataset | undefined {
  if (!rowId || !isDiffObject(rowId)) {
    return;
  }

  const {
    objectId,
    originId,
    originObjectId,
    originXactId,
    [DiffRightField]: primaryRowId,
  } = rowId;

  if (rowId.objectType === "dataset" && objectId && primaryRowId) {
    return {
      id: objectId,
      rowId: primaryRowId,
    };
  }

  if (originId && originObjectId) {
    return {
      id: originObjectId,
      rowId: originId,
      originXactId,
    };
  }

  return {
    id: SINGLETON_DATASET_ID,
    rowId: SINGLETON_DATASET_ID,
  };
}

export function useOriginTrace({
  originDataset,
}: {
  originDataset: OriginDataset | undefined;
}) {
  const search = useMemo(() => {
    if (!originDataset || originDataset.id === SINGLETON_DATASET_ID) {
      return;
    }

    return {
      id: originDataset.id,
      filters: {
        sql: [],
        btql: [
          {
            op: "eq",
            left: { btql: "id" },
            right: {
              op: "literal",
              value: originDataset.rowId,
            },
          } as const,
        ],
      },
      limit: 1,
    };
  }, [originDataset]);

  const { refreshed: datasetRowRefreshed, scan: datasetRowScan } =
    useParquetView({
      objectType: "dataset",
      search,
    });

  const [trace, setTrace] = useState<LoadedTrace | null>(null);
  const query =
    originDataset?.id === SINGLETON_DATASET_ID
      ? SINGLETON_DATASET_SCAN
      : // Because realtime does not filter, we need to re-apply the id filter here
        datasetRowScan && originDataset
        ? `SELECT * FROM (${datasetRowScan}) WHERE id = '${originDataset.rowId}'`
        : null;
  const queryFn = useMemo(() => {
    if (!query || !originDataset?.rowId) {
      return null;
    }
    return async (conn: AsyncDuckDBConnection, abortSignal: AbortSignal) => {
      const result = await dbQuery(conn, abortSignal, query);
      if (!result || result.numRows === 0) {
        return null;
      }
      const resultTrace = buildTrace(result);
      if (!resultTrace) {
        return null;
      }

      setTrace(resultTrace);
      return null;
    };
  }, [originDataset?.rowId, query, setTrace]);

  const { hasLoaded } = useDBQuery(
    queryFn,
    originDataset?.id === SINGLETON_DATASET_ID ? [1] : [datasetRowRefreshed],
  );

  return {
    originDataset,
    hasLoaded,
    trace,
  };
}
