import { type DataObjectType } from "#/utils/btapi/btapi";
import { dataObjectTypes } from "#/utils/btapi/load-bt-cache-db";
import { type RowId } from "#/ui/query-parameters";
import { isDiffObject } from "#/utils/diffs/diff-objects";
import { z } from "zod";

const rowSchema = z.object({
  span_type_info: z.string(),
});

const spanTypeInfoSchema = z.object({
  generation: z.string().nullish(),
});

export function hasGeneration(row: unknown) {
  const parsed = rowSchema.safeParse(row);
  if (!parsed.success) {
    return false;
  }

  let spanTypeInfo;
  try {
    spanTypeInfo = spanTypeInfoSchema.parse(
      JSON.parse(parsed.data.span_type_info),
    );
  } catch (e) {
    return false;
  }

  return !!spanTypeInfo.generation;
}

export function getExpandedRowParamsPlayground(rowId: RowId) {
  if (!isDiffObject(rowId)) {
    return null;
  }
  const objectType = rowId.objectType;
  if (!objectType || !isDataObjectType(objectType)) {
    return null;
  }
  const objectId = rowId.objectId;
  if (!objectId) {
    return null;
  }

  return { objectId, objectType };
}

function isDataObjectType(objectType: string): objectType is DataObjectType {
  return dataObjectTypes.some((t) => t === objectType);
}
