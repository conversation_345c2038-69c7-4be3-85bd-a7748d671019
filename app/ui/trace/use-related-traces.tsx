import { useQueries, useQuery, useQueryClient } from "@tanstack/react-query";
import { type DataObjectType } from "#/utils/btapi/btapi";
import {
  makeFullSpanQueryKey,
  makeSpanOverviewProjection,
  type TraceQueryResult,
} from "./loading/query-utils";
import { makeFullSpanQueryFn } from "./use-load-full-spans";
import { useCallback, useContext, useEffect, useMemo, useState } from "react";
import { type PreviewTrace } from "./graph";
import { useIsFeatureEnabled } from "#/lib/feature-flags";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { type CustomColumnDefinition } from "#/utils/custom-columns/use-custom-columns";
import {
  getGroupedSpanIdsFn,
  getGroupedSpanIdsQueryKey,
  type GroupingData,
  parseGroupForRow,
  type RowData,
} from "#/utils/grouping";
import { useTraceViewTypeState } from "#/ui/query-parameters";
import { makeTraceQueryKey } from "./loading/query-utils";
import { type CustomColumn } from "@braintrust/typespecs";
import {
  type SpanOverviewRow,
  spanOverviewSchema,
} from "./loading/schema-utils";
import {
  type BTQLResponse,
  fetchBtqlPaginated,
  useFetchBtqlOptions,
  useIsRootBtqlSnippet,
} from "#/utils/btql/btql";
import { useBtqlQueryBuilder } from "#/utils/btql/use-query-builder";
import { combineResults } from "#/utils/react-query";
import {
  buildVirtualTrace,
  makeTraceQueryFn,
  type TraceQueryKeyParams,
} from "#/ui/trace/loading/query-utils";
import { type ModelCosts } from "#/ui/prompts/models";

const RELATED_ROW_IDS_PAGE_SIZE = 5;

export type TracePaginationProps = {
  fetchNextPage: () => void;
  fetchPreviousPage: () => void;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  isFetching: boolean;
  allRelatedRowIds: RowData[];
  fetchedRelatedRowIds: RowData[];
  // to help top-pagination
  lowestLoadedIndex: number | null | undefined;
  traces: PreviewTrace[];
};

export function useRelatedTraces({
  primaryId,
  objectId,
  objectType,
  grouping,
  customColumns,
  trace,
  modelCosts,
}: {
  primaryId: string | null;
  objectId: string | null;
  objectType: DataObjectType;
  grouping?: string;
  customColumns?: CustomColumn[] | CustomColumnDefinition[];
  trace: PreviewTrace | null;
  modelCosts?: Record<string, ModelCosts>;
}): {
  tracePaginationProps: TracePaginationProps | undefined;
  groupingData: GroupingData | undefined;
  isLoading: boolean;
  buildTraceError: Error | null;
} {
  const { projectId } = useContext(ProjectContext);
  const builder = useBtqlQueryBuilder({});
  const btqlOptions = useFetchBtqlOptions();
  const fastExperimentSummary = useIsFeatureEnabled("fastExperimentSummary");
  const loadCustomColumnsFromScope = useIsFeatureEnabled(
    "loadCustomColumnsFromScope",
  );
  const isRootBtqlSnippet = useIsRootBtqlSnippet();
  const queryClient = useQueryClient();
  const { data: rootData, isLoading: isRootDataLoading } = useQuery({
    queryKey: makeFullSpanQueryKey(
      objectType,
      objectId ?? "",
      projectId,
      primaryId,
      true,
      customColumns,
    ),
    queryFn: makeFullSpanQueryFn(
      builder,
      btqlOptions,
      fastExperimentSummary,
      loadCustomColumnsFromScope,
      isRootBtqlSnippet,
      queryClient,
      customColumns,
      true,
    ),
    enabled: !!primaryId && !!grouping,
    staleTime: Infinity,
  });

  const groupingData = useMemo(() => {
    return parseGroupForRow(rootData?.data, grouping);
  }, [rootData?.data, grouping]);

  const isGroupingQueryEnabled = !!groupingData;

  const { data: groupedRowIds, isFetching: isGroupedRowsQueryFetching } =
    useQuery({
      queryKey: getGroupedSpanIdsQueryKey({
        objectType,
        objectId,
        groupingData,
      }),
      queryFn: getGroupedSpanIdsFn({
        builder,
        btqlOptions,
        isRootBtqlSnippet,
        objectType,
        objectId,
        groupingData,
      }),
      throwOnError: false,
      placeholderData: undefined,
      enabled: isGroupingQueryEnabled,
    });

  const [viewType] = useTraceViewTypeState(projectId);
  const isThreadView = viewType === "thread";
  const { relatedRowIds: allRelatedRowIds, rowIdIndex } = useMemo(() => {
    const sortedRowIds =
      groupedRowIds?.data.toSorted((a, b) =>
        a._pagination_key > b._pagination_key ? 1 : -1,
      ) ?? [];

    const rowIdIndex = sortedRowIds.findIndex(
      (d) => d.root_span_id === primaryId || d.id === primaryId,
    );
    return {
      relatedRowIds: sortedRowIds,
      // in thread view, only paginate down to simplify things
      rowIdIndex: isThreadView ? 0 : rowIdIndex === -1 ? null : rowIdIndex,
    };
  }, [groupedRowIds?.data, primaryId, isThreadView]);

  const {
    relatedIdsToFetch,
    hasPreviousPage,
    hasNextPage,
    fetchPreviousPage,
    fetchNextPage,
    hasRelatedRowIds,
  } = usePaginatedIds({
    rowIdIndex,
    isThreadView,
    allRowIds: allRelatedRowIds,
    primaryId,
  });

  const isQueryEnabled = !!primaryId && !!objectId;
  const isRelatedSpansQueryEnabled =
    isQueryEnabled && isGroupingQueryEnabled && relatedIdsToFetch.length > 0;

  const queryIds = useMemo(
    () => relatedIdsToFetch.map((d) => d.root_span_id),
    [relatedIdsToFetch],
  );
  const {
    data: relatedSpansData,
    isPending: isGroupingQueryPending,
    isFetching: isGroupQueryFetching,
  } = useQuery(
    {
      queryKey: ["traceRelatedSpans", objectType, objectId, queryIds],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
        const selectProjection = makeSpanOverviewProjection({
          builder,
          objectType,
        });
        const { existing, idsToFetch } = queryIds.reduce(
          (
            acc: {
              existing: Record<string, SpanOverviewRow[]>;
              idsToFetch: string[];
            },
            rootSpanId,
          ) => {
            const existing = queryClient.getQueryData<TraceQueryResult>(
              makeTraceQueryKey({ objectType, objectId, traceId: rootSpanId }),
            );
            if (existing?.rootSpanId && existing?.queryData.data) {
              acc.existing[existing.rootSpanId] = existing.queryData.data;
            } else {
              acc.idsToFetch.push(rootSpanId);
            }
            return acc;
          },
          { existing: {}, idsToFetch: [] },
        );

        const allSpansBtqlResult = await (idsToFetch.length > 0
          ? fetchBtqlPaginated(
              {
                args: {
                  query: {
                    filter: builder.or(
                      ...idsToFetch.map((rootSpanId) => ({
                        op: "eq" as const,
                        left: { btql: "root_span_id" },
                        right: {
                          op: "literal" as const,
                          value: rootSpanId,
                        },
                      })),
                    ),
                    from: builder.from(
                      objectType,
                      objectId ? [objectId] : [],
                      "traces",
                    ),
                    select: selectProjection,
                    limit: 1,
                  },
                  brainstoreRealtime: true,
                },
                ...btqlOptions,
                schema: spanOverviewSchema,
                signal,
              },
              1000,
            )
          : Promise.resolve({ data: [] }));

        const groupedRows = allSpansBtqlResult.data.reduce(
          (acc: Record<string, SpanOverviewRow[]>, d) => {
            acc[d.root_span_id] ??= [];
            acc[d.root_span_id].push(d);
            return acc;
          },
          existing ?? {},
        );
        return {
          // keep the btql result data which includes realtime_state and schema
          ...allSpansBtqlResult,
          data: groupedRows,
        };
      },
      enabled: isRelatedSpansQueryEnabled,
      throwOnError: false,
      staleTime: Infinity,
    },
    queryClient,
  );

  const cachedData = useRealtimeTraces({
    rootSpanIdsToWatch: queryIds,
    objectType,
    objectId,
    queriedSpanData: relatedSpansData,
    enabled: !!(objectType && objectId && groupingData),
  });

  const {
    traces,
    error: buildTraceError,
    lowestLoadedIndex,
  } = useMemo(() => {
    if (!isQueryEnabled) {
      return { traces: [], error: null, lowestLoadedIndex: null };
    }
    if (!isGroupingQueryEnabled && trace) {
      return { traces: [trace], error: null, lowestLoadedIndex: null };
    }
    let lowestLoadedIndex: number | null = null;
    const { traces, error } = allRelatedRowIds.reduce(
      (
        acc: {
          traces: PreviewTrace[];
          error: Error | null;
        },
        { root_span_id },
        i,
      ) => {
        const loadedData = cachedData[root_span_id];
        if (!loadedData) {
          return acc;
        }
        if (lowestLoadedIndex == null) {
          lowestLoadedIndex = i;
        }
        const { trace, error } = buildVirtualTrace(loadedData, modelCosts);
        acc.error ??= error;
        if (!trace) {
          return acc;
        }

        acc.traces.push(trace);
        return acc;
      },
      { traces: [], error: null },
    );

    return {
      traces,
      error,
      lowestLoadedIndex,
    };
  }, [
    allRelatedRowIds,
    cachedData,
    isQueryEnabled,
    modelCosts,
    trace,
    isGroupingQueryEnabled,
  ]);

  const tracePaginationProps = useMemo(() => {
    return groupingData && hasRelatedRowIds
      ? {
          fetchPreviousPage,
          fetchNextPage,
          hasPreviousPage,
          hasNextPage,
          isFetching: isGroupedRowsQueryFetching || isGroupQueryFetching,
          allRelatedRowIds,
          fetchedRelatedRowIds: relatedIdsToFetch,
          lowestLoadedIndex,
          traces: traces ?? [],
        }
      : undefined;
  }, [
    fetchNextPage,
    fetchPreviousPage,
    hasNextPage,
    hasPreviousPage,
    isGroupedRowsQueryFetching,
    isGroupQueryFetching,
    allRelatedRowIds,
    relatedIdsToFetch,
    hasRelatedRowIds,
    groupingData,
    lowestLoadedIndex,
    traces,
  ]);

  return {
    tracePaginationProps,
    buildTraceError,
    isLoading:
      isRootDataLoading || (isGroupingQueryEnabled && isGroupingQueryPending),
    groupingData,
  };
}

/**
 * Track a window using upper and lower index bounds so that switching traces within the same group
 * maintains the same related traces window
 */
function usePaginatedIds({
  rowIdIndex,
  isThreadView,
  allRowIds,
  primaryId,
}: {
  rowIdIndex: number | null;
  isThreadView?: boolean;
  allRowIds: RowData[];
  primaryId: string | null;
}) {
  const [relatedTraceIndexBounds, setRelatedTraceIndexBounds] = useState<{
    lower: number;
    upper: number;
  } | null>(null);

  const {
    lower: resolvedLowerBound,
    upper: resolvedUpperBound,
    outOfBounds,
  } = rowIdIndex != null
    ? resolveBounds(
        rowIdIndex,
        relatedTraceIndexBounds?.lower,
        relatedTraceIndexBounds?.upper,
      )
    : { lower: null, upper: null, outOfBounds: false };

  const lowerBoundTraceIndex = isThreadView ? 0 : resolvedLowerBound;
  const upperBoundTraceIndex = resolvedUpperBound ?? null;

  const paginatedIds = useMemo(() => {
    if (
      rowIdIndex == null ||
      lowerBoundTraceIndex == null ||
      upperBoundTraceIndex == null
    ) {
      return [];
    }

    const ids: RowData[] = [];
    for (
      let i = Math.max(0, lowerBoundTraceIndex - RELATED_ROW_IDS_PAGE_SIZE);
      i <
      Math.min(
        upperBoundTraceIndex + RELATED_ROW_IDS_PAGE_SIZE + 1,
        allRowIds.length,
      );
      i++
    ) {
      ids.push(allRowIds[i]);
    }

    return ids;
  }, [allRowIds, rowIdIndex, lowerBoundTraceIndex, upperBoundTraceIndex]);

  const hasPreviousPage =
    lowerBoundTraceIndex != null && lowerBoundTraceIndex > 0;
  const hasNextPage =
    upperBoundTraceIndex != null && upperBoundTraceIndex < allRowIds.length;
  const baseRowId = isThreadView ? allRowIds[0]?.root_span_id : primaryId;
  const hasRelatedRowIds = allRowIds.some(
    ({ root_span_id }) => root_span_id !== baseRowId,
  );
  const hasBaseRowId = allRowIds.some(
    ({ root_span_id }) => root_span_id === baseRowId,
  );
  const unrelatedRowId = !hasRelatedRowIds || !hasBaseRowId;
  // Reset bounds when row id is completely different
  // Reset bounds when user selected a row that's outside of the current bounds
  const resetBounds = unrelatedRowId || outOfBounds;
  const fetchPreviousPage = useCallback(() => {
    if (resetBounds) {
      return;
    }
    setRelatedTraceIndexBounds((prev) => {
      if (prev != null) {
        return {
          ...prev,
          lower: prev.lower - RELATED_ROW_IDS_PAGE_SIZE,
        };
      }
      if (rowIdIndex != null) {
        return {
          lower: rowIdIndex - RELATED_ROW_IDS_PAGE_SIZE,
          upper: rowIdIndex,
        };
      }
      return null;
    });
  }, [rowIdIndex, resetBounds]);
  const fetchNextPage = useCallback(() => {
    if (resetBounds) {
      return;
    }
    setRelatedTraceIndexBounds((prev) => {
      if (prev != null) {
        return {
          ...prev,
          upper: prev.upper + RELATED_ROW_IDS_PAGE_SIZE,
        };
      }
      if (rowIdIndex != null) {
        return {
          lower: rowIdIndex,
          upper: rowIdIndex + RELATED_ROW_IDS_PAGE_SIZE,
        };
      }
      return null;
    });
  }, [rowIdIndex, resetBounds]);

  useEffect(() => {
    if (resetBounds) {
      setRelatedTraceIndexBounds(null);
    }
  }, [resetBounds]);

  return {
    relatedIdsToFetch: paginatedIds,
    hasPreviousPage,
    hasNextPage,
    fetchPreviousPage,
    fetchNextPage,
    hasRelatedRowIds,
  };
}

function resolveBounds(
  base: number,
  lowerBound: number | null | undefined,
  upperBound: number | null | undefined,
) {
  const lower = lowerBound ?? base;
  const upper = upperBound ?? base;

  if (base < lower || base > upper) {
    return { lower: base, upper: base, outOfBounds: true };
  }
  return { lower, upper };
}

/**
 * This hook is used to support these goals:
 * 1. Query multiple traces at once in a useQuery call (associated traces).
 * 2. Handle individual caching of trace data so that switching traces is cached.
 * 3. Support realtime updates to traces that are currently being displayed.
 *
 * As the associated trace windows changes in size, we only want to query for traces that haven't
 * been already loaded since realtime updates come through the realtime channel.
 * For span updates that come through the realtime channel, we want to update the individual trace
 * in the query cache but for that to work we need to create a useQuery call for
 * each relevant trace query key.
 *
 * This hook both splits the batched data into individual queries and
 * also tries to keep the cached data up to date.
 */
export function useRealtimeTraces({
  rootSpanIdsToWatch,
  objectType,
  objectId,
  queriedSpanData,
  enabled,
}: {
  rootSpanIdsToWatch: string[];
  objectType: DataObjectType | null;
  objectId: string | null;
  queriedSpanData?: Omit<Partial<BTQLResponse<unknown>>, "data"> & {
    /** root_span_id -> span rows */
    data: Record<string, SpanOverviewRow[]>;
  };
  enabled?: boolean;
}) {
  const btqlOptions = useFetchBtqlOptions();
  const builder = useBtqlQueryBuilder({});

  const queryClient = useQueryClient();
  useEffect(() => {
    if (
      !enabled ||
      !objectType ||
      !queriedSpanData ||
      Object.keys(queriedSpanData?.data ?? {}).length === 0
    ) {
      return;
    }

    const { data: queriedData, ...btqlResponseMetadata } = queriedSpanData;
    Object.entries(queriedData ?? {}).forEach(([rootSpanId, data]) => {
      const queryKey = makeTraceQueryKey({
        objectType,
        objectId,
        traceId: rootSpanId,
      });
      const existing = queryClient.getQueryData<TraceQueryResult>(queryKey);
      const maxExistingXactId = maxXactId(existing?.queryData.data ?? []);
      const maxDataXactId = maxXactId(data);
      if (
        !maxExistingXactId ||
        (maxDataXactId && maxExistingXactId < maxDataXactId)
      ) {
        queryClient.setQueryData(queryKey, {
          rootSpanId,
          queryData: {
            ...btqlResponseMetadata,
            data,
          },
        });
      }
    });
  }, [enabled, objectType, queryClient, queriedSpanData, objectId]);

  // somewhat of a hack to get data out of the cache in a reactive way
  const { data: cachedData } = useQueries({
    queries: rootSpanIdsToWatch.map((rootSpanId) => {
      const queryKeyParams: TraceQueryKeyParams = {
        objectType: objectType!,
        objectId,
        traceId: rootSpanId,
      };
      return {
        queryKey: makeTraceQueryKey(queryKeyParams),
        // for typing
        queryFn: makeTraceQueryFn(
          queryKeyParams,
          builder,
          btqlOptions,
          queryClient,
        ),
        enabled: false,
      };
    }),
    combine: combineResults,
  });

  return useMemo(
    () =>
      cachedData.reduce((acc: Record<string, SpanOverviewRow[]>, data) => {
        const spanRows = data.queryData.data;
        const rows = [];
        let rootSpanId;
        for (const row of spanRows) {
          rows.push(row);
          if ((row.span_parents ?? []).length === 0) {
            rootSpanId = row.root_span_id;
          }
        }
        if (!rootSpanId) {
          return acc;
        }
        acc[rootSpanId] = rows;
        return acc;
      }, {}),
    [cachedData],
  );
}

function maxXactId(rows: SpanOverviewRow[]) {
  return rows.reduce((max: string | null, row) => {
    return row._xact_id && (!max || row._xact_id > max) ? row._xact_id : max;
  }, null);
}
