import {
  type Trace,
  type LoadedTrace,
  type PreviewSpan,
  type PreviewTrace,
} from "#/ui/trace/graph";
import {
  DiffRightField,
  getDiffRight,
  isDiffObject,
} from "#/utils/diffs/diff-objects";
import { type RowId } from "#/ui/query-parameters";
import { type Span } from "@braintrust/local";

export type LoadingState = "loading" | "not_found" | "loaded";

export function calculateLoadingState({
  isTraceQueryLoading,
  hasNoLoadedTraceData,
  loadedSpan,
  isConfigLoading,
  isSearchOpen,
}: {
  isTraceQueryLoading?: boolean;
  hasNoLoadedTraceData?: boolean;
  loadedSpan?: Span | null;
  isConfigLoading?: boolean;
  isSearchOpen?: boolean;
}): LoadingState {
  if ((isTraceQueryLoading && !isSearchOpen) || isConfigLoading) {
    return "loading";
  }

  if (hasNoLoadedTraceData) {
    return "not_found";
  }

  if (loadedSpan) {
    return "loaded";
  }
  return "loading";
}

export function isSpanLoadedForTrace({
  span,
  trace,
  relatedRowIds,
}: {
  span?: Span | PreviewSpan | null;
  trace: Trace | null | undefined;
  relatedRowIds?: { id: string; root_span_id: string }[];
}) {
  const rootSpan = trace?.root;
  if (!span || !rootSpan) {
    return false;
  }

  if (trace.spans[span.span_id]) {
    return true;
  }

  if (relatedRowIds?.some((r) => r.root_span_id === span.root_span_id)) {
    return true;
  }

  return false;
}

/**
 * Try to make sure changes to diff span selection update at the same time:
 *  - traces must both be loaded
 *  - spans must both be loaded
 *  - expected matching span must match the loaded span
 * Also handle non-existing comparison cases
 * @returns
 */
export function resolveLoadedDiffSpans({
  isDiffMode,
  loadedPrimarySpan: primarySpan,
  loadedComparisonSpan: comparisonSpan,
  activeSpanId,
  loadedPrimaryTrace: primaryTrace,
  loadedComparisonTrace: comparisonTrace,
  expectedComparisonExperimentRootSpanId,
  expectedMatchingSpan,
}: {
  isDiffMode: boolean;
  loadedPrimarySpan: Span | null;
  loadedComparisonSpan: Span | null;
  activeSpanId: string | null;
  loadedPrimaryTrace: PreviewTrace | null;
  loadedComparisonTrace: PreviewTrace | null;
  expectedComparisonExperimentRootSpanId: string | null;
  expectedMatchingSpan?: PreviewSpan;
}) {
  if (!isDiffMode) {
    return { primarySpan };
  }

  // For non-overlapping traces, the s param refers to the span_id on the comparison trace
  const urlComparisonSpanRowId = comparisonTrace?.spans[activeSpanId ?? ""]?.id;
  if (urlComparisonSpanRowId) {
    return comparisonSpan?.id === urlComparisonSpanRowId
      ? { comparisonSpan }
      : {};
  }

  if (!primarySpan || !primaryTrace) {
    return {};
  }

  if (!expectedComparisonExperimentRootSpanId) {
    // no matching comparison row at all -- return null for comparison span so a comparison can still be made
    return { primarySpan, comparisonSpan: null };
  }

  if (!comparisonTrace) {
    return {};
  }

  if (!expectedMatchingSpan) {
    // no matching span in comparison trace -- return null for comparison span so a comparison can still be made
    return { primarySpan, comparisonSpan: null };
  }
  if (expectedMatchingSpan.id === comparisonSpan?.id) {
    return { primarySpan, comparisonSpan };
  }

  return {};
}

/**
 * The r param accepts these values:
 * - row id
 * - root span id
 * We want the r param to resolve to the root_span_id in all cases
 * @param existingUrlRowId the existing row id from the url
 * @param loadedTrace
 * @returns a resolved root_span_id, null otherwise
 */
export function resolvePrimaryRootSpanIdForUpdate({
  existingUrlRowId,
  loadedTrace,
}: {
  existingUrlRowId: RowId | null;
  loadedTrace: LoadedTrace | null;
}) {
  if (!loadedTrace) {
    return null;
  }

  if (
    isDiffObject(existingUrlRowId) &&
    getDiffRight(existingUrlRowId) === loadedTrace.root.id
  ) {
    return {
      ...existingUrlRowId,
      [DiffRightField]: loadedTrace.root.root_span_id,
    };
  }

  if (existingUrlRowId === loadedTrace.root.id) {
    return loadedTrace.root.root_span_id;
  }

  if (loadedTrace.root.isSyntheticRoot) {
    return loadedTrace.root.root_span_id;
  }

  return null;
}

/**
 *
 * @param params.activeSpanId
 * @param params.primaryTrace
 * @param params.urlRowId should be root_span_id, but may be a span row id for compatibility
 */
export function resolveSpanIdForLoading({
  activeSpanId,
  primaryTrace,
  urlRowId,
}: {
  activeSpanId: string | null;
  primaryTrace: PreviewTrace | null;
  urlRowId: string | null;
}): {
  spanRowId: string | null;
  isRoot?: boolean;
  includeRootSpanIdLookup?: boolean;
} {
  if (activeSpanId && primaryTrace?.spans[activeSpanId]) {
    const span = primaryTrace.spans[activeSpanId];
    return { spanRowId: span.id, isRoot: span.parent_span_id == null };
  }

  if (primaryTrace?.root.isSyntheticRoot) {
    return { spanRowId: primaryTrace.root.children[0]?.id, isRoot: false };
  }

  if (urlRowId === primaryTrace?.root.root_span_id && primaryTrace?.root.id) {
    return { spanRowId: primaryTrace.root.id, isRoot: true };
  }

  return {
    spanRowId: urlRowId,
    isRoot: true,
    // In the typical case, we want to query the span id only, but
    // in the case where r= exists but s= does not, we will use the r= param
    // which may be the root_span_id. This may cause some brainstore
    // loading quirks if we query by more than just id,
    // so try to use the includeRootSpanIdLookup sparingly
    includeRootSpanIdLookup: true,
  };
}
