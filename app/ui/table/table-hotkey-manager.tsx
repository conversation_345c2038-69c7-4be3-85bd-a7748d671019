import { useContext, useEffect } from "react";
import { useHotkeys, useHotkeysContext } from "react-hotkeys-hook";
import { type RowId, useActiveRowAndSpan } from "#/ui/query-parameters";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { type RowIdInfo } from "#/utils/row-id";

type OpenSidePanelFn = (
  rowId: RowId,
  opts?: {
    humanReviewMode?: boolean;
    openInNewTab?: boolean;
    preserveSpan?: boolean;
  },
) => void;

type Props = {
  isHumanReviewModeEnabled?: boolean;
  openSidePanel: OpenSidePanelFn;
  rowIdInfo: RowIdInfo[];
};

/**
 * Handle "r" hotkey when not in sidebar or human review mode.
 * This is mounted as a component returning null to limit the hotkey-context re-renders
 */
export default function TableHotkeyManager({
  isHumanReviewModeEnabled,
  openSidePanel,
  rowIdInfo,
}: Props) {
  const [{ r: activeRowId }] = useActiveRowAndSpan();
  const firstRowIdInfo = rowIdInfo[0];
  const { disableScope, enableScope, enabledScopes } = useHotkeysContext();

  const { projectId } = useContext(ProjectContext);
  const [isTraceTreeCollapsed] = useEntityStorage({
    entityType: "humanReview",
    entityIdentifier: projectId ?? "",
    key: "isTraceTreeCollapsed",
  });

  useEffect(() => {
    enableScope("table");
    return () => {
      disableScope("table");
    };
  }, [enableScope, disableScope]);

  useHotkeys(
    "r",
    () => {
      const rowId =
        activeRowId ?? firstRowIdInfo?.diffObject ?? firstRowIdInfo.rootSpanId;
      if (!rowId) return;
      openSidePanel(rowId, {
        humanReviewMode: true,
        preserveSpan: !isTraceTreeCollapsed,
      });
    },
    {
      enabled:
        isHumanReviewModeEnabled &&
        !enabledScopes.includes("human-review") &&
        !!firstRowIdInfo,
      description: "Open human review mode",
      preventDefault: true,
    },
  );

  return null;
}
