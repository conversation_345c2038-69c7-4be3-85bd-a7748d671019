import { getDiffRight } from "#/utils/diffs/diff-objects";
import { useCallback } from "react";
import {
  activeRowParserSerialize,
  useActiveRowAndSpan,
  useHumanReviewState,
} from "#/ui/query-parameters";
import { matchesRowIdInfo } from "./use-active-row-effects";
import { type RowId } from "#/ui/query-parameters";
import { type RowIdInfo } from "#/utils/row-id";

type Args = {
  rowIdInfo: RowIdInfo[];
};

export default function useOpenSidePanel({ rowIdInfo }: Args) {
  const [_activeRowAndSpan, setActiveRowAndSpan] = useActiveRowAndSpan();
  const [_humanReviewState, setHumanReviewState] = useHumanReviewState();

  return useCallback(
    (
      rowId: RowId,
      opts?: {
        humanReviewMode?: boolean;
        openInNewTab?: boolean;
        preserveSpan?: boolean;
      },
    ) => {
      const primaryRowId = getDiffRight(rowId);
      const matchingRowIdInfo = rowIdInfo.find((r) => {
        return matchesRowIdInfo(r, primaryRowId);
      });
      if (!matchingRowIdInfo) return;
      const nextRowId =
        matchingRowIdInfo.diffObject ?? matchingRowIdInfo.rootSpanId;
      if (!nextRowId) {
        return;
      }

      if (opts?.openInNewTab) {
        const pathname = window.location.pathname;
        const params = new URLSearchParams(window.location.search);
        params.set(
          "r",
          matchingRowIdInfo.diffObject != null
            ? activeRowParserSerialize(matchingRowIdInfo.diffObject)
            : matchingRowIdInfo.rootSpanId,
        );
        if (opts?.humanReviewMode) {
          params.set("review", "1");
        }
        window.open(`${pathname}?${params.toString()}`, "_blank");
        return;
      }

      setActiveRowAndSpan({
        r: nextRowId,
        ...(opts?.preserveSpan ? {} : { s: null }),
      });

      if (opts?.humanReviewMode) {
        setHumanReviewState("1");
      }
    },
    [rowIdInfo, setActiveRowAndSpan, setHumanReviewState],
  );
}
