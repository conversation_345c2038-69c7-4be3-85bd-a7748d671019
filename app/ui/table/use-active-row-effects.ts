import { getDiffRight, isDiffObject } from "#/utils/diffs/diff-objects";
import { type Dispatch, useEffect } from "react";
import { useActiveRowAndSpan } from "#/ui/query-parameters";
import isEqual from "lodash.isequal";
import { SINGLETON_DATASET_ID } from "#/app/app/[org]/p/[project]/playgrounds/[playground]/playx/stream";
import { type RowId } from "#/ui/query-parameters";
import { type RowIdInfo } from "#/utils/row-id";

type Args = {
  rowIdInfo: RowIdInfo[];
  setRowIdInfo: Dispatch<RowIdInfo[]>;
};

export default function useActiveRowEffects({ rowIdInfo, setRowIdInfo }: Args) {
  const [{ r: activeRowId }, setActiveRowAndSpan] = useActiveRowAndSpan();

  useEffect(() => {
    const primaryRowId = makeRowIdPrimaryOrigin(activeRowId);
    const matchingRowId = rowIdInfo.find((r) => {
      return matchesRowIdInfo(r, primaryRowId);
    });

    // hack for playx mode single row
    if (
      isDiffObject(activeRowId) &&
      activeRowId.originId === SINGLETON_DATASET_ID &&
      rowIdInfo.length > 0
    ) {
      setActiveRowAndSpan(
        { r: rowIdInfo[0]?.diffObject ?? rowIdInfo[0]?.rootSpanId },
        { history: "replace" },
      );
      return;
    }

    if (!matchingRowId) return;
    const nextRowId = matchingRowId.diffObject ?? matchingRowId.rootSpanId;
    if (
      isDiffObject(activeRowId) &&
      isDiffObject(nextRowId) &&
      !isEqual(activeRowId, nextRowId)
    ) {
      // in multi-experiment mode, the diff object may increase/decrease in size when experiments are selected
      // in playX mode, the diff values may change as data comes in
      // in playX mode, the diffModeEnabled key may change
      setActiveRowAndSpan({ r: nextRowId }, { history: "replace" });
      return;
    }

    if (
      (isDiffObject(nextRowId) && !isDiffObject(activeRowId)) ||
      (!isDiffObject(nextRowId) && isDiffObject(activeRowId))
    ) {
      setActiveRowAndSpan({ r: nextRowId }, { history: "replace" });
    }
  }, [activeRowId, setActiveRowAndSpan, rowIdInfo]);

  useEffect(() => {
    if (!activeRowId) return;

    // If the activeRowId is set, update trace state on mount
    setRowIdInfo(rowIdInfo);
    // we only want this hook to run on mount
    // eslint-disable-next-line react-compiler/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
}

/**
 * @param rowIdInfo
 * @param idToMatch - root_span_id or span row id
 */
export function matchesRowIdInfo(
  rowIdInfo: RowIdInfo,
  idToMatch: string | null,
) {
  if (idToMatch === null) {
    return false;
  }

  if (rowIdInfo.rootSpanId === idToMatch || rowIdInfo.id === idToMatch) {
    return true;
  }

  const primaryRowId = getDiffRight(rowIdInfo.diffObject);
  if (primaryRowId === idToMatch) {
    return true;
  }

  if (isDiffObject(rowIdInfo.diffObject) && rowIdInfo.diffObject.originId) {
    return rowIdInfo.diffObject.originId === idToMatch;
  }

  return false;
}

// with the playground, the first prompt may not have been run but other prompts have
// in these cases, the primaryRowId will be null so we have to rely on the origin
export function makeRowIdPrimaryOrigin(row: RowId | null) {
  if (row === null) {
    return null;
  }

  const primaryRowId = getDiffRight(row);
  if (primaryRowId) {
    return primaryRowId;
  }

  if (isDiffObject(row) && row.originId) {
    return row.originId;
  }

  return null;
}
