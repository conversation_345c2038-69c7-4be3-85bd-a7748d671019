import { dbQuery, useDBQuery, useDuckDB } from "#/utils/duckdb";
import {
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  ArrowTable,
  type ArrowTableUIProps,
  type RowData,
} from "#/ui/arrow-table";
import { type Table, type TypeMap } from "apache-arrow";
import { useActiveRowAndSpan } from "#/ui/query-parameters";
import { type AsyncDuckDBConnection } from "@duckdb/duckdb-wasm";
import { type PathTree } from "#/utils/display-paths";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { extractRowIdInfo } from "#/utils/row-id";

interface VizQueryDataProps {
  onNumRowsChange?: (numRows: number) => void;
  disableLimit?: boolean;
  query: string | null;
  signals: number[];
  vizQueryRef?: React.RefObject<{
    data: Table<TypeMap> | null;
  } | null>;
  enableLoadingForDBQuery?: boolean;
  onDataLoaded?: (
    conn: AsyncDuckDBConnection,
    abortSignal: AbortSignal,
  ) => Promise<void>;
  loadingColumns?: string[];
}

export type VizQueryProps<TsData, TsValue> = VizQueryDataProps &
  ArrowTableUIProps<TsData, TsValue>;

const PAGE_SIZE = 20;

export function VizQuery<TsData extends RowData, TsValue>({
  query: baseQuery,
  signals,
  formatters,
  tableType = "detailed",
  sizeConstraintsMap,
  tableRef,
  vizQueryRef,
  error: errorProp,
  onNumRowsChange,
  disableLimit,
  rowGroupingData: rowGroupingDataProp,
  displayPaths: displayPathsProp,
  enableLoadingForDBQuery,
  onDataLoaded,
  loadingColumns,
  ...tableProps
}: VizQueryProps<TsData, TsValue>) {
  const [{ r: activeRowId }] = useActiveRowAndSpan();
  const activeRowOnPageLoad = useRef(activeRowId);

  const [limit, setLimit] = useState(PAGE_SIZE);

  // eslint-disable-next-line react-compiler/react-compiler
  const isLimitDisabled = disableLimit || !!activeRowOnPageLoad.current;

  const [error, setError] = useState<string | null>(null);
  const [{ data, rowGroupingData, displayPaths }, setQueryData] = useState<{
    data: Table | null;
    rowGroupingData?: typeof rowGroupingDataProp;
    displayPaths?: PathTree;
  }>({
    data: null,
    rowGroupingData: undefined,
    displayPaths: undefined,
  });

  const queryFn = useMemo(() => {
    if (!baseQuery) {
      return null;
    }
    return async (conn: AsyncDuckDBConnection, abortSignal: AbortSignal) => {
      const query = `${baseQuery}
        ${isLimitDisabled ? "" : `LIMIT ${limit}`}`;
      const result = await dbQuery(conn, abortSignal, query);
      onDataLoaded?.(conn, abortSignal);

      setQueryData({
        data: result,
        rowGroupingData: rowGroupingDataProp,
        displayPaths: displayPathsProp,
      });
      return null;
    };
  }, [
    baseQuery,
    isLimitDisabled,
    limit,
    rowGroupingDataProp,
    displayPathsProp,
    onDataLoaded,
  ]);

  const { loading: loadingDBQuery } = useDBQuery(queryFn, signals, {
    setError,
  });

  const duck = useDuckDB();
  const { data: queriedRowIdInfo } = useQuery({
    queryKey: ["rowIdsQuery", baseQuery, signals],
    queryFn: async ({ signal }) => {
      const conn = await duck!.connect();
      return await rowIdsQuery(conn, signal, baseQuery ?? "");
    },
    enabled: !!duck && !!baseQuery && signals.every((s) => s > 0),
  });
  const signalSum = signals.reduce((acc, v) => acc + v, 0);
  const queryClient = useQueryClient();
  useEffect(() => {
    queryClient.invalidateQueries({
      queryKey: ["rowIdsQuery"],
    });
  }, [queryClient, signalSum]);

  useEffect(() => {
    const numRows = data?.numRows;
    if (!onNumRowsChange || numRows === undefined) return;
    onNumRowsChange(numRows);
  }, [data?.numRows, onNumRowsChange]);

  useImperativeHandle(
    vizQueryRef,
    () => ({
      data,
    }),
    [data],
  );

  const errorVal = errorProp ?? error;

  const infiniteScroll = useMemo(
    () =>
      !isLimitDisabled && data?.numRows && data.numRows >= limit
        ? async () => setLimit(limit + PAGE_SIZE)
        : undefined,
    [data?.numRows, isLimitDisabled, limit],
  );

  return (
    <ArrowTable
      table={data}
      isLoading={enableLoadingForDBQuery && loadingDBQuery}
      formatters={formatters}
      tableType={tableType}
      sizeConstraintsMap={sizeConstraintsMap}
      tableRef={tableRef}
      rowGroupingData={rowGroupingData}
      displayPaths={displayPaths}
      queriedRowIdInfo={queriedRowIdInfo}
      infiniteScroll={infiniteScroll}
      // eslint-disable-next-line react-compiler/react-compiler
      activeRowOnPageLoad={activeRowOnPageLoad.current}
      error={errorVal}
      loadingColumns={loadingColumns}
      {...tableProps}
    />
  );
}

async function rowIdsQuery(
  conn: AsyncDuckDBConnection,
  abortSignal: AbortSignal,
  query: string,
) {
  const result = await dbQuery(conn, abortSignal, query);

  const rowIds = [];
  for (const r of result ?? []) {
    const rowId = extractRowIdInfo(r);
    if (rowId) {
      rowIds.push(rowId);
    }
  }
  return rowIds;
}
