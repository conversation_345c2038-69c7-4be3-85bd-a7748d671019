import {
  Activity,
  Sparkle,
  Beaker,
  MessageCircle,
  Database,
  Search,
  TrendingUpIcon,
} from "lucide-react";
import Link from "next/link";
import { buttonVariants } from "#/ui/button";
import { TableEmptyState } from "./table/TableEmptyState";
import { getExperimentsLink } from "#/app/app/[org]/p/[project]/experiments/[experiment]/getExperimentLink";
import { getDatasetsLink } from "#/app/app/[org]/p/[project]/datasets/[dataset]/getDatasetLink";
import { getProjectLogsLink } from "#/app/app/[org]/p/[project]/logs/getProjectLogsLink";
import { getProjectLink } from "#/app/app/[org]/p/[project]/getProjectLink";
import { cn } from "#/utils/classnames";
import { useAppAnalytics } from "#/ui/analytics/segment-analytics";
import { useOrg } from "#/utils/user";
import { useState, useRef, useCallback, useEffect } from "react";

interface OverviewCardProps {
  icon: React.ReactNode;
  title: string;
  subtitle: string;
  description: React.ReactNode;
  children?: React.ReactNode;
  className?: string;
  glowColor?: string;
}

const OverviewCard = ({
  icon,
  title,
  subtitle,
  description,
  children,
  className,
  glowColor = "rgba(59, 130, 246, 0.12)",
}: OverviewCardProps) => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);
  const animationFrameRef = useRef<number | undefined>(undefined);

  const handleMouseMove = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }

    animationFrameRef.current = requestAnimationFrame(() => {
      if (cardRef.current) {
        const rect = cardRef.current.getBoundingClientRect();
        setMousePosition({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top,
        });
      }
    });
  }, []);

  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);

  return (
    <div
      ref={cardRef}
      className={cn(
        "relative z-10 min-w-0 flex-1 overflow-hidden rounded-xl border border-primary-100 bg-background p-6 shadow-xs",
        className,
      )}
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      <div
        className={cn(
          "pointer-events-none absolute transition-all duration-500 ease-out",
          isHovering ? "opacity-80 dark:opacity-100" : "opacity-60",
        )}
        style={{
          left: isHovering ? mousePosition.x - 400 : "50%",
          top: isHovering ? mousePosition.y - 400 : "100%",
          transform: isHovering ? "none" : "translateX(-50%) translateY(-15%)",
          width: 800,
          height: 800,
          background: `radial-gradient(circle, ${glowColor} 0%, ${glowColor.replace("0.12", "0.08")} 30%, ${glowColor.replace("0.12", "0.04")} 60%, transparent 80%)`,
          borderRadius: "50%",
          willChange: "transform",
          filter: "blur(20px)",
        }}
      />

      <div className="relative z-10 mb-4 flex items-start gap-3">
        {icon}
        <div>
          <h4 className="text-base font-semibold text-primary-900">{title}</h4>
          <p className="text-xs text-primary-500">{subtitle}</p>
        </div>
      </div>
      <div className="relative z-10 mb-4 text-sm text-primary-600">
        {description}
      </div>
      <div className="relative z-10">{children}</div>
    </div>
  );
};

export const OverviewEmptyState = ({
  orgName,
  projectName,
  onCreatePlayground,
}: {
  orgName: string;
  projectName: string;
  onCreatePlayground: () => void;
}) => {
  const { track } = useAppAnalytics();
  const org = useOrg();

  const [aiCardMousePosition, setAiCardMousePosition] = useState({
    x: 0,
    y: 0,
  });
  const [isAiCardHovering, setIsAiCardHovering] = useState(false);
  const aiCardRef = useRef<HTMLDivElement>(null);
  const aiCardAnimationFrameRef = useRef<number | undefined>(undefined);

  const handleAiCardMouseMove = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      if (aiCardAnimationFrameRef.current) {
        cancelAnimationFrame(aiCardAnimationFrameRef.current);
      }

      aiCardAnimationFrameRef.current = requestAnimationFrame(() => {
        if (aiCardRef.current) {
          const rect = aiCardRef.current.getBoundingClientRect();
          setAiCardMousePosition({
            x: e.clientX - rect.left,
            y: e.clientY - rect.top,
          });
        }
      });
    },
    [],
  );

  useEffect(() => {
    return () => {
      if (aiCardAnimationFrameRef.current) {
        cancelAnimationFrame(aiCardAnimationFrameRef.current);
      }
    };
  }, []);

  return (
    <TableEmptyState
      labelClassName="max-w-192 text-left w-full"
      label={
        <div>
          <h3 className="mb-1 text-lg font-semibold text-primary-900">
            Configure this project
          </h3>
          <p className="text-sm text-primary-600">
            Get started with observability and evaluation for this AI project
          </p>
        </div>
      }
    >
      <div className="relative mt-2 w-full">
        <div className="relative mx-auto flex max-w-192 flex-col gap-6 lg:flex-row lg:gap-12">
          <OverviewCard
            icon={
              <div className="flex h-10 w-10 shrink-0 items-center justify-center rounded-lg bg-purple-200/50 dark:bg-purple-800/50">
                <Search className="h-5 w-5 text-purple-700 dark:text-purple-300" />
              </div>
            }
            className="group transition-colors duration-200 hover:border-purple-200 dark:hover:border-purple-200/50"
            title="Observability"
            subtitle="Trace your AI app interactions"
            glowColor="rgb(196 181 253 / 0.2)"
            description={
              <>
                Trace user interactions for monitoring, real-time scoring, and
                review. Annotate logs data and use it as the source for
                evaluations in Braintrust.
                <br />
                <br />
                Set up tracing for your AI app within minutes.
              </>
            }
          >
            <Link
              href={getProjectLogsLink({ orgName, projectName })}
              className={cn(
                buttonVariants({ size: "sm" }),
                "w-full justify-start bg-background",
              )}
              onClick={() => {
                track("onboardingOverviewClick", {
                  orgName,
                  orgId: org?.id ?? "",
                  projectName,
                  actionType: "setupObservability",
                  entryPoint: "onboardingOverview",
                  destinationUrl: getProjectLogsLink({
                    orgName,
                    projectName,
                  }),
                  source: "web",
                });
              }}
            >
              <Activity className="h-3 w-3" />
              Setup tracing
            </Link>
          </OverviewCard>
          <OverviewCard
            icon={
              <div className="flex h-10 w-10 shrink-0 items-center justify-center rounded-lg bg-blue-200/50 dark:bg-blue-800/50">
                <TrendingUpIcon className="h-5 w-5 text-blue-700 dark:text-blue-300" />
              </div>
            }
            className="transition-colors duration-200 hover:border-blue-200 dark:hover:border-blue-200/50"
            title="Evaluation"
            subtitle="Measure and iterate on performance"
            glowColor="rgb(var(--blue-200) / 0.25)"
            description={
              <>
                Add your prompt or golden dataset, or use the SDK to get started
                quickly.
              </>
            }
          >
            <div className="space-y-2">
              <Link
                href={`${getProjectLink({ orgName, projectName })}/prompts`}
                className={cn(
                  buttonVariants({ size: "sm" }),
                  "w-full justify-start bg-background",
                )}
                onClick={() => {
                  onCreatePlayground();
                  track("onboardingOverviewClick", {
                    orgName,
                    orgId: org?.id ?? "",
                    projectName,
                    actionType: "addPrompt",
                    entryPoint: "onboardingOverview",
                    destinationUrl: `${getProjectLink({ orgName, projectName })}/prompts`,
                    source: "web",
                  });
                }}
              >
                <MessageCircle className="h-3 w-3" />
                Add your prompt
              </Link>
              <Link
                href={getDatasetsLink({ orgName, projectName })}
                className={cn(
                  buttonVariants({ size: "sm", variant: "border" }),
                  "w-full justify-start bg-background",
                )}
                onClick={() => {
                  track("onboardingOverviewClick", {
                    orgName,
                    orgId: org?.id ?? "",
                    projectName,
                    actionType: "uploadDataset",
                    entryPoint: "onboardingOverview",
                    destinationUrl: getDatasetsLink({ orgName, projectName }),
                    source: "web",
                  });
                }}
              >
                <Database className="h-3 w-3" />
                Upload dataset (CSV/JSON)
              </Link>
              <Link
                href={getExperimentsLink({ orgName, projectName })}
                className={cn(
                  buttonVariants({ size: "sm", variant: "border" }),
                  "w-full justify-start bg-background",
                )}
                onClick={() => {
                  track("onboardingOverviewClick", {
                    orgName,
                    orgId: org?.id ?? "",
                    projectName,
                    actionType: "evalViaSdk",
                    entryPoint: "onboardingOverview",
                    destinationUrl: getExperimentsLink({
                      orgName,
                      projectName,
                    }),
                    source: "web",
                  });
                }}
              >
                <Beaker className="h-3 w-3" />
                Create experiment
              </Link>
            </div>
          </OverviewCard>
        </div>

        <div className="relative z-10 mx-auto mt-6 hidden max-w-192 gap-6 lg:mt-8 lg:flex">
          <div className="flex w-full items-center justify-center">
            <div
              ref={aiCardRef}
              className="relative flex w-full flex-col items-start justify-center overflow-hidden rounded-xl border border-primary-200 bg-primary-50 p-3 text-left transition-colors duration-200 hover:border-gray-300 lg:w-56 dark:hover:border-gray-700"
              onMouseMove={handleAiCardMouseMove}
              onMouseEnter={() => setIsAiCardHovering(true)}
              onMouseLeave={() => setIsAiCardHovering(false)}
            >
              <div
                className={cn(
                  "pointer-events-none absolute transition-opacity duration-200 ease-out",
                  isAiCardHovering
                    ? "opacity-20 dark:opacity-100"
                    : "opacity-15 dark:opacity-100",
                )}
                style={{
                  left: isAiCardHovering ? aiCardMousePosition.x - 400 : "50%",
                  top: isAiCardHovering
                    ? aiCardMousePosition.y - 400
                    : "-500px",
                  transform: isAiCardHovering ? "none" : "translateX(-50%)",
                  width: 800,
                  height: 800,
                  background: `radial-gradient(circle, rgba(107, 114, 128, 0.25) 0%, rgba(107, 114, 128, 0.15) 30%, rgba(107, 114, 128, 0.08) 60%, transparent 80%)`,
                  borderRadius: "50%",
                  filter: "blur(20px)",
                }}
              />

              {/* Interactive gray glow */}
              <div
                className={cn(
                  "pointer-events-none absolute transition-all duration-100 ease-out",
                  isAiCardHovering
                    ? "opacity-50 dark:opacity-100"
                    : "opacity-0",
                )}
                style={{
                  left: isAiCardHovering ? aiCardMousePosition.x - 150 : "50%",
                  top: isAiCardHovering ? aiCardMousePosition.y - 150 : "100%",
                  transform: isAiCardHovering
                    ? "none"
                    : "translateX(-50%) translateY(-10%)",
                  width: 500,
                  height: 500,
                  background: `radial-gradient(circle, rgba(107, 114, 128, 0.18) 0%, rgba(107, 114, 128, 0.09) 30%, rgba(107, 114, 128, 0.05) 60%, transparent 80%)`,
                  borderRadius: "50%",
                  willChange: "transform",
                  filter: "blur(20px)",
                }}
              />

              {/* TODO: refactor into overviewcard */}
              <div className="relative z-10 flex items-center justify-center gap-2">
                <Sparkle className="h-5 w-5 text-primary-600" />
                <h4 className="text-base font-semibold text-primary-900">
                  AI in your app
                </h4>
              </div>
            </div>
          </div>
        </div>

        <div className="absolute top-1/2 left-1/2 hidden h-72 w-128 -translate-x-1/2 -translate-y-[49%] items-center justify-center rounded-3xl lg:flex">
          <div className="absolute inset-0 translate-y-2 rounded-3xl border-1 border-gray-400/80 dark:border-gray-600" />
          {/* bottom left arrowhead */}
          <div className="absolute bottom-0 left-0 z-10 -translate-x-[0.02rem] -translate-y-[1.84rem]">
            <div className="relative h-0 w-0">
              <div className="absolute bottom-0 left-0 h-2.5 w-0.25 origin-top translate-x-[0.03rem] rotate-45 bg-gray-400/80 dark:bg-gray-600"></div>
              <div className="absolute bottom-0 left-0 h-2.5 w-0.25 origin-top -rotate-45 bg-gray-400/80 dark:bg-gray-600"></div>
            </div>
          </div>
          {/* top arrowhead */}
          <div className="absolute top-0 right-1/2 z-10 translate-x-[0.45rem] translate-y-[0.53rem]">
            <div className="relative h-0 w-0">
              <div className="absolute top-0 left-0 h-0.25 w-2.5 origin-right rotate-45 bg-gray-400/80 dark:bg-gray-600"></div>
              <div className="absolute top-0 left-0 h-0.25 w-2.5 origin-right -translate-y-[0.05rem] -rotate-45 bg-gray-400/80 dark:bg-gray-600"></div>
            </div>
          </div>
          {/* bottom right arrowhead */}
          <div className="absolute right-0 bottom-0 z-10 -translate-x-31.5 translate-y-[0.41rem]">
            <div className="relative h-0 w-0">
              <div className="absolute top-0 right-0 h-0.25 w-2.5 origin-left rotate-45 bg-gray-400/80 dark:bg-gray-600"></div>
              <div className="absolute top-0 right-0 h-0.25 w-2.5 origin-left translate-y-[0.05rem] -rotate-45 bg-gray-400/80 dark:bg-gray-600"></div>
            </div>
          </div>
          {/* space between arrow and cards */}
          {/* Bottom left arrow space */}
          <div className="absolute bottom-0 left-0 h-3 w-3 -translate-x-[0.32rem] -translate-y-[2.43rem] bg-primary-50"></div>
          <div className="absolute bottom-0 left-0 h-2.5 w-2.5 translate-x-33.5 translate-y-4 bg-primary-50"></div>
          {/* top arrow space */}
          <div className="absolute top-0 right-1/2 h-3 w-3 translate-x-7 translate-y-[0.2rem] bg-primary-50"></div>
          <div className="absolute top-0 right-1/2 h-2.5 w-2.5 -translate-x-4.5 translate-y-[0.2rem] bg-primary-50"></div>
          {/* bottom right arrow space */}
          <div className="absolute right-0 bottom-0 h-3 w-3 -translate-x-34 translate-y-3.5 bg-primary-50"></div>
          <div className="absolute bottom-0 left-0 h-2.5 w-2.5 translate-x-126 -translate-y-[2.55rem] bg-primary-50"></div>
        </div>
      </div>
    </TableEmptyState>
  );
};
