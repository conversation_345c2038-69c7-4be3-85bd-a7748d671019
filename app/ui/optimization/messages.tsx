import {
  type ParsedMessage,
  type ToolInteraction,
  type SystemMessage,
  type BTQLContextObject,
} from "./use-global-chat-context";
import { ContextObjectBadge } from "#/ui/optimization/context-object-badge";
import { type ContextObject } from "#/ui/optimization/use-global-chat-context";
import { type UserMessage as UserMessageType } from "./use-global-chat-context";
import { MarkdownViewer } from "#/ui/markdown";
import { SyntaxHighlight } from "#/ui/syntax-highlighter";
import { ErrorBanner } from "#/ui/error-banner";
import { InfoBanner } from "#/ui/info-banner";
import { Feedback } from "#/ui/optimization/feedback";
import {
  EditTaskToolDisplay,
  EditDataToolDisplay,
  EditScorersToolDisplay,
  RunTaskToolDisplay,
  DetailedResultsToolDisplay,
  SummaryToolDisplay,
  AvailableScorersToolDisplay,
  UserConsentMessage,
  CollapsibleAction,
  Title,
  // BTQL-TODO: Re-enable this tool once we have a good tool for BTQLs
  // EditBTQLToolDisplay,
  CreateLLMScorerToolDisplay,
  CreateCodeScorerToolDisplay,
  InferSchemaToolDisplay,
  BTQLQueryToolDisplay,
  SearchDocsToolDisplay,
  RunBtqlToolDisplay,
  GetDataSourceToolDisplay,
} from "#/ui/optimization/tool-message-ui";
import { type ChatContext, type PageKey } from "@braintrust/local/optimization";
import { ObjectRowLink, evalRowUrlTransform } from "./object-row-link";
import { cn } from "#/utils/classnames";
import { Button } from "#/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "#/ui/popover";
import { type SearchableItemInfo } from "#/utils/codemirror/btql-lang";
import { ContextDataSourceSelector } from "#/ui/optimization/context-data-source-selector";
import { memo, useEffect, useMemo, useRef, useState } from "react";
import { CopyToClipboardButton } from "#/ui/copy-to-clipboard-button";
import { ChevronsUpDown, Plus, RotateCcw } from "lucide-react";
import { type ConfirmationRequest } from "#/utils/optimization/provider";

const LLM_MESSAGE_MD_COMPONENTS = {
  a: ObjectRowLink,
};

const getMessageContent = (
  messages: ParsedMessage[],
  currentIndex: number,
): string => {
  const precedingMessages = messages.slice(0, currentIndex);

  const lastTurnEndIndex = precedingMessages.findLastIndex(
    (msg) =>
      msg.type !== "user_message" &&
      "isLastMessageOfTurn" in msg &&
      msg.isLastMessageOfTurn,
  );

  const startIndex = lastTurnEndIndex === -1 ? 0 : lastTurnEndIndex + 1;

  const endIndex = currentIndex + 1;

  const turnMessages = messages
    .slice(startIndex, endIndex)
    .filter((msg) => msg.type !== "user_message")
    .map((msg) => {
      switch (msg.type) {
        case "llm_message":
          return msg.llmContent;
        case "system_message":
          return msg.message;
        case "tool_interaction":
          return `Tool called: ${msg.functionName}`;
        default:
          return "";
      }
    });

  return turnMessages.join("\n\n");
};

export const Messages = memo(
  ({
    parsedMessages,
    setAllowRunningWithoutConsent,
    allowRunningWithoutConsent,
    pageKey,
    chat,
    size = "widget",
    onBTQLFilter,
    onRunInSandbox,
    availableDataSources,
    handleCreateNewSession,
    handleSendMessage,
    getConfirmationByToolCallId,
  }: {
    parsedMessages: ParsedMessage[];
    setAllowRunningWithoutConsent: (allow: boolean) => void;
    allowRunningWithoutConsent: boolean;
    pageKey: PageKey;
    chat: ChatContext | null;
    size?: "widget" | "full";
    onBTQLFilter?: (filterText: string) => void;
    onRunInSandbox?: (args: {
      query: string;
      title?: string;
      openNewTab?: boolean;
    }) => void;
    availableDataSources?: {
      projects: SearchableItemInfo[];
      datasets: SearchableItemInfo[];
      experiments: SearchableItemInfo[];
      promptSessions: SearchableItemInfo[];
      orgs: SearchableItemInfo[];
    };
    handleCreateNewSession: () => void;
    handleSendMessage: (
      userMessage: UserMessageType,
      options?: {
        clearContextObjects?: boolean;
        clearUserMessage?: boolean;
      },
    ) => Promise<void>;
    getConfirmationByToolCallId: (
      toolCallId: string,
    ) => ConfirmationRequest | null;
  }) => {
    //We are grouping messages by turn here. This allows us to do neat interactions to highlight the user message for each turn users are viewing.
    const messageGroups = useMemo(() => {
      const groups: ParsedMessage[][] = [];
      let currentGroup: ParsedMessage[] = [];

      parsedMessages.forEach((msg) => {
        if (msg.type === "user_message" && currentGroup.length > 0) {
          groups.push(currentGroup);
          currentGroup = [msg];
        } else {
          currentGroup.push(msg);
        }
      });

      if (currentGroup.length > 0) {
        groups.push(currentGroup);
      }

      return groups;
    }, [parsedMessages]);

    return (
      <>
        {messageGroups.map((group, groupIndex) => {
          const userMessage =
            group[0]?.type === "user_message" ? group[0] : null;
          const responses = userMessage ? group.slice(1) : group;
          const isLastGroup = groupIndex === messageGroups.length - 1;
          const hasNonSystemResponses = responses.some(
            (r) => r.type !== "system_message",
          );

          return (
            <div key={`group-${groupIndex}`}>
              {userMessage && (
                <div
                  className="sticky top-0 bg-primary-50 px-4 pt-2"
                  style={{ zIndex: 30 + groupIndex }}
                >
                  <UserMessage msg={userMessage} size={size} />
                </div>
              )}

              <div className="space-y-2 pt-3">
                {responses.map((msg) => {
                  const originalIndex = parsedMessages.indexOf(msg);
                  return (
                    <div key={msg.id} className="px-3">
                      {msg.type === "tool_interaction" && msg.toolCallId && (
                        <RenderToolMessage
                          msg={msg}
                          setAllowRunningWithoutConsent={
                            setAllowRunningWithoutConsent
                          }
                          allowRunningWithoutConsent={
                            allowRunningWithoutConsent
                          }
                          availableDataSources={availableDataSources}
                          pageKey={pageKey}
                          onBTQLFilter={onBTQLFilter}
                          onRunInSandbox={onRunInSandbox}
                          getConfirmationByToolCallId={
                            getConfirmationByToolCallId
                          }
                        />
                      )}
                      {msg.type === "llm_message" && (
                        <MarkdownViewer
                          className={cn(
                            "px-2 py-0 text-xs prose-headings:my-3 prose-h1:text-lg prose-blockquote:my-3 prose-pre:-mx-3 prose-pre:mt-3 prose-ol:mt-3 prose-ul:mt-3 prose-table:my-3 [&_span.block]:mt-3",
                            size === "full" && "text-sm",
                          )}
                          value={msg.llmContent}
                          components={LLM_MESSAGE_MD_COMPONENTS}
                          urlTransform={evalRowUrlTransform}
                        />
                      )}
                      {msg.type === "system_message" && isLastGroup && (
                        <SystemMessage
                          msg={msg}
                          handleCreateNewSession={handleCreateNewSession}
                          handleSendMessage={handleSendMessage}
                          userMessage={userMessage}
                        />
                      )}
                      {(msg.type === "tool_interaction" ||
                        msg.type === "llm_message" ||
                        msg.type === "system_message") &&
                        hasNonSystemResponses &&
                        msg.isLastMessageOfTurn && (
                          <div className="mt-2 flex items-start gap-0.5">
                            <CopyToClipboardButton
                              size="xs"
                              variant="ghost"
                              textToCopy={getMessageContent(
                                parsedMessages,
                                originalIndex,
                              )}
                              className="text-primary-500 hover:bg-primary-200"
                              copyMessage="Copy turn"
                            />
                            {chat && (
                              <Feedback
                                sendFeedback={chat.recordFeedback.bind(chat)}
                                isActive={
                                  originalIndex === parsedMessages.length - 1
                                }
                              />
                            )}
                          </div>
                        )}
                    </div>
                  );
                })}
              </div>
            </div>
          );
        })}
      </>
    );
  },
);
Messages.displayName = "Messages";

const RenderToolMessage = ({
  msg,
  setAllowRunningWithoutConsent,
  allowRunningWithoutConsent,
  availableDataSources,
  pageKey,
  onBTQLFilter,
  onRunInSandbox,
  getConfirmationByToolCallId,
}: {
  msg: ToolInteraction;
  setAllowRunningWithoutConsent: (allow: boolean) => void;
  allowRunningWithoutConsent: boolean;
  availableDataSources?: {
    projects: SearchableItemInfo[];
    datasets: SearchableItemInfo[];
    experiments: SearchableItemInfo[];
    promptSessions: SearchableItemInfo[];
    orgs: SearchableItemInfo[];
  };
  pageKey: PageKey;
  onBTQLFilter?: (filterText: string) => void;
  onRunInSandbox?: (args: {
    query: string;
    title?: string;
    openNewTab?: boolean;
  }) => void;
  getConfirmationByToolCallId: (
    toolCallId: string,
  ) => ConfirmationRequest | null;
}) => {
  const confirmation = getConfirmationByToolCallId(msg.toolCallId);
  const userConsentConfirmationData =
    confirmation?.type === "continue_execution" ? confirmation : null;

  switch (msg.functionName) {
    case "edit_task":
      return (
        <EditTaskToolDisplay
          msg={msg}
          editTaskConfirmationData={
            confirmation?.type === "edit_task" ? confirmation : null
          }
          selectedContinueWithoutConsent={allowRunningWithoutConsent}
          setSelectedContinueWithoutConsent={setAllowRunningWithoutConsent}
        />
      );
    case "edit_data":
      return (
        <EditDataToolDisplay
          msg={msg}
          editDatasetConfirmationData={
            confirmation?.type === "edit_dataset" ? confirmation : null
          }
          selectedContinueWithoutConsent={allowRunningWithoutConsent}
          setSelectedContinueWithoutConsent={setAllowRunningWithoutConsent}
        />
      );
    case "edit_scorers":
      return (
        <EditScorersToolDisplay
          msg={msg}
          editScorersConfirmationData={
            confirmation?.type === "edit_scorers" ? confirmation : null
          }
          selectedContinueWithoutConsent={allowRunningWithoutConsent}
          setSelectedContinueWithoutConsent={setAllowRunningWithoutConsent}
        />
      );
    case "create_code_scorer":
      return (
        <CreateCodeScorerToolDisplay
          msg={msg}
          createCodeScorerConfirmationData={
            confirmation?.type === "create_code_scorer" ? confirmation : null
          }
          selectedContinueWithoutConsent={allowRunningWithoutConsent}
          setSelectedContinueWithoutConsent={setAllowRunningWithoutConsent}
        />
      );
    case "infer_schema":
      return <InferSchemaToolDisplay msg={msg} />;
    case "btql_query":
      return <BTQLQueryToolDisplay msg={msg} onBTQLFilter={onBTQLFilter} />;
    case "create_llm_scorer":
      return (
        <CreateLLMScorerToolDisplay
          msg={msg}
          createLLMScorerConfirmationData={
            confirmation?.type === "create_llm_scorer" ? confirmation : null
          }
          selectedContinueWithoutConsent={allowRunningWithoutConsent}
          setSelectedContinueWithoutConsent={setAllowRunningWithoutConsent}
        />
      );
    case "search_docs":
      return <SearchDocsToolDisplay msg={msg} />;
    case "run_task":
      return (
        <RunTaskToolDisplay
          msg={msg}
          userConsentConfirmationData={userConsentConfirmationData}
          setAllowRunningWithoutConsent={setAllowRunningWithoutConsent}
          allowRunningWithoutConsent={allowRunningWithoutConsent}
        />
      );
    case "get_results":
      return <DetailedResultsToolDisplay msg={msg} pageKey={pageKey} />;
    case "get_summary":
      return <SummaryToolDisplay msg={msg} />;
    case "continue_execution":
      if (msg.status === "pending_output" && userConsentConfirmationData) {
        return (
          <UserConsentMessage
            userConsentConfirmationData={userConsentConfirmationData}
            setAllowRunningWithoutConsent={setAllowRunningWithoutConsent}
            allowRunningWithoutConsent={allowRunningWithoutConsent}
            label="Do you want to continue execution?"
          />
        );
      }
      return null;
    case "get_available_scorers":
      return <AvailableScorersToolDisplay msg={msg} />;
    case "get_data_source":
      return (
        <GetDataSourceToolDisplay
          msg={msg}
          dataSourceSelectionConfirmationData={
            confirmation?.type === "data_source_selection" ? confirmation : null
          }
          availableDataSources={availableDataSources}
        />
      );
    case "run_btql":
      return (
        <RunBtqlToolDisplay
          msg={msg}
          onRunInSandbox={onRunInSandbox}
          runBtqlConfirmationData={
            confirmation?.type === "run_btql" ? confirmation : null
          }
          setAllowRunningWithoutConsent={setAllowRunningWithoutConsent}
          allowRunningWithoutConsent={allowRunningWithoutConsent}
        />
      );

    // BTQL-TODO: Re-enable this tool once we have a good tool for BTQLs
    // case "edit_btql":
    //   return <EditBTQLToolDisplay msg={msg} />;
    default:
      break;
  }

  // Default case for all other function names and statuses
  return (
    <CollapsibleAction title={<Title msg={msg} />} defaultCollapsed={true}>
      <SyntaxHighlight
        language="json"
        className="rounded-b-md border-x border-b bg-primary-100 p-2 break-all"
        content={JSON.stringify(
          msg.status === "completed" ||
            msg.status === "error_executing_tool" ||
            msg.status === "rejected"
            ? msg.toolOutput
            : msg.arguments,
          null,
          2,
        )}
      />
    </CollapsibleAction>
  );
};

type ErrorType = "context_limit" | "provider_down" | "network" | "other";

const getErrorType = (error: string): ErrorType => {
  const normalized = error.toLowerCase();

  if (
    normalized.includes("context limit") ||
    normalized.includes("max_tokens") ||
    normalized.includes("prompt is too long") ||
    normalized.includes("token limit")
  ) {
    return "context_limit";
  }

  if (normalized.includes("503 error") || normalized.includes("529 error")) {
    return "provider_down";
  }

  if (normalized.includes("typeerror: fetch failed")) {
    return "network";
  }

  return "other";
};

const SystemMessage = ({
  msg,
  handleCreateNewSession,
  handleSendMessage,
  userMessage,
}: {
  msg: SystemMessage;
  handleCreateNewSession: () => void;
  handleSendMessage: (
    userMessage: UserMessageType,
    options?: {
      clearContextObjects?: boolean;
      clearUserMessage?: boolean;
    },
  ) => Promise<void>;
  userMessage: UserMessageType | null;
}) => {
  if (msg.variant === "error") {
    const errorType = getErrorType(msg.message);
    const errorMessage = msg.message;

    let displayMessage: string;
    switch (errorType) {
      case "context_limit":
        displayMessage =
          "This request will exceed the model's context window for this session. Start a new session to reset the context";
        break;
      case "provider_down":
        displayMessage =
          "We were not able to reach the model provider. Please try a different provider or try again later";
        break;
      case "network":
        displayMessage = "Network error occurred. Please try again";
        break;
      default:
        displayMessage = errorMessage;
    }

    return (
      <ErrorBanner
        className="items-start overflow-x-auto"
        iconClassName="mt-0.5"
        skipErrorReporting={errorType !== "other" ? true : undefined}
        textContainerClassName="flex flex-col gap-2"
      >
        {displayMessage}

        {errorType === "context_limit" && (
          <Button
            variant="border"
            size="xs"
            className="w-fit bg-background"
            Icon={Plus}
            onClick={() => {
              handleCreateNewSession();
            }}
          >
            New session
          </Button>
        )}
        {userMessage && (
          <Button
            variant="border"
            size="xs"
            className="w-fit bg-background"
            Icon={RotateCcw}
            onClick={() => {
              handleSendMessage(userMessage, {
                clearContextObjects: false,
                clearUserMessage: false,
              });
            }}
          >
            Try again
          </Button>
        )}
      </ErrorBanner>
    );
  }
  return (
    <InfoBanner className="items-start" iconClassName="mt-0.5">
      {msg.message}
    </InfoBanner>
  );
};

const CONTEXT_OBJECT_DISPLAY_LIMIT = 3;

const UserMessage = ({
  msg,
  size = "widget",
}: {
  msg: UserMessageType;
  size?: "widget" | "full";
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const [isClamped, setIsClamped] = useState(false);

  //Logic to check if the message is clamped/overflowing past the limit
  useEffect(() => {
    const checkClamped = () => {
      if (ref.current) {
        setIsClamped(ref.current.scrollHeight > ref.current.clientHeight);
      }
    };

    checkClamped();

    window.addEventListener("resize", checkClamped);

    const resizeObserver = new ResizeObserver(checkClamped);
    if (ref.current) {
      resizeObserver.observe(ref.current);
    }

    return () => {
      window.removeEventListener("resize", checkClamped);
      resizeObserver.disconnect();
    };
  }, []);

  const [isExpanded, setIsExpanded] = useState(false);

  const messageContent = (expanded: boolean) => (
    <div
      ref={ref}
      className={cn(
        "overflow-hidden text-xs break-all whitespace-pre-wrap",
        expanded ? "max-h-full" : "max-h-14",
      )}
    >
      {msg.message}
    </div>
  );

  if (isClamped) {
    return (
      <Button
        className={cn(
          "mb-2.5 min-h-7 w-full flex-col items-start justify-center gap-1 rounded-md border border-primary-300 bg-background px-2 text-start font-normal text-primary-900",
          isExpanded ? "pb-2" : "pb-0",
        )}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        {msg.contextObjects && Object.keys(msg.contextObjects).length > 0 && (
          <ContextObjectBadgeSection contextObjects={msg.contextObjects} />
        )}
        {messageContent(isExpanded)}
        {!isExpanded && (
          <div className="absolute right-0 bottom-0 left-0 h-4 rounded-b-md bg-gradient-to-t from-primary-100 to-transparent dark:from-black" />
        )}
        <div className="absolute -bottom-2.5 left-1/2 z-30 -translate-x-1/2 rounded-full border border-primary-300/80 bg-primary-50 p-0.5">
          <ChevronsUpDown className="size-3 text-primary-500" />
        </div>
      </Button>
    );
  }

  return (
    <div className="group min-h-7 flex-col justify-center gap-1 rounded-md border border-primary-300 bg-background p-2 text-primary-900">
      {msg.contextObjects && Object.keys(msg.contextObjects).length > 0 && (
        <ContextObjectBadgeSection contextObjects={msg.contextObjects} />
      )}
      {messageContent(isExpanded)}
    </div>
  );
};

export const ContextObjectBadgeSection = ({
  contextObjects,
  onAddContextObject,
  onDelete,
  onClearAll,
  contextSelectorData,
}: {
  contextObjects: Record<string, ContextObject>;
  onAddContextObject?: (contextObject: ContextObject) => void;
  onDelete?: (contextObject: ContextObject) => void;
  onClearAll?: () => void;
  contextSelectorData?: {
    dataSources: {
      projects: SearchableItemInfo[];
      datasets: SearchableItemInfo[];
      experiments: SearchableItemInfo[];
      promptSessions: SearchableItemInfo[];
      orgs: SearchableItemInfo[];
    };
    btqlTabs: BTQLContextObject[];
  };
}) => {
  const contextObjectsArray = Object.values(contextObjects);

  const hasMore = contextObjectsArray.length > CONTEXT_OBJECT_DISPLAY_LIMIT;
  const displayedObjects = contextObjectsArray.slice(
    0,
    CONTEXT_OBJECT_DISPLAY_LIMIT,
  );
  const restObjects = contextObjectsArray.slice(CONTEXT_OBJECT_DISPLAY_LIMIT);

  return (
    <div className="group flex flex-wrap items-center gap-1">
      {contextSelectorData && onAddContextObject && (
        <ContextDataSourceSelector
          contextSelectorData={contextSelectorData}
          onAddContextObject={onAddContextObject}
          hasDisplayedObjects={displayedObjects.length > 0}
          contextObjects={contextObjects}
        />
      )}
      {displayedObjects.map(
        (contextObjectItem: ContextObject, index: number) => {
          return (
            <ContextObjectBadge
              key={index}
              contextObjectItem={contextObjectItem}
              onDelete={onDelete}
            />
          );
        },
      )}
      {hasMore && (
        <Popover>
          <PopoverTrigger asChild>
            <Button
              size="xs"
              className="flex size-5 min-w-fit items-center rounded-[4px] bg-primary-100 text-xs font-normal text-primary-700"
              variant="border"
            >
              {restObjects.length} +
            </Button>
          </PopoverTrigger>
          <PopoverContent
            className="flex w-fit flex-col gap-1 overflow-y-auto p-1 text-xs"
            align="start"
          >
            {restObjects.map(
              (contextObjectItem: ContextObject, index: number) => {
                return (
                  <ContextObjectBadge
                    key={index}
                    contextObjectItem={contextObjectItem}
                    className="border-none bg-background"
                  />
                );
              },
            )}
          </PopoverContent>
        </Popover>
      )}
      {onClearAll && contextObjectsArray.length > 0 && (
        <Button
          size="xs"
          variant="ghost"
          className="hidden h-5 min-w-0 rounded-[4px] text-[11px] font-normal text-primary-500 group-hover:flex"
          onClick={onClearAll}
        >
          Clear
        </Button>
      )}
    </div>
  );
};
