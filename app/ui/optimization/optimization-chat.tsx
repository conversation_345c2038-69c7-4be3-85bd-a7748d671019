import { <PERSON><PERSON> } from "#/ui/button";
import {
  <PERSON>lend,
  Octagon<PERSON><PERSON><PERSON>,
  PanelRight,
  PictureInPicture2,
  Plus,
  X,
} from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";
import { cn } from "#/utils/classnames";
import {
  type BTQLContextObject,
  useGlobalChat,
} from "./use-global-chat-context";
import { useOptimizationContext } from "#/utils/optimization/provider";
import { Messages } from "./messages";
import { toolLabels } from "./tool-message-ui";
import { PromptTextArea } from "./prompt-text-area";
import { useFeatureFlags } from "#/lib/feature-flags";
import { usePathname } from "next/navigation";
import {
  isBTQLSandboxPage,
  isDatasetPage,
  isExperimentPage,
  isLogsPage,
  isPlaygroundPage,
} from "#/app/app/[org]/pathname-checker";
import { HEIGHT_WITH_TOP_OFFSET } from "#/app/app/body-wrapper";
import { Pop<PERSON>, PopoverContent, PopoverTrigger } from "#/ui/popover";
import { useHotkeys } from "react-hotkeys-hook";
import { SessionsHistoryDropdown } from "#/ui/optimization/sessions-history-dropdown";
import { BasicTooltip } from "#/ui/tooltip";
import { AnimatePresence, motion } from "motion/react";
import { useUpsellContext } from "#/app/playground/upsell-dialog";
import { EmptyState } from "./empty-state";
import { type SearchableItemInfo } from "#/utils/codemirror/btql-lang";
import { useConfirmationContext } from "./confirmation-context";

export const OptimizationChat = ({
  hasMultipleSelectedExperiments = false,
  onBTQLFilter,
  wrapperClassName,
  contextSelectorData,
  onRunInSandbox,
}: {
  hasMultipleSelectedExperiments?: boolean;
  onBTQLFilter?: (filterText: string) => void;
  wrapperClassName?: string;
  contextSelectorData?: {
    dataSources: {
      projects: SearchableItemInfo[];
      datasets: SearchableItemInfo[];
      experiments: SearchableItemInfo[];
      promptSessions: SearchableItemInfo[];
      orgs: SearchableItemInfo[];
    };
    btqlTabs: BTQLContextObject[];
  };
  onRunInSandbox?: (args: {
    query: string;
    title?: string;
    openNewTab?: boolean;
  }) => void;
}) => {
  const {
    isChatOpen,
    setIsChatOpen,
    isDocked,
    setIsDocked,
    createNewSession,
    chatSessions,
    currentChatSessionId,
    setCurrentChatSessionId,
    currentSessionMessages,
    currentSessionContextObjects,
    model,
    currentSessionTools,
    setCurrentSessionContextObjects,
    setCurrentSessionHasInteractedWithContextObjects,
    setModel,
    setCurrentSessionTools,
    currentSessionUserMessage,
    setCurrentSessionUserMessage,
    currentSessionIsActive,
    handleSendMessage,
    handleAbort,
    deleteSession,
    implementedTools,
    pageKey,
    chat,
    screenTooNarrow,
    activeSessionId,
  } = useGlobalChat();
  const {
    isConfirming,
    firstPendingConfirmation,
    getConfirmationByToolCallId,
  } = useConfirmationContext();

  const {
    allowRunningWithoutConsent,
    setAllowRunningWithoutConsent,
    timeRangeSettings,
    setTimeRangeSettings,
  } = useOptimizationContext();
  const textAreaRef = useRef<HTMLTextAreaElement | null>(null);
  const tooltipContentRef = useRef<HTMLDivElement | null>(null);
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true);

  useEffect(() => {
    if (textAreaRef.current) {
      textAreaRef.current.focus();
    }
  }, [currentSessionMessages.length, isDocked, isChatOpen]);

  useEffect(() => {
    if (tooltipContentRef.current && shouldAutoScroll) {
      tooltipContentRef.current.scrollTop =
        tooltipContentRef.current.scrollHeight;
    }
  }, [currentSessionMessages, shouldAutoScroll]);

  const handleScroll = useCallback(() => {
    if (tooltipContentRef.current) {
      const { scrollTop, scrollHeight, clientHeight } =
        tooltipContentRef.current;
      //4px threshold for determining if user is at the bottom.
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 4;
      setShouldAutoScroll(isAtBottom);
    }
  }, []);

  const { onUpsell } = useUpsellContext();

  const handleCreateNewSession = useCallback(() => {
    if (onUpsell) {
      onUpsell();
      return;
    }

    const emptySession = chatSessions.sessions.find(
      (session) =>
        session.messages.length === 0 && session.id !== currentChatSessionId,
    );

    if (emptySession) {
      setCurrentChatSessionId(emptySession.id);
    } else if (currentSessionMessages.length > 0) {
      createNewSession();
    }

    if (textAreaRef.current) {
      textAreaRef.current.focus();
    }
  }, [
    chatSessions.sessions,
    currentChatSessionId,
    currentSessionMessages.length,
    setCurrentChatSessionId,
    createNewSession,
    onUpsell,
  ]);

  const handleAcceptConfirmation = useCallback(() => {
    firstPendingConfirmation?.onConfirm();
  }, [firstPendingConfirmation]);

  useHotkeys(
    "Mod+I",
    () => {
      setIsChatOpen((o) => !o);
    },
    {
      preventDefault: true,
      enableOnFormTags: true,
    },
  );

  useHotkeys(
    "Mod+O",
    () => {
      handleCreateNewSession();
    },
    {
      preventDefault: true,
      enableOnFormTags: true,
      enabled: isChatOpen,
    },
  );

  useHotkeys("Mod+Enter", handleAcceptConfirmation, {
    preventDefault: true,
    enabled: isChatOpen && isConfirming,
    enableOnFormTags: true,
  });

  const finalDockedState = screenTooNarrow ? false : isDocked;

  const contents = (
    <div
      className={cn(
        "relative flex h-[360px] min-h-[360px] w-[400px] flex-col overflow-hidden",
        {
          "h-[calc(100vh-200px)]": currentSessionMessages.length > 0,
          [`right-0 rounded-none rounded-tl-md border bg-primary-50 ${HEIGHT_WITH_TOP_OFFSET} fixed top-[44px]`]:
            finalDockedState,
          [wrapperClassName ?? ""]: finalDockedState,
        },
      )}
    >
      <div
        className={cn(
          "pointer-events-none flex w-full flex-none items-end gap-1",
          {
            "border-b p-[5.5px]": finalDockedState,
            "absolute top-0 right-0 left-0 z-50 rounded-t-md bg-primary-50 py-2 pr-3":
              !finalDockedState,
          },
        )}
      >
        {finalDockedState && (
          <div className="flex items-center gap-2 self-center pl-2 text-xs font-medium">
            <Blend className="size-3" />
            Loop
          </div>
        )}
        <div className="grow" />
        <BasicTooltip
          tooltipContent={
            finalDockedState
              ? "Undock chat"
              : screenTooNarrow
                ? "Increase screen width to dock chat"
                : "Dock chat"
          }
        >
          <Button
            variant="ghost"
            size="icon"
            Icon={finalDockedState ? PictureInPicture2 : PanelRight}
            className={cn(
              "pointer-events-auto z-30 size-7 bg-primary-50 hover:bg-primary-200",
            )}
            disabled={screenTooNarrow}
            onClick={() => {
              setIsDocked((d) => !d);
            }}
          />
        </BasicTooltip>
        <SessionsHistoryDropdown
          chatSessions={chatSessions}
          setCurrentChatSessionId={setCurrentChatSessionId}
          deleteSession={deleteSession}
          currentChatSessionId={currentChatSessionId}
          activeSessionId={activeSessionId}
          isConfirming={isConfirming}
          size="widget"
          className="z-30"
        />
        <BasicTooltip tooltipContent="New chat">
          <Button
            variant="ghost"
            size="icon"
            Icon={Plus}
            onClick={handleCreateNewSession}
            className="pointer-events-auto z-30 size-7 bg-primary-50 hover:bg-primary-200"
          />
        </BasicTooltip>
        <BasicTooltip tooltipContent="Close chat">
          <Button
            variant="ghost"
            size="icon"
            className={cn(
              "pointer-events-auto z-30 size-7 bg-primary-50 hover:bg-primary-200",
            )}
            Icon={X}
            onClick={() => setIsChatOpen(false)}
          />
        </BasicTooltip>
      </div>
      <div className="flex min-h-0 flex-1 flex-col">
        <div
          ref={tooltipContentRef}
          className={cn(
            "flex w-full flex-1 flex-col overflow-x-hidden overflow-y-auto pb-10 text-xs",
            {
              "pt-12": !finalDockedState && currentSessionMessages.length === 0,
              "pt-9": !finalDockedState && currentSessionMessages.length > 0,
            },
          )}
          onScroll={handleScroll}
        >
          {currentSessionMessages.length > 0 ? (
            <Messages
              parsedMessages={currentSessionMessages}
              setAllowRunningWithoutConsent={setAllowRunningWithoutConsent}
              allowRunningWithoutConsent={allowRunningWithoutConsent}
              pageKey={pageKey}
              chat={chat}
              size="widget"
              onBTQLFilter={onBTQLFilter}
              onRunInSandbox={onRunInSandbox}
              availableDataSources={contextSelectorData?.dataSources}
              handleCreateNewSession={handleCreateNewSession}
              handleSendMessage={handleSendMessage}
              getConfirmationByToolCallId={getConfirmationByToolCallId}
            />
          ) : (
            <EmptyState
              page={pageKey}
              handleSendMessage={handleSendMessage}
              hasMultipleSelectedExperiments={hasMultipleSelectedExperiments}
              contextObjects={currentSessionContextObjects}
            />
          )}
        </div>
        <PromptTextArea
          textAreaRef={textAreaRef}
          allowRunningWithoutConsent={allowRunningWithoutConsent}
          setAllowRunningWithoutConsent={setAllowRunningWithoutConsent}
          currentModel={model}
          setCurrentModel={setModel}
          currentTool={currentSessionTools}
          setCurrentTool={setCurrentSessionTools}
          contextObjects={currentSessionContextObjects}
          setContextObject={setCurrentSessionContextObjects}
          setCurrentSessionHasInteractedWithContextObjects={
            setCurrentSessionHasInteractedWithContextObjects
          }
          userMessage={currentSessionUserMessage}
          setUserMessage={setCurrentSessionUserMessage}
          isChatActive={currentSessionIsActive}
          isConfirming={isConfirming}
          handleSendMessage={handleSendMessage}
          handleAbort={handleAbort}
          implementedTools={implementedTools}
          setShouldAutoScroll={setShouldAutoScroll}
          pageKey={pageKey}
          hasMultipleSelectedExperiments={hasMultipleSelectedExperiments}
          timeRangeSettings={timeRangeSettings}
          setTimeRangeSettings={setTimeRangeSettings}
          contextSelectorData={contextSelectorData}
        />
      </div>
    </div>
  );

  const lastMessage = currentSessionMessages[currentSessionMessages.length - 1];
  const status =
    lastMessage?.type === "tool_interaction"
      ? toolLabels[pageKey]["pending_output"][lastMessage.functionName]
      : "Generating...";

  return (
    <>
      {isChatOpen && finalDockedState && contents}
      <Popover open={isChatOpen} onOpenChange={setIsChatOpen}>
        <PopoverTrigger asChild>
          <Button
            size="xs"
            className={cn(
              "group/optimization-chat relative w-16 justify-start overflow-hidden bg-transparent transition-all duration-200 hover:bg-accent-50",
              {
                hidden: finalDockedState && isChatOpen,
                "w-32 bg-accent-50 hover:bg-accent-100":
                  !isChatOpen && currentSessionIsActive && !isConfirming,
                "border-accent-200": isConfirming && currentSessionIsActive,
              },
            )}
            onClick={() => {
              setIsChatOpen((o) => !o);
            }}
          >
            {isConfirming && currentSessionIsActive ? (
              <OctagonAlert className="size-3 rounded-full text-accent-500" />
            ) : (
              <div className="transition-all duration-500 group-hover/optimization-chat:rotate-90">
                <Blend
                  className={cn(
                    "size-3 group-hover/optimization-chat:text-accent-500",
                    {
                      "animate-smooth-spin transition-opacity will-change-transform":
                        currentSessionIsActive && !isConfirming && !isChatOpen,
                    },
                  )}
                />
              </div>
            )}
            <AnimatePresence mode="wait">
              {!isChatOpen && currentSessionIsActive && !isConfirming ? (
                <motion.div
                  key={status}
                  initial={{ opacity: 0, filter: "blur(2px)", translateY: 10 }}
                  animate={{ opacity: 1, filter: "blur(0px)", translateY: 0 }}
                  exit={{ opacity: 0.5, filter: "blur(2px)", translateY: -10 }}
                  transition={{ duration: 0.2 }}
                  className="flex-1 animate-text-shimmer truncate bg-linear-to-r from-primary-300 via-primary-600 to-primary-300 bg-clip-text text-start text-xs text-transparent"
                >
                  {status}
                </motion.div>
              ) : (
                <motion.span
                  initial={{ opacity: 0, filter: "blur(2px)", translateY: 10 }}
                  animate={{ opacity: 1, filter: "blur(0px)", translateY: 0 }}
                  exit={{ opacity: 0.5, filter: "blur(2px)", translateY: -10 }}
                  transition={{ duration: 0.2 }}
                >
                  Loop
                </motion.span>
              )}
            </AnimatePresence>
          </Button>
        </PopoverTrigger>
        {!finalDockedState && (
          <PopoverContent
            onOpenAutoFocus={(e) => {
              e.preventDefault();
              if (textAreaRef.current) {
                textAreaRef.current.focus();
              }
            }}
            align="start"
            collisionPadding={12}
            className="w-[400px] bg-primary-50 p-0"
          >
            {contents}
          </PopoverContent>
        )}
      </Popover>
    </>
  );
};

export const useIsLoopEnabled = () => {
  const pathname = usePathname();
  const { flags, isLoading } = useFeatureFlags();

  const isPlayground = isPlaygroundPage(pathname ?? "");
  const isExperiment = isExperimentPage(pathname ?? "");
  const isLogs = isLogsPage(pathname ?? "");
  const isDataset = isDatasetPage(pathname ?? "");
  const isBTQL = isBTQLSandboxPage(pathname ?? "");
  const matches = isPlayground || isExperiment || isDataset || isLogs || isBTQL;

  return Boolean(!isLoading && flags.loop && matches);
};
