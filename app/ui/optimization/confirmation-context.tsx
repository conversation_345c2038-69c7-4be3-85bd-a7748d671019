import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  type ReactNode,
  type SetStateAction,
  type Dispatch,
  useMemo,
} from "react";
import { type ConfirmationRequest } from "#/utils/optimization/provider";

type ConfirmationByType<T extends ConfirmationRequest["type"]> = Extract<
  ConfirmationRequest,
  { type: T }
>;

function isConfirmationOfType<T extends ConfirmationRequest["type"]>(
  confirmation: ConfirmationRequest,
  type: T,
): confirmation is ConfirmationByType<T> {
  return confirmation.type === type;
}

export interface ConfirmationContextType {
  setPendingConfirmations: Dispatch<
    SetStateAction<Map<string, ConfirmationRequest>>
  >;
  addPendingConfirmation: (confirmation: ConfirmationRequest) => void;
  getConfirmationByToolCallId: (
    toolCallId: string,
  ) => ConfirmationRequest | null;
  getConfirmationByType: <T extends ConfirmationRequest["type"]>(
    type: T,
  ) => ConfirmationByType<T> | null;
  removePendingConfirmation: (toolCallId: string) => void;
  clearAllConfirmations: () => void;
  isConfirming: boolean;
  firstPendingConfirmation: ConfirmationRequest | null;
}

const ConfirmationContext = createContext<ConfirmationContextType>({
  setPendingConfirmations: () => {},
  addPendingConfirmation: () => {},
  getConfirmationByToolCallId: () => null,
  getConfirmationByType: () => null,
  removePendingConfirmation: () => {},
  clearAllConfirmations: () => {},
  isConfirming: false,
  firstPendingConfirmation: null,
});

interface ConfirmationProviderProps {
  children: ReactNode;
}

export function ConfirmationProvider({ children }: ConfirmationProviderProps) {
  const [pendingConfirmations, setPendingConfirmations] = useState<
    Map<string, ConfirmationRequest>
  >(new Map());

  const removePendingConfirmation = useCallback((toolCallId: string) => {
    setPendingConfirmations((prev) => {
      const next = new Map(prev);
      next.delete(toolCallId);
      return next;
    });
  }, []);

  const addPendingConfirmation = useCallback(
    (confirmation: ConfirmationRequest) => {
      if (!confirmation.toolCallId) {
        console.warn(
          "Confirmation missing toolCallId, cannot add to pending confirmations",
        );
        return;
      }
      setPendingConfirmations((prev) => {
        const next = new Map(prev);
        next.set(confirmation.toolCallId!, {
          ...confirmation,
          onCancel: (reason?: string, opts?: unknown) => {
            removePendingConfirmation(confirmation.toolCallId!);
            // @ts-expect-error different signatures per union member
            return confirmation.onCancel(reason, opts);
          },
          // @ts-expect-error different signatures per union member
          onConfirm: (opts?: unknown) => {
            removePendingConfirmation(confirmation.toolCallId!);
            // @ts-expect-error different signatures per union member
            return confirmation.onConfirm(opts);
          },
        });
        return next;
      });
    },
    [removePendingConfirmation],
  );

  const getConfirmationByToolCallId = useCallback(
    (toolCallId: string): ConfirmationRequest | null => {
      return pendingConfirmations.get(toolCallId) ?? null;
    },
    [pendingConfirmations],
  );

  const getConfirmationByType = useCallback(
    <T extends ConfirmationRequest["type"]>(
      type: T,
    ): ConfirmationByType<T> | null => {
      for (const confirmation of pendingConfirmations.values()) {
        if (isConfirmationOfType(confirmation, type)) {
          return confirmation;
        }
      }
      return null;
    },
    [pendingConfirmations],
  );

  const clearAllConfirmations = useCallback(() => {
    setPendingConfirmations(new Map());
  }, []);

  const isConfirming = pendingConfirmations.size > 0;
  const firstPendingConfirmation = useMemo(
    () => Array.from(pendingConfirmations.values())[0] ?? null,
    [pendingConfirmations],
  );

  const value: ConfirmationContextType = useMemo(
    () => ({
      setPendingConfirmations,
      addPendingConfirmation,
      getConfirmationByToolCallId,
      getConfirmationByType,
      removePendingConfirmation,
      clearAllConfirmations,
      isConfirming,
      firstPendingConfirmation,
    }),
    [
      setPendingConfirmations,
      addPendingConfirmation,
      getConfirmationByToolCallId,
      getConfirmationByType,
      removePendingConfirmation,
      clearAllConfirmations,
      isConfirming,
      firstPendingConfirmation,
    ],
  );

  return (
    <ConfirmationContext.Provider value={value}>
      {children}
    </ConfirmationContext.Provider>
  );
}

export function useConfirmationContext() {
  const context = useContext(ConfirmationContext);
  if (!context) {
    throw new Error(
      "useConfirmationContext must be used within a ConfirmationProvider",
    );
  }
  return context;
}
