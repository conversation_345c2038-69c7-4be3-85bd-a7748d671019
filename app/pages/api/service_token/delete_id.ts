import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import { deleteObjects, extractSingularRow } from "../_object_crud_util";
import { type AuthLookup } from "../_lookup_api_key";
import { makeOwnerApiKeysFullResultSetQuery } from "../apikey/_util";
import { transformServiceToken } from "./_util";
import {
  deleteServiceTokenSchema,
  serviceTokenSchema,
} from "@braintrust/typespecs";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { HTTPError } from "#/utils/server-util";
import { z } from "zod";
import { type BtPgClient } from "@braintrust/local/bt-pg";

export async function deleteServiceTokenHelper(
  params: z.infer<typeof deleteServiceTokenSchema>,
  authLookup: AuthLookup,
  client?: BtPgClient,
): Promise<z.infer<typeof serviceTokenSchema>> {
  const supabase = client ?? getServiceRoleSupabase();

  const orgQuery = `select org_id from api_keys join users on api_keys.user_id = users.id where api_keys.id = $1 and users.user_type = 'service_account' `;
  const row = extractSingularRow({
    rows: (await supabase.query(orgQuery, [params.id])).rows,
    notFoundErrorMessage: "Service token not found",
  });

  const orgId = z.string().optional().parse(row.org_id);
  // We only create unscoped service tokens for the data plane manager
  // which is handled in a separate path and shouldn't be relevant for
  // customers who are interacting with the api
  if (!orgId) {
    throw new HTTPError(
      401,
      "No service tokens found or missing organization ownership",
    );
  }

  const { query, queryParams, notFoundErrorMessage } =
    makeOwnerApiKeysFullResultSetQuery({
      authLookup,
      org_id: orgId,
      user_type: "service_account",
      id: params.id,
    });

  let rows;
  try {
    rows = await deleteObjects({
      fullResultsQueryOverride: query,
      baseTableOverride: "api_key",
      startingParams: queryParams,
      fullResultsSize: 1,
      notFoundErrorMessage,
    });
  } catch (error) {
    if (error instanceof Error && error.message?.includes("Expected 1")) {
      throw new HTTPError(404, "Service token not found");
    }
    throw error;
  }

  return serviceTokenSchema.parse(
    transformServiceToken(
      extractSingularRow({
        rows,
        notFoundErrorMessage: undefined,
      }),
    ),
  );
}

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    async (params, authLookup) => {
      return await deleteServiceTokenHelper(params, authLookup);
    },
    {
      paramsSchema: deleteServiceTokenSchema,
      outputSchema: serviceTokenSchema,
    },
  );
}
