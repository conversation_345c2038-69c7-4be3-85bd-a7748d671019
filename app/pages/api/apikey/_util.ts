import { type NextApiRequest, type <PERSON><PERSON>piResponse } from "next";
import { type AuthLookup } from "../_lookup_api_key";
import { type SqlQueryParams } from "#/utils/sql-query-params";
import {
  type QueryFilterParamValue,
  makeUserAuthIdFilter,
  makeFullResultSetQuery,
  CustomQueryFilter,
} from "../_object_crud_util";
import { makeIsOrgOwnerCTE } from "../_special_queries";
import type { UserType } from "../user/_util";

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export function sanitizeApiKey(r: any) {
  return Object.fromEntries(
    Object.entries(r).filter((e) => e[0] !== "key_hash"),
  );
}

// RBAC_DISCLAIMER: A user can fetch only their own API keys.
export function makeApiKeyFullResultSetQuery({
  authLookup,
  startingParams,
  org_id,
  org_name,
  id,
  name,
  internal_metadata,
}: {
  authLookup: AuthLookup;
  startingParams?: SqlQueryParams;
  org_id?: QueryFilterParamValue<string> | null;
  org_name?: QueryFilterParamValue<string> | null;
  id?: QueryFilterParamValue<string> | null;
  name?: QueryFilterParamValue<string> | null;
  internal_metadata?: QueryFilterParamValue<string> | null;
}): {
  query: string;
  queryParams: SqlQueryParams;
  notFoundErrorMessage: string;
} {
  return makeFullResultSetQuery({
    authLookup,
    permissionInfo: "org-membership",
    startingParams,
    priorObjectTables: ["api_key", "user", "organization"],
    fullResultSetAdditionalProjections: [
      "users.email as user_email",
      "users.given_name as user_given_name",
      "users.family_name as user_family_name",
    ],
    filters: {
      org_id,
      org_name,
      id,
      name,
      internal_metadata,
      auth_id: makeUserAuthIdFilter(authLookup.auth_id),
    },
  });
}

// RBAC_DISCLAIMER: Org owners can fetch all API keys for an organization.
export function makeOwnerApiKeysFullResultSetQuery({
  authLookup,
  org_id,
  id,
  name,
  user_type,
}: {
  authLookup: AuthLookup;
  org_id: string;
  user_type: UserType;
  id?: QueryFilterParamValue<string> | null;
  name?: QueryFilterParamValue<string> | null;
}): {
  query: string;
  queryParams: SqlQueryParams;
  notFoundErrorMessage: string;
} {
  const { query: isOrgOwnerCTE, queryParams: isOrgOwnerQueryParams } =
    makeIsOrgOwnerCTE({
      userId: authLookup.user_id,
      orgId: org_id,
    });

  const { query: resultSetQuery, queryParams: resultSetQueryParams } =
    makeFullResultSetQuery({
      authLookup,
      permissionInfo: "org-membership",
      startingParams: isOrgOwnerQueryParams,
      priorObjectTables: ["api_key", "user", "organization"],
      fullResultSetAdditionalProjections: [
        "users.email as user_email",
        "users.given_name as user_given_name",
        "users.family_name as user_family_name",
        "users.user_type as user_type",
      ],
      filters: {
        id,
        name,
        org_id: new CustomQueryFilter({
          values: [org_id],
          tableName: "api_keys",
          col: "org_id",
        }),
        user_type: new CustomQueryFilter({
          values: [user_type],
          tableName: "users",
          col: "user_type",
        }),
      },
    });

  const query = `
    ${isOrgOwnerCTE}
    ${resultSetQuery}
    and exists (select 1 from is_org_owner where is_org_owner.exists)
  `;

  return {
    query,
    queryParams: resultSetQueryParams,
    notFoundErrorMessage: "No API keys found or missing organization ownership",
  };
}

/**
 * dummy placeholder function to make nextjs validator happy
 */
export default async function _action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  return;
}
