import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import {
  idParamSchema,
  extractSingularRow,
  patchObjects,
} from "../_object_crud_util";
import { projectSchema, patchProjectSchema } from "@braintrust/typespecs";
import { flushCache } from "../_invoke_supabase_udf";
import type { AuthLookup } from "#/utils/server-util";
import { type BtPgClient } from "@braintrust/local/bt-pg";

const paramsSchema = idParamSchema.merge(patchProjectSchema);

export async function helper(
  projectId: string,
  updates: Record<string, unknown>,
  authLookup: AuthLookup,
  client?: BtPgClient,
): Promise<unknown> {
  try {
    return extractSingularRow({
      rows: await patchObjects(
        {
          authLookup,
          permissionInfo: {
            aclObjectType: "project",
          },
          filters: {
            id: projectId,
          },
          patchValueParams: updates,
          fullResultsSize: 1,
        },
        client,
      ),
      notFoundErrorMessage: undefined,
    });
  } finally {
    await flushCache({ objectId: projectId });
  }
}

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    async ({ id, ...paramsRest }, authLookup) => {
      return await helper(id, paramsRest, authLookup);
    },
    {
      paramsSchema,
      outputSchema: projectSchema,
    },
  );
}
