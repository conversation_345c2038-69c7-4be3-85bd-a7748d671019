import type { NextApiRequest, NextApiResponse } from "next";
import { runVercelRequest } from "#/lib/vercel/_request_util";
import {
  listProductBillingPlansRequestSchema,
  getBillingPlansResponseSchema,
} from "#/lib/vercel/schemas";
import { getAllBillingPlans } from "#/lib/partner/vercel";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runVercelRequest(req, res, {
    GET: {
      paramsSchema: listProductBillingPlansRequestSchema,
      outputSchema: getBillingPlansResponseSchema,
      authType: "system",
      handler: async (claims, _) =>
        getAllBillingPlans(claims.installation_id ?? ""),
    },
  });
}
