import type { NextApiRequest, NextApiResponse } from "next";
import {
  runVercelRequest,
  checkVercelSystemAuth,
  checkVercelUser<PERSON>uth,
  JsonHTTPError,
} from "#/lib/vercel/_request_util";
import {
  installationIdSchema,
  installationResponseSchema,
  updateInstallationRequestSchema,
  installIntegrationRequestSchema,
  deleteInstallationRequestSchema,
  deleteInstallationResponseSchema,
} from "#/lib/vercel/schemas";
import {
  getAllBillingPlans,
  getInstallation,
  installIntegration,
  uninstallInstallation,
  updateInstallation,
} from "#/lib/partner/vercel";
import { testingOnlyVercelAuthRequest } from "#/lib/vercel/auth";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runVercelRequest(req, res, {
    GET: {
      paramsSchema: installationIdSchema,
      outputSchema: installationResponseSchema,
      authType: "system",
      handler: async (_, params) => {
        const claims = await checkVercelSystemAuth(req);
        const installation = await getInstallation(
          claims,
          params.installationId,
        );
        if (!installation) {
          throw new JsonHTTPError(403, {
            error: {
              code: "forbidden",
              fields: null,
              message: "Access denied to this installation",
              user: {
                message: "Access denied to this installation",
                url: null,
              },
            },
          });
        }

        // Get stored billing plan or default to free
        const { plans: billingPlans } = await getAllBillingPlans(
          params.installationId,
        );
        const billingPlanId = installation.billing_plan_id || "free";
        const billingPlan =
          billingPlans.find((plan) => plan.id === billingPlanId) ||
          billingPlans[0];

        return {
          billingPlan: {
            ...billingPlan,
            scope: "installation",
          },
        };
      },
    },
    PUT: {
      paramsSchema: installIntegrationRequestSchema,
      outputSchema: installationResponseSchema,
      authType: "user",
      handler: async (_, params) => {
        const claims = await checkVercelUserAuth(req);
        const test_overrideAuth = !!testingOnlyVercelAuthRequest(req);
        if (
          !(
            "eula" in params.acceptedPolicies &&
            "privacy" in params.acceptedPolicies
          )
        ) {
          throw new JsonHTTPError(400, {
            error: {
              code: "validation_error",
              fields: null,
              message: "Must accept EULA and Privacy Policy",
              user: {
                message: "Must accept EULA and Privacy Policy",
                url: null,
              },
            },
          });
        }

        return await installIntegration(
          claims,
          {
            ...params,
            type: "marketplace",
          },
          test_overrideAuth,
        );
      },
    },
    PATCH: {
      paramsSchema: updateInstallationRequestSchema,
      outputSchema: installationResponseSchema,
      authType: "user",
      handler: async (_, params) => {
        const claims = await checkVercelUserAuth(req);
        await updateInstallation(claims, params.billingPlanId);

        // Return updated billing plan information
        const { plans: billingPlans } = await getAllBillingPlans(
          params.installationId,
        );
        const billingPlan = billingPlans.find(
          (plan) => plan.id === params.billingPlanId,
        );

        return {
          billingPlan: billingPlan
            ? {
                ...billingPlan,
                scope: "installation",
              }
            : undefined,
        };
      },
    },
    DELETE: {
      paramsSchema: deleteInstallationRequestSchema,
      outputSchema: deleteInstallationResponseSchema,
      handler: async (claims, params) =>
        uninstallInstallation(
          claims,
          params.installationId,
          params.cascadeResourceDeletion,
        ),
    },
  });
}
