import type { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";
import {
  runVercelRequest,
  checkVercelSystemAuth,
  checkVercelUserAuth,
  JsonHTTPError,
} from "#/lib/vercel/_request_util";
import {
  deleteResource,
  getResource,
  updateResource,
} from "#/lib/partner/vercel";
import {
  updateResourceRequestSchema,
  getResourceResponseSchema,
  updateResourceResponseSchema,
} from "#/lib/vercel/schemas";

const resourceIdSchema = z.object({
  installationId: z.string(),
  resourceId: z.string(),
});

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runVercelRequest(req, res, {
    GET: {
      paramsSchema: resourceIdSchema,
      outputSchema: getResourceResponseSchema,
      authType: "system",
      handler: async (_, params) => {
        const claims = await checkVercelSystemAuth(req);
        const resource = await getResource(
          claims,
          params.installationId,
          params.resourceId,
        );

        if (!resource) {
          throw new JsonHTTPError(404, {
            error: {
              code: "not_found",
              fields: null,
              message: `Resource ${params.resourceId} not found`,
              user: {
                message: `Resource ${params.resourceId} not found`,
                url: null,
              },
            },
          });
        }

        return resource;
      },
    },
    PATCH: {
      paramsSchema: resourceIdSchema.merge(updateResourceRequestSchema),
      outputSchema: updateResourceResponseSchema,
      authType: "user",
      handler: async (_, params) => {
        const claims = await checkVercelUserAuth(req);
        const { resourceId, ...updates } = params;

        return await updateResource(claims, resourceId, updates);
      },
    },
    DELETE: {
      paramsSchema: resourceIdSchema,
      outputSchema: z.undefined(),
      authType: "user",
      handler: async (_, params) => {
        const claims = await checkVercelUserAuth(req);
        await deleteResource(claims, params.resourceId);
      },
    },
  });
}
