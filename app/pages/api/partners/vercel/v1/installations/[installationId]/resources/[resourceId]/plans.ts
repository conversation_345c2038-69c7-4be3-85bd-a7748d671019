import type { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";
import { runVercelRequest } from "#/lib/vercel/_request_util";
import { getBillingPlansResponseSchema } from "#/lib/vercel/schemas";
import { getAllBillingPlans } from "#/lib/partner/vercel";

const resourcePlansSchema = z.object({
  installationId: z.string(),
  resourceId: z.string(),
});

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runVercelRequest(req, res, {
    GET: {
      paramsSchema: resourcePlansSchema,
      outputSchema: getBillingPlansResponseSchema,
      authType: "user",
      handler: async (_, params) => getAllBillingPlans(params.installationId),
    },
  });
}
