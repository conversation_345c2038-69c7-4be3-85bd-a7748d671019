import type { NextApiRequest, NextApiResponse } from "next";
import {
  runVercelRequest,
  checkVercelUserAuth,
} from "#/lib/vercel/_request_util";
import { provisionResource } from "#/lib/partner/vercel";
import {
  provisionResourceRequestSchema,
  provisionResourceResponseSchema,
  installationIdSchema,
} from "#/lib/vercel/schemas";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runVercelRequest(req, res, {
    POST: {
      paramsSchema: installationIdSchema.merge(provisionResourceRequestSchema),
      outputSchema: provisionResourceResponseSchema,
      authType: "user",
      handler: async (_, params) => {
        const claims = await checkVercelUserAuth(req);
        return await provisionResource(claims, {
          name: params.name,
          productId: params.productId,
          billingPlanId: params.billingPlanId,
          metadata: params.metadata,
        });
      },
    },
  });
}
