infer: root_span_id from: experiment('singleton');
infer: scores | from: experiment('singleton');
infer: metadata | from: experiment('singleton') | filter: is_root;

/*!result -- This should return 0 rows (no inference budget)
  length == 0
 */
infer: input | from: experiment('singleton') | inference_budget: 0;

/*!result -- This should return 1 row
  length == 1
 */
infer: input | from: experiment('singleton') | inference_budget: 1;
