[{"error": null, "query": "infer: root_span_id from: experiment('singleton')", "result_rows": [{"name": ["root_span_id"], "top_values": [{"count": 6, "value": "07d1b1e7-4fcf-46c8-af9a-9638bc868627"}, {"count": 6, "value": "69ab9061-157a-43e1-a932-ca1c1579d5e7"}, {"count": 6, "value": "6c4053be-dedb-4353-abb7-0fe4039090be"}, {"count": 6, "value": "79b07f13-7c91-44f6-9519-533fdce40ba3"}, {"count": 6, "value": "85f1de08-2192-4039-886a-056658f86580"}, {"count": 6, "value": "8aceaf3e-f6a0-49d7-b8de-fd12bf6063e8"}, {"count": 6, "value": "967a194a-6e07-4ca5-8b2d-e07771a355bf"}, {"count": 6, "value": "c3303800-7aa9-42e4-8bdb-fa71a4950ab3"}, {"count": 6, "value": "f49e0cfd-42a7-4da9-8f51-a22df367d5e9"}, {"count": 6, "value": "fb96be18-8f99-4456-ba26-72a503f67ba0"}], "type": {"type": "string"}}], "skip": false}, {"error": null, "query": "infer: scores | from: experiment('singleton')", "result_rows": [{"name": ["scores", "Factuality"], "top_values": [{"count": 1, "value": 0}, {"count": 9, "value": 1}], "type": {"type": "number"}}, {"name": ["scores", "Levenshtein"], "top_values": [{"count": 1, "value": 0.021739}, {"count": 1, "value": 0.024096}, {"count": 1, "value": 0.024631}, {"count": 1, "value": 0.024752}, {"count": 1, "value": 0.027624}, {"count": 1, "value": 0.029268}, {"count": 1, "value": 0.031746}, {"count": 1, "value": 0.032258}, {"count": 1, "value": 0.034146}, {"count": 1, "value": 0.036585}], "type": {"type": "number"}}], "skip": false}, {"error": null, "query": "infer: metadata | from: experiment('singleton') | filter: is_root", "result_rows": [{"name": ["metadata", "category"], "top_values": [{"count": 5, "value": "casual"}, {"count": 5, "value": "formal"}], "type": {"type": "string"}}, {"name": ["metadata", "language"], "top_values": [{"count": 4, "value": "Spanish"}, {"count": 6, "value": "English"}], "type": {"type": "string"}}, {"name": ["metadata", "user_type"], "top_values": [{"count": 5, "value": "new"}, {"count": 5, "value": "returning"}], "type": {"type": "string"}}], "skip": false}, {"error": null, "query": "/*!result -- This should return 0 rows (no inference budget)\n  length == 0\n */\ninfer: input | from: experiment('singleton') | inference_budget: 0", "result_rows": [], "skip": false}, {"error": null, "query": "/*!result -- This should return 1 row\n  length == 1\n */\ninfer: input | from: experiment('singleton') | inference_budget: 1", "result_rows": [{"name": ["input"], "top_values": [{"count": 2, "value": "Center different west community myself major travel southern consumer company together view say scene various grow very line ..."}], "type": {"type": "string"}}], "skip": false}]