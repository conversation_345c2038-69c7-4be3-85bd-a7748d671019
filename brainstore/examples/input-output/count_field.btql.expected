[{"error": null, "query": "/*!optimizer -- metadata field should use only_exists optimization\n[.. | objects | select(has(\"TantivyAggregate\")) | .TantivyAggregate.projection] | .[0] | length == 1 and .[0].alias == \"metadata\" and .[0].only_exists == true\n*/\nfrom: dataset('singleton') | measures: count(metadata) as non_null", "result_rows": [{"non_null": 6}], "skip": false}, {"error": null, "query": "/*!optimizer -- metadata field should use only_exists optimization\n[.. | objects | select(has(\"TantivyAggregate\")) | .TantivyAggregate.projection] | .[0] | length == 1 and .[0].alias == \"metadata\" and .[0].only_exists == true\n*/\nfrom: dataset('singleton') | measures: sum(metadata is null) as m_null", "result_rows": [{"m_null": 1}], "skip": false}, {"error": null, "query": "/*!optimizer -- metadata field should use only_exists optimization\n[.. | objects | select(has(\"TantivyAggregate\")) | .TantivyAggregate.projection] | .[0] | length == 1 and .[0].alias == \"metadata\" and .[0].only_exists == true\n*/\nfrom: dataset('singleton') | measures: sum(metadata is not null) as m_not_null", "result_rows": [{"m_not_null": 6}], "skip": false}, {"error": null, "query": "/*!optimizer -- span_attributes field should use only_exists optimization\n[.. | objects | select(has(\"TantivyAggregate\")) | .TantivyAggregate.projection] | .[0] | length == 1 and .[0].alias == \"span__attributes\" and .[0].only_exists == true\n*/\nfrom: dataset('singleton') | measures: count(span_attributes) as non_null", "result_rows": [{"non_null": 6}], "skip": false}, {"error": null, "query": "/*!optimizer -- span_attributes field should use only_exists optimization\n[.. | objects | select(has(\"TantivyAggregate\")) | .TantivyAggregate.projection] | .[0] | length == 1 and .[0].alias == \"span__attributes\" and .[0].only_exists == true\n*/\nfrom: dataset('singleton') | measures: sum(span_attributes is null) as m_null", "result_rows": [{"m_null": 1}], "skip": false}, {"error": null, "query": "/*!optimizer -- span_attributes field should use only_exists optimization\n[.. | objects | select(has(\"TantivyAggregate\")) | .TantivyAggregate.projection] | .[0] | length == 1 and .[0].alias == \"span__attributes\" and .[0].only_exists == true\n*/\nfrom: dataset('singleton') | measures: sum(span_attributes is not null) as m_not_null", "result_rows": [{"m_not_null": 6}], "skip": false}]