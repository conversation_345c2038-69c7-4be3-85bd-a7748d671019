/*!optimizer -- metadata field should use only_exists optimization
[.. | objects | select(has("TantivyAggregate")) | .TantivyAggregate.projection] | .[0] | length == 1 and .[0].alias == "metadata" and .[0].only_exists == true
*/
from: dataset('singleton') | measures: count(metadata) as non_null;

/*!optimizer -- metadata field should use only_exists optimization
[.. | objects | select(has("TantivyAggregate")) | .TantivyAggregate.projection] | .[0] | length == 1 and .[0].alias == "metadata" and .[0].only_exists == true
*/
from: dataset('singleton') | measures: sum(metadata is null) as m_null;

/*!optimizer -- metadata field should use only_exists optimization
[.. | objects | select(has("TantivyAggregate")) | .TantivyAggregate.projection] | .[0] | length == 1 and .[0].alias == "metadata" and .[0].only_exists == true
*/
from: dataset('singleton') | measures: sum(metadata is not null) as m_not_null;

/*!optimizer -- span_attributes field should use only_exists optimization
[.. | objects | select(has("TantivyAggregate")) | .TantivyAggregate.projection] | .[0] | length == 1 and .[0].alias == "span__attributes" and .[0].only_exists == true
*/
from: dataset('singleton') | measures: count(span_attributes) as non_null;

/*!optimizer -- span_attributes field should use only_exists optimization
[.. | objects | select(has("TantivyAggregate")) | .TantivyAggregate.projection] | .[0] | length == 1 and .[0].alias == "span__attributes" and .[0].only_exists == true
*/
from: dataset('singleton') | measures: sum(span_attributes is null) as m_null;

/*!optimizer -- span_attributes field should use only_exists optimization
[.. | objects | select(has("TantivyAggregate")) | .TantivyAggregate.projection] | .[0] | length == 1 and .[0].alias == "span__attributes" and .[0].only_exists == true
*/
from: dataset('singleton') | measures: sum(span_attributes is not null) as m_not_null;
