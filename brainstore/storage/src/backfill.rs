use std::{
    collections::HashMap,
    sync::{atomic::AtomicBool, Arc},
    time::Duration,
};

use clap::Parser;
use lazy_static::lazy_static;
use otel_common::opentelemetry::metrics::{Counter, UpDownCounter};
use rand::Rng;
use serde::{Deserialize, Serialize};
use tokio::time::Instant;
use tracing::instrument;

use crate::{
    compaction_loop::CompactionLoop,
    config_with_store::ConfigWithStore,
    global_store::{
        BackfillBrainstoreObject, BackfillTrackingEntry, BackfillTrackingEntryId,
        BackfillTrackingEntryUpdate, GlobalStore,
    },
    postgres_wal::{PostgresWAL, PostgresWalStreamBounded, PostgresWalStreamOpts},
    process_wal::{
        process_object_wal, ProcessObjectWalInput, ProcessObjectWalOptionalInput,
        ProcessObjectWalOptions,
    },
};

use futures::future::join_all;
use util::{anyhow::Result, system_types::ObjectType};

struct BackfillMeters {
    // Time spent in backfilling.
    elapsed_time_ms_counter: Counter<u64>,
    // Number of active realtime workers.
    active_realtime_workers: UpDownCounter<i64>,
    // Number of active historical workers.
    active_historical_workers: UpDownCounter<i64>,
}

impl BackfillMeters {
    fn new() -> Self {
        let meter = otel_common::opentelemetry::global::meter("brainstore");
        Self {
            elapsed_time_ms_counter: meter
                .u64_counter("brainstore.process_wal_worker.elapsed_time_ms")
                .build(),
            active_realtime_workers: meter
                .i64_up_down_counter("brainstore.process_wal_worker.active_realtime_workers")
                .build(),
            active_historical_workers: meter
                .i64_up_down_counter("brainstore.process_wal_worker.active_historical_workers")
                .build(),
        }
    }
}

struct UpDownCounterHandle {
    counter: UpDownCounter<i64>,
}

impl UpDownCounterHandle {
    fn new(counter: UpDownCounter<i64>) -> Self {
        counter.add(1, &[]);
        Self { counter }
    }
}

impl Drop for UpDownCounterHandle {
    fn drop(&mut self) {
        self.counter.add(-1, &[]);
    }
}

lazy_static! {
    static ref BACKFILL_METERS: BackfillMeters = BackfillMeters::new();
}

struct BackfillOneTrackingEntryInput<'a> {
    tracking_entry: BackfillTrackingEntry,
    config: &'a ConfigWithStore,
    process_wal_opts: &'a ProcessObjectWalOptions,
    compaction_loop: &'a CompactionLoop,
}

#[derive(Debug, Clone, Parser, Serialize, Deserialize)]
pub struct BackfillOneTrackingEntryOptions {
    /// The batch size of sequence IDs we query over for fetching rows belonging
    /// to the tracking entry.
    #[arg(
        long,
        default_value_t = default_sequence_id_batch_size(),
        env = "BRAINSTORE_BACKFILL_SEQUENCE_ID_BATCH_SIZE"
    )]
    #[serde(default = "default_sequence_id_batch_size")]
    pub sequence_id_batch_size: usize,

    /// The maximum number of loop iterations we spend backfilling the tracking
    /// entry. Each loop iteration attempts to advance the tracking entry by
    /// `sequence_id_batch_size`.
    #[arg(
        long,
        default_value_t = default_max_backfill_iterations(),
        env = "BRAINSTORE_BACKFILL_MAX_ITERATIONS"
    )]
    #[serde(default = "default_max_backfill_iterations")]
    pub max_backfill_iterations: usize,
}

fn default_sequence_id_batch_size() -> usize {
    20_000
}

fn default_max_backfill_iterations() -> usize {
    1000
}

impl Default for BackfillOneTrackingEntryOptions {
    fn default() -> Self {
        Self {
            sequence_id_batch_size: default_sequence_id_batch_size(),
            max_backfill_iterations: default_max_backfill_iterations(),
        }
    }
}

#[derive(Default, Debug, Clone)]
#[allow(unused)]
struct BackfillOneTrackingEntryOutput {
    acquired_lock: bool,
    num_iterations: usize,
}

#[instrument(err, skip(input), fields(tracking_entry_id = input.tracking_entry.id().to_string()))]
async fn backfill_one_tracking_entry(
    input: BackfillOneTrackingEntryInput<'_>,
    options: BackfillOneTrackingEntryOptions,
) -> Result<BackfillOneTrackingEntryOutput> {
    let _tracking_entry_lock = match input
        .config
        .locks_manager
        .try_write(&format!(
            "backfill_one_tracking_entry:{}",
            input.tracking_entry.id()
        ))
        .await?
    {
        Some(lock) => lock,
        None => {
            log::debug!("Skipping backfilling tracking entry {} because another worker is already processing it", input.tracking_entry.id());
            return Ok(BackfillOneTrackingEntryOutput::default());
        }
    };

    let start_backfill_time = Instant::now();
    let sequence_id_batch_size = i64::try_from(options.sequence_id_batch_size)?;
    let max_backfill_iterations = options.max_backfill_iterations;

    let mut tracking_entry = input.tracking_entry;
    let mut num_iterations = 0;
    while num_iterations < max_backfill_iterations {
        let tracking_entry_ids = [tracking_entry.id()];
        let (brainstore_objects, start_sequence_id, end_sequence_id, is_logs2) = if tracking_entry
            .last_processed_sequence_id
            < tracking_entry.last_encountered_sequence_id
        {
            let upper_bound_sequence_id = std::cmp::min(
                tracking_entry.last_processed_sequence_id + sequence_id_batch_size,
                tracking_entry.last_encountered_sequence_id,
            );
            let brainstore_objects = input
                .config
                .global_store
                .query_backfill_brainstore_objects(
                    &tracking_entry_ids,
                    false, /* is_logs2 */
                    tracking_entry.last_processed_sequence_id + 1,
                    upper_bound_sequence_id,
                )
                .await?
                .remove(0);
            (
                brainstore_objects,
                tracking_entry.last_processed_sequence_id + 1,
                upper_bound_sequence_id,
                false,
            )
        } else if tracking_entry.last_processed_sequence_id_2
            < tracking_entry.last_encountered_sequence_id_2
        {
            let upper_bound_sequence_id = std::cmp::min(
                tracking_entry.last_processed_sequence_id_2 + sequence_id_batch_size,
                tracking_entry.last_encountered_sequence_id_2,
            );
            let brainstore_objects = input
                .config
                .global_store
                .query_backfill_brainstore_objects(
                    &tracking_entry_ids,
                    true, /* is_logs2 */
                    tracking_entry.last_processed_sequence_id_2 + 1,
                    upper_bound_sequence_id,
                )
                .await?
                .remove(0);
            (
                brainstore_objects,
                tracking_entry.last_processed_sequence_id_2 + 1,
                upper_bound_sequence_id,
                true,
            )
        } else {
            // Nothing left to process here.
            break;
        };

        let iter_start_time = Instant::now();

        join_all(brainstore_objects.iter().map(|object| {
            process_one_object(
                object,
                input.config,
                input.process_wal_opts,
                input.compaction_loop,
            )
        }))
        .await
        .into_iter()
        .collect::<Result<()>>()?;

        let tracking_entry_update = if is_logs2 {
            BackfillTrackingEntryUpdate {
                last_processed_sequence_id_2: Some(end_sequence_id),
                ..Default::default()
            }
        } else {
            BackfillTrackingEntryUpdate {
                last_processed_sequence_id: Some(end_sequence_id),
                ..Default::default()
            }
        };
        tracking_entry = input
            .config
            .global_store
            .update_backfill_tracking_entries(vec![(tracking_entry.id(), tracking_entry_update)])
            .await?
            .remove(0);

        let iter_duration_ms =
            u64::try_from(iter_start_time.elapsed().as_millis()).unwrap_or(u64::MAX);
        let total_backfill_duration_ms =
            u64::try_from((Instant::now() - start_backfill_time).as_millis()).unwrap_or(u64::MAX);

        tracing::info!(
            tracking_entry_id = tracking_entry.id().to_string(),
            start_sequence_id = start_sequence_id,
            end_sequence_id = end_sequence_id,
            is_logs2 = is_logs2,
            iter_duration_ms = iter_duration_ms,
            iter_num = num_iterations,
            total_backfill_duration_ms = total_backfill_duration_ms,
            "Processed tracking entry",
        );

        BACKFILL_METERS.elapsed_time_ms_counter.add(
            iter_duration_ms,
            &[otel_common::opentelemetry::KeyValue::new(
                "tracking_entry_id",
                tracking_entry.id().to_string(),
            )],
        );

        num_iterations += 1;
    }

    Ok(BackfillOneTrackingEntryOutput {
        acquired_lock: true,
        num_iterations,
    })
}

#[instrument(skip(config, process_wal_opts, compaction_loop))]
async fn process_one_object(
    object: &BackfillBrainstoreObject,
    config: &ConfigWithStore,
    process_wal_opts: &ProcessObjectWalOptions,
    compaction_loop: &CompactionLoop,
) -> Result<()> {
    // Wrap this in a bounded postgres wal stream IF it's a PostgresWAL.
    //
    // There is a general issue with backfilling, due to the fact that we
    // process in ranges of sequence ids. It's possible that an individual
    // transaction for an object, spanning multiple sequence ids, may get split
    // into separate backfill batches. This is problematic for realtime queries,
    // because say our first batch has completed and updated its
    // `last_processed_xact_id` to X. If we then make a realtime query, it will
    // think the entire transaction X has been processed and read it from the
    // segment WAL, and then miss the remaining rows for that transaction.
    //
    // This issue shows up in our unit tests, so we insert a hacky-workaround
    // for it by incrementing the max_sequence_id by a constant to help
    // guarantee we capture all logs for the transaction.
    let mut config = config.clone();
    config.wal = match config.wal.as_ref().downcast_ref::<PostgresWAL>() {
        Some(_) => Arc::new(PostgresWalStreamBounded::new(
            config.wal,
            PostgresWalStreamOpts {
                start_sequence_id: Some(u64::try_from(object.min_sequence_id)?),
                end_sequence_id: Some(u64::try_from(object.max_sequence_id + 20000)?),
                read_logs2: Some(object.is_logs2),
                ..Default::default()
            },
        )),
        None => config.wal,
    };

    let start = std::time::Instant::now();
    let result = process_object_wal(
        ProcessObjectWalInput {
            object_id: object.object_id.as_ref(),
            config: &config,
        },
        ProcessObjectWalOptionalInput {
            start_xact_id: Some(object.min_xact_id),
            end_xact_id: Some(object.max_xact_id),
            source: Some(
                (BackfillTrackingEntryId {
                    project_id: &object.project_id,
                    object_type: object.object_id.object_type,
                })
                .to_string(),
            ),
            ..Default::default()
        },
        process_wal_opts.clone(),
    )
    .await?;
    tracing::debug!(
        object = format!("{:?}", object),
        duration_ms = start.elapsed().as_millis(),
        "Processed WAL",
    );
    tracing::debug!(
        object_id = object.object_id.to_string(),
        modified_segment_ids = result.modified_segment_ids.len(),
        "Modified segment IDs"
    );

    if compaction_loop
        .add_segments(
            result
                .modified_segment_ids
                .into_iter()
                .map(|id| (id, Some(object.object_id.object_type))),
        )
        .is_err()
    {
        tracing::warn!(
            object_id = object.object_id.to_string(),
            "Failed to add segments to compaction queue. At capacity. Slow down!"
        );
    }

    Ok(())
}

pub struct BackfillWorkerInput {
    pub config: ConfigWithStore,
    pub process_wal_opts: ProcessObjectWalOptions,
    pub compaction_loop: CompactionLoop,
}

#[derive(Default, Clone)]
pub struct BackfillWorkerOptionalInput {
    pub terminate_signal: Option<Arc<AtomicBool>>,
    pub testing_signal_produced_entry: Option<async_channel::Sender<()>>,
}

#[derive(Debug, Clone, Parser, Serialize, Deserialize)]
pub struct BackfillWorkerOptions {
    /// The number of backfill workers to use for realtime objects (which have
    /// already completed their initial backfill). Each worker will work on a
    /// single tracking entry at a time.
    #[arg(
        long,
        default_value_t = default_num_backfill_workers_realtime(),
        env = "BRAINSTORE_NUM_BACKFILL_WORKERS_REALTIME"
    )]
    #[serde(default = "default_num_backfill_workers_realtime")]
    pub num_backfill_workers_realtime: usize,

    /// The number of backfill workers to use for historical objects (which have
    /// not already completed their initial backfill). Each worker will work on
    /// a single tracking entry at a time.
    #[arg(
        long,
        default_value_t = default_num_backfill_workers_historical(),
        env = "BRAINSTORE_NUM_BACKFILL_WORKERS_HISTORICAL"
    )]
    #[serde(default = "default_num_backfill_workers_historical")]
    pub num_backfill_workers_historical: usize,

    #[command(flatten)]
    #[serde(flatten)]
    pub backfill_one_tracking_entry_opts: BackfillOneTrackingEntryOptions,
}

impl Default for BackfillWorkerOptions {
    fn default() -> Self {
        Self {
            num_backfill_workers_realtime: default_num_backfill_workers_realtime(),
            num_backfill_workers_historical: default_num_backfill_workers_historical(),
            backfill_one_tracking_entry_opts: BackfillOneTrackingEntryOptions::default(),
        }
    }
}

fn default_num_backfill_workers_realtime() -> usize {
    100
}

fn default_num_backfill_workers_historical() -> usize {
    100
}

pub async fn backfill_worker(
    input: BackfillWorkerInput,
    optional_input: BackfillWorkerOptionalInput,
    options: BackfillWorkerOptions,
) {
    // The worker is an infinite loop that works as follows:
    //
    // - One background task continuously loops through the un-backfilled
    // tracking entries and enqueues them for backfilling.
    //
    // - A pool of worker tasks continuously dequeue and backfill the
    // tracking entries.
    //
    // We spawn separate producers and worker pools for processing
    // already-backfilled and not-already-backfilled entries, so that we can
    // adjust their resource allocations independently.
    let bookkeeper = CurrentlyBackfillingBookkeeper::new();

    let make_futs = |has_completed_initial_backfill: bool| {
        let num_workers = if has_completed_initial_backfill {
            options.num_backfill_workers_realtime
        } else {
            options.num_backfill_workers_historical
        };
        let (sender, receiver) = if num_workers > 0 {
            let (s, r) = async_channel::bounded::<BackfillTrackingEntry>(num_workers);
            (Some(s), Some(r))
        } else {
            (None, None)
        };
        let producer_fut = backfill_producer_task(
            sender,
            has_completed_initial_backfill,
            &*input.config.global_store,
            optional_input.terminate_signal.clone(),
            optional_input.testing_signal_produced_entry.clone(),
        );
        let worker_futs = (0..num_workers)
            .map(|_| {
                backfill_worker_task(
                    receiver.clone(),
                    bookkeeper.clone(),
                    &input.config,
                    &input.process_wal_opts,
                    &input.compaction_loop,
                    options.backfill_one_tracking_entry_opts.clone(),
                )
            })
            .collect::<Vec<_>>();
        (producer_fut, worker_futs)
    };

    let (producer_fut_realtime, worker_futs_realtime) = make_futs(true);
    let (producer_fut_historical, worker_futs_historical) = make_futs(false);
    tokio::join!(
        producer_fut_realtime,
        join_all(worker_futs_realtime),
        producer_fut_historical,
        join_all(worker_futs_historical)
    );
}

async fn backfill_producer_task(
    sender: Option<async_channel::Sender<BackfillTrackingEntry>>,
    has_completed_initial_backfill: bool,
    global_store: &dyn GlobalStore,
    terminate_signal: Option<Arc<AtomicBool>>,
    testing_producer_signal_produced_entry: Option<async_channel::Sender<()>>,
) {
    let error_sleep_duration = Duration::from_millis(100);
    const TRACKING_ENTRIES_BATCH_SIZE: usize = 100;

    let sender = if let Some(s) = sender {
        s
    } else {
        return;
    };

    let mut tick_manager = ProducerLoopTickManager::new();
    let mut cursor: Option<(String, ObjectType)> = None;
    loop {
        if let Some(terminate_signal) = &terminate_signal {
            if terminate_signal.load(std::sync::atomic::Ordering::Relaxed) {
                log::info!("Backfill producer task terminated");
                break;
            }
        }
        let cursor_id = cursor
            .as_ref()
            .map(|(project_id, object_type)| BackfillTrackingEntryId {
                project_id,
                object_type: *object_type,
            });
        let tracking_entries = match global_store
            .query_unbackfilled_tracking_entries_ordered(
                has_completed_initial_backfill,
                cursor_id,
                TRACKING_ENTRIES_BATCH_SIZE,
            )
            .await
        {
            Err(e) => {
                log::warn!(
                    "Failed to query unbackfilled tracking entries: {}. Sleeping for {}ms",
                    e,
                    error_sleep_duration.as_millis()
                );
                tokio::time::sleep(error_sleep_duration).await;
                continue;
            }
            Ok(x) => x,
        };
        if tracking_entries.is_empty() {
            log::debug!("Reached the end of the set of un-backfilled tracking entries");
            tick_manager.next_tick().await;
            cursor = None;
            continue;
        }
        cursor = {
            let last_entry = tracking_entries.last().unwrap();
            Some((last_entry.project_id.clone(), last_entry.object_type))
        };
        for tracking_entry in tracking_entries {
            match sender.send(tracking_entry).await {
                Ok(()) => {
                    if let Some(sender) = &testing_producer_signal_produced_entry {
                        let _ = sender.send(()).await;
                    }
                }
                Err(_) => {
                    log::info!("Tracking entry producer channel is closed. Exiting",);
                    return;
                }
            }
        }
    }
}

async fn backfill_worker_task(
    receiver: Option<async_channel::Receiver<BackfillTrackingEntry>>,
    bookkeeper: CurrentlyBackfillingBookkeeper,
    config: &ConfigWithStore,
    process_wal_opts: &ProcessObjectWalOptions,
    compaction_loop: &CompactionLoop,
    options: BackfillOneTrackingEntryOptions,
) {
    let error_sleep_duration = Duration::from_millis(100);

    let receiver = if let Some(r) = receiver {
        r
    } else {
        return;
    };

    loop {
        let tracking_entry = match receiver.recv().await {
            Ok(tracking_entry) => tracking_entry,
            Err(_) => {
                log::info!("Tracking entry receiver channel is closed. Exiting",);
                return;
            }
        };
        let _active_worker_handle = {
            let counter = if tracking_entry.completed_initial_backfill_ts.is_some() {
                BACKFILL_METERS.active_realtime_workers.clone()
            } else {
                BACKFILL_METERS.active_historical_workers.clone()
            };
            UpDownCounterHandle::new(counter)
        };
        let tracking_entry_id_str = tracking_entry.id().to_string();
        let backfill_handle = match bookkeeper
            .obtain_handle(&tracking_entry.project_id, tracking_entry.object_type)
        {
            Some(handle) => handle,
            None => {
                log::debug!(
                    "Skipping tracking entry {} because it is already being backfilled.",
                    tracking_entry_id_str
                );
                continue;
            }
        };
        let res = backfill_one_tracking_entry(
            BackfillOneTrackingEntryInput {
                tracking_entry,
                config,
                process_wal_opts,
                compaction_loop,
            },
            options.clone(),
        )
        .await;
        match res {
            Ok(out) => {
                if out.acquired_lock {
                    // We actually backfilled it, so let the handle drop normally.
                } else {
                    // Failed to acquire the lock, so convert the handle into a
                    // TTL-based one to prevent future consumers from grabbing
                    // it for some time. While we don't know exactly when the
                    // lock will get released, we expect another process is
                    // backfilling this entry right now, and when finished, will
                    // release it's in-process lock. Meaning that process has
                    // has a better chance of re-acquiring the entry because it
                    // should not have any TTL-based lock.
                    backfill_handle.mark_locked();
                }
            }
            Err(err) => {
                log::error!(
                    "Failed to backfill tracking entry {}: {:?}. Sleeping for {}ms",
                    tracking_entry_id_str,
                    err,
                    error_sleep_duration.as_millis()
                );
                tokio::time::sleep(error_sleep_duration).await;
            }
        }
    }
}

// We explicitly limit the frequency that the producer can visit each
// tracking entry with a "tick" period. Without it, we risk tightly looping
// over the same set of entries and fanning them out to all of our worker
// tasks, all except one of them of which will fail to acquire the lock.
// Each time the loop starts, we re-initialize the period to include some
// random jitter.
struct ProducerLoopTickManager {
    tick_period: Duration,
    start_time: Instant,
}

impl ProducerLoopTickManager {
    fn new() -> Self {
        Self {
            tick_period: Self::sample_tick_duration(),
            start_time: Instant::now(),
        }
    }

    async fn next_tick(&mut self) {
        let elapsed = self.start_time.elapsed();
        if elapsed < self.tick_period {
            let remaining = self.tick_period - elapsed;
            log::debug!(
                "Producer sleeping for {}ms for remaining tick period of {}ms",
                remaining.as_millis(),
                self.tick_period.as_millis()
            );
            tokio::time::sleep(remaining).await;
        }
        self.tick_period = Self::sample_tick_duration();
        self.start_time = Instant::now();
    }

    fn sample_tick_duration() -> Duration {
        // Note: if you adjust this, make sure to run a batch CI jobs to ensure
        // it doesn't blow out Redis. Smaller wait times (e.g. <100ms) were
        // evaluated and shown to put too much load on redis, at which point it
        // would start rejecting connections, and we'd drop locks and abort
        // brainstore, etc.
        Duration::from_millis(rand::thread_rng().gen_range(500..=600))
    }
}

// In order to limit how frequently we hit redis to acquire locks for
// backfilling, we track in memory which objects we are backfilling within this
// process and which we've failed to acquire a lock on (which means another
// process is backfilling it). This will let us skip attempting to acquire the
// lock.

#[derive(Clone, Debug, Default)]
struct CurrentlyBackfillingBookkeeper(Arc<std::sync::Mutex<CurrentlyBackfillingBookkeeperInner>>);

#[derive(Debug, Default)]
struct CurrentlyBackfillingBookkeeperInner {
    currently_backfilling: HashMap<(String, ObjectType), Option<Instant>>,
}

#[derive(Debug)]
struct CurrentlyBackfillingEntryHandle {
    key: (String, ObjectType),
    bookkeeper: CurrentlyBackfillingBookkeeper,
    was_released: bool,
}

#[cfg(not(test))]
const BOOKKEEPER_EXPIRY_MIN_MS: u64 = 4000;

#[cfg(not(test))]
const BOOKKEEPER_EXPIRY_MAX_MS: u64 = 6000;

#[cfg(test)]
const BOOKKEEPER_EXPIRY_MIN_MS: u64 = 100;

#[cfg(test)]
const BOOKKEEPER_EXPIRY_MAX_MS: u64 = 200;

impl CurrentlyBackfillingBookkeeper {
    fn new() -> Self {
        Self(Arc::new(std::sync::Mutex::new(
            CurrentlyBackfillingBookkeeperInner::default(),
        )))
    }

    fn obtain_handle(
        &self,
        project_id: &str,
        object_type: ObjectType,
    ) -> Option<CurrentlyBackfillingEntryHandle> {
        use std::collections::hash_map::Entry;
        let mut guard = self.0.lock().unwrap();
        let success = match guard
            .currently_backfilling
            .entry((project_id.to_string(), object_type))
        {
            Entry::Vacant(entry) => {
                entry.insert(None);
                true
            }
            Entry::Occupied(mut entry) => {
                let val = entry.get_mut();
                if let Some(expiry_time) = val {
                    if Instant::now() > *expiry_time {
                        // There was a TTL-based entry but it expired. So clear
                        // the TTL and return a valid handle.
                        *val = None;
                        true
                    } else {
                        false
                    }
                } else {
                    false
                }
            }
        };
        if success {
            Some(CurrentlyBackfillingEntryHandle {
                key: (project_id.to_string(), object_type),
                bookkeeper: self.clone(),
                was_released: false,
            })
        } else {
            None
        }
    }

    fn release_entry(&self, key: &(String, ObjectType)) -> bool {
        return self
            .0
            .lock()
            .unwrap()
            .currently_backfilling
            .remove(key)
            .is_some();
    }

    fn gen_expiry_time() -> Instant {
        Instant::now()
            + Duration::from_millis(
                rand::thread_rng().gen_range(BOOKKEEPER_EXPIRY_MIN_MS..=BOOKKEEPER_EXPIRY_MAX_MS),
            )
    }
}

impl CurrentlyBackfillingEntryHandle {
    fn mark_locked(mut self) {
        let mut guard = self.bookkeeper.0.lock().unwrap();
        let entry = match guard.currently_backfilling.get_mut(&self.key) {
            Some(entry) => entry,
            None => panic!("Unexpected: missing backfill entry for key {:?}", self.key),
        };
        assert!(
            entry.is_none(),
            "Backfill entry handle for key {:?} should not have a TTL",
            self.key
        );
        *entry = Some(CurrentlyBackfillingBookkeeper::gen_expiry_time());
        self.was_released = true;
    }
}

impl Drop for CurrentlyBackfillingEntryHandle {
    fn drop(&mut self) {
        if self.was_released {
            return;
        }
        if !self.bookkeeper.release_entry(&self.key) {
            log::error!("Unexpected: missing backfill entry for key {:?}", self.key);
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::{atomic::AtomicBool, Arc};

    use util::{
        anyhow::Result,
        chrono::Utc,
        system_types::{FullObjectId, FullObjectIdOwned, ObjectId, ObjectIdOwned, ObjectType},
        xact::TransactionId,
    };

    use crate::{
        backfill::{
            backfill_one_tracking_entry, backfill_worker, BackfillOneTrackingEntryInput,
            BackfillOneTrackingEntryOptions, BackfillWorkerInput, BackfillWorkerOptionalInput,
            BackfillWorkerOptions,
        },
        compaction_loop::CompactionLoop,
        config_with_store::{url_to_global_store, ConfigWithStore},
        directory::cached_directory::FileCacheOpts,
        global_store::{BackfillTrackingEntry, TestingOnlyBackfillBrainstoreObjectAtom},
        global_store_test::{get_postgres_global_store_migration, POSTGRES_EXTERNAL_MIGRATION},
        object_and_global_store_wal::ObjectAndGlobalStoreWal,
        process_wal::ProcessObjectWalOptions,
        test_util::{collect_wal_stream, PostgresContainer, TmpDirConfigWithStore},
        wal::{wal_stream, WALScope, Wal},
        wal_entry::WalEntry,
        wal_test_util::insert_object_atoms_into_wal,
    };

    async fn stream_object_wal(
        object_id: FullObjectId<'_>,
        config: &ConfigWithStore,
    ) -> Result<Vec<WalEntry>> {
        let segment_ids = config
            .global_store
            .list_segment_ids(&[object_id], None)
            .await?
            .remove(0);
        let mut wal_entries = Vec::new();
        for segment_id in segment_ids {
            let segment_wal = ObjectAndGlobalStoreWal {
                object_store: config.index.store.clone(),
                global_store: config.global_store.clone(),
                directory: config.index.directory.clone(),
                store_prefix: config.index.prefix.clone(),
                store_type: config.index.store_type,
            };
            let collected_entries = collect_wal_stream(wal_stream(
                segment_wal
                    .wal_metadata_stream(WALScope::Segment(segment_id), Default::default())
                    .await?,
                Default::default(),
            ))
            .await?;
            wal_entries.extend(
                collected_entries
                    .into_iter()
                    .flat_map(|(_, entries)| entries),
            );
        }
        wal_entries.sort_by_key(|entry| (entry._xact_id, entry.id.clone()));
        Ok(wal_entries)
    }

    /// Helper function to test backfill_one_tracking_entry with configurable global store
    async fn test_backfill_one_tracking_entry_helper(
        use_postgres_global_store: bool,
    ) -> Result<()> {
        let mut tmp_dir_config = TmpDirConfigWithStore::new();
        let wal = tmp_dir_config.config.wal.clone();

        // Set up postgres global store if requested
        let _container = if use_postgres_global_store {
            let container = PostgresContainer::new().await;
            container
                .run_migration(POSTGRES_EXTERNAL_MIGRATION)
                .await
                .unwrap();
            container
                .run_migration(&get_postgres_global_store_migration())
                .await
                .unwrap();
            Some(container)
        } else {
            None
        };

        if use_postgres_global_store {
            let global_store = url_to_global_store(
                &_container.as_ref().unwrap().connection_url,
                FileCacheOpts::default(),
                tmp_dir_config.config.locks_manager.clone(),
            )
            .unwrap();
            tmp_dir_config.config.global_store = global_store.0;
        }

        let global_store = tmp_dir_config.config.global_store.clone();
        let compaction_loop = CompactionLoop::default(); // This is not really used

        // Create test data with spaced-out sequence IDs to test batching
        let sequence_id_batch_size = 100;
        let mut tracking_entries: Vec<BackfillTrackingEntry> = Vec::new();
        for object_type in [
            ObjectType::Experiment,
            ObjectType::Dataset,
            ObjectType::PlaygroundLogs,
        ] {
            tracking_entries.push(BackfillTrackingEntry {
                project_id: "test_project".to_string(),
                object_type,
                last_processed_sequence_id: 0,
                last_encountered_sequence_id: 350, // This will require multiple batches
                last_processed_sequence_id_2: 0,
                last_encountered_sequence_id_2: 250,
                completed_initial_backfill_ts: Some(Utc::now()),
            });
        }

        let brainstore_objects = vec![
            // First batch of objects (sequence IDs 0-100)
            TestingOnlyBackfillBrainstoreObjectAtom {
                project_id: "test_project".to_string(),
                object_id: FullObjectIdOwned {
                    object_type: ObjectType::Experiment,
                    object_id: ObjectIdOwned::new("obj1".to_string()).unwrap(),
                },
                is_logs2: false,
                sequence_id: 50,
                xact_id: TransactionId(101),
            },
            TestingOnlyBackfillBrainstoreObjectAtom {
                project_id: "test_project".to_string(),
                object_id: FullObjectIdOwned {
                    object_type: ObjectType::Experiment,
                    object_id: ObjectIdOwned::new("obj1".to_string()).unwrap(),
                },
                is_logs2: false,
                sequence_id: 90,
                xact_id: TransactionId(102),
            },
            // Second batch of objects (sequence IDs 100-200)
            TestingOnlyBackfillBrainstoreObjectAtom {
                project_id: "test_project".to_string(),
                object_id: FullObjectIdOwned {
                    object_type: ObjectType::Dataset,
                    object_id: ObjectIdOwned::new("obj2".to_string()).unwrap(),
                },
                is_logs2: false,
                sequence_id: 150,
                xact_id: TransactionId(103),
            },
            TestingOnlyBackfillBrainstoreObjectAtom {
                project_id: "test_project".to_string(),
                object_id: FullObjectIdOwned {
                    object_type: ObjectType::Dataset,
                    object_id: ObjectIdOwned::new("obj2".to_string()).unwrap(),
                },
                is_logs2: false,
                sequence_id: 180,
                xact_id: TransactionId(104),
            },
            // Third batch of objects (sequence IDs 200-300)
            TestingOnlyBackfillBrainstoreObjectAtom {
                project_id: "test_project".to_string(),
                object_id: FullObjectIdOwned {
                    object_type: ObjectType::PlaygroundLogs,
                    object_id: ObjectIdOwned::new("obj3".to_string()).unwrap(),
                },
                is_logs2: false,
                sequence_id: 250,
                xact_id: TransactionId(105),
            },
            // Fourth batch of objects (sequence IDs 300-400)
            TestingOnlyBackfillBrainstoreObjectAtom {
                project_id: "test_project".to_string(),
                object_id: FullObjectIdOwned {
                    object_type: ObjectType::PlaygroundLogs,
                    object_id: ObjectIdOwned::new("obj4".to_string()).unwrap(),
                },
                is_logs2: false,
                sequence_id: 320,
                xact_id: TransactionId(106),
            },
            // logs2 objects for testing logs2 processing
            TestingOnlyBackfillBrainstoreObjectAtom {
                project_id: "test_project".to_string(),
                object_id: FullObjectIdOwned {
                    object_type: ObjectType::Experiment,
                    object_id: ObjectIdOwned::new("obj5".to_string()).unwrap(),
                },
                is_logs2: true,
                sequence_id: 100,
                xact_id: TransactionId(107),
            },
            TestingOnlyBackfillBrainstoreObjectAtom {
                project_id: "test_project".to_string(),
                object_id: FullObjectIdOwned {
                    object_type: ObjectType::Experiment,
                    object_id: ObjectIdOwned::new("obj5".to_string()).unwrap(),
                },
                is_logs2: true,
                // Use a much larger sequence ID to test that we artificially extend
                // the sequence ID of rows we search for to grab all rows in the
                // transaction. This also requires using the same transaction ID as
                // the previous entry.
                sequence_id: 100 + 10000,
                xact_id: TransactionId(107),
            },
        ];

        // Insert test data
        insert_object_atoms_into_wal(
            brainstore_objects,
            tracking_entries.clone(),
            wal.clone(),
            global_store.clone(),
            false,
        )
        .await?;

        // Test the backfill function
        for tracking_entry in &tracking_entries {
            let options = BackfillOneTrackingEntryOptions {
                sequence_id_batch_size,
                ..Default::default()
            };

            let input = BackfillOneTrackingEntryInput {
                tracking_entry: tracking_entry.clone(),
                config: &tmp_dir_config.config,
                process_wal_opts: &ProcessObjectWalOptions::default(),
                compaction_loop: &compaction_loop,
            };

            let output = backfill_one_tracking_entry(input, options).await?;

            // Check the output fields directly
            assert!(output.acquired_lock, "Should have acquired the lock");
            assert_eq!(
                output.num_iterations, 7,
                "Should have performed exactly 7 iterations: 4 for regular logs (0->350 in batches of 100) + 3 for logs2 (0->250 in batches of 100)"
            );

            // Verify that the tracking entry was updated correctly
            let updated_entry = global_store
                .query_backfill_tracking_entries_by_ids(&[tracking_entry.id()])
                .await?
                .remove(0)
                .unwrap();
            assert_eq!(
                updated_entry.last_processed_sequence_id, 350,
                "Regular logs should be processed up to sequence ID 350"
            );
            assert_eq!(
                updated_entry.last_processed_sequence_id_2, 250,
                "Logs2 should be processed up to sequence ID 250"
            );
        }

        // Check that objects were processed by verifying their WAL entries exist
        // Expected WAL entries per object based on the test data:
        // - obj1: 2 entries (sequence IDs 50, 90)
        // - obj2: 2 entries (sequence IDs 150, 180)
        // - obj3: 1 entry (sequence ID 250)
        // - obj4: 1 entry (sequence ID 320)
        // - obj5: 2 entries (sequence IDs 100, 200 - logs2)
        let expected_wal_counts = vec![
            (
                FullObjectId {
                    object_type: ObjectType::Experiment,
                    object_id: ObjectId::new("obj1").unwrap(),
                },
                2,
            ),
            (
                FullObjectId {
                    object_type: ObjectType::Dataset,
                    object_id: ObjectId::new("obj2").unwrap(),
                },
                2,
            ),
            (
                FullObjectId {
                    object_type: ObjectType::PlaygroundLogs,
                    object_id: ObjectId::new("obj3").unwrap(),
                },
                1,
            ),
            (
                FullObjectId {
                    object_type: ObjectType::PlaygroundLogs,
                    object_id: ObjectId::new("obj4").unwrap(),
                },
                1,
            ),
            (
                FullObjectId {
                    object_type: ObjectType::Experiment,
                    object_id: ObjectId::new("obj5").unwrap(),
                },
                2,
            ),
        ];

        for (full_object_id, expected_count) in expected_wal_counts {
            let wal_entries = stream_object_wal(full_object_id, &tmp_dir_config.config).await?;
            assert_eq!(
                wal_entries.len(),
                expected_count,
                "Object {:?} should have exactly {} WAL entries after processing",
                full_object_id,
                expected_count
            );
        }

        Ok(())
    }

    /// Tests that `backfill_one_tracking_entry` correctly processes tracking entries
    /// and their associated brainstore objects, handling both regular logs and logs2
    /// with appropriate batch processing using the default global store.
    #[tokio::test]
    async fn test_backfill_one_tracking_entry() -> Result<()> {
        test_backfill_one_tracking_entry_helper(false).await
    }

    /// Tests that `backfill_one_tracking_entry` correctly processes tracking entries
    /// and their associated brainstore objects, handling both regular logs and logs2
    /// with appropriate batch processing using a postgres global store.
    #[tokio::test]
    async fn test_backfill_one_tracking_entry_postgres() -> Result<()> {
        test_backfill_one_tracking_entry_helper(true).await
    }

    /// Tests that `backfill_one_tracking_entry` correctly handles the case where
    /// it cannot acquire the required lock (i.e., another process is already
    /// working on the same tracking entry).
    #[tokio::test]
    async fn test_backfill_one_tracking_entry_no_lock() -> Result<()> {
        let tmp_dir_config = TmpDirConfigWithStore::new();
        let global_store = tmp_dir_config.config.global_store.clone();
        let locks_manager = tmp_dir_config.config.locks_manager.clone();
        let compaction_loop = CompactionLoop::default();

        let tracking_entry = BackfillTrackingEntry {
            project_id: "test_project".to_string(),
            object_type: ObjectType::Experiment,
            last_processed_sequence_id: 0,
            last_encountered_sequence_id: 100,
            last_processed_sequence_id_2: 0,
            last_encountered_sequence_id_2: 100,
            completed_initial_backfill_ts: Some(Utc::now()),
        };

        // Insert the tracking entry first
        global_store
            .testing_only_insert_backfill_data(vec![tracking_entry.clone()], vec![], false)
            .await?;

        // Acquire the lock first so the backfill function can't get it
        let _lock = locks_manager
            .write(&format!(
                "backfill_one_tracking_entry:{}",
                tracking_entry.id()
            ))
            .await?;

        let input = BackfillOneTrackingEntryInput {
            tracking_entry: tracking_entry.clone(),
            config: &tmp_dir_config.config,
            process_wal_opts: &ProcessObjectWalOptions::default(),
            compaction_loop: &compaction_loop,
        };

        let output =
            backfill_one_tracking_entry(input, BackfillOneTrackingEntryOptions::default()).await?;

        // Check the output fields directly
        assert!(!output.acquired_lock, "Should not have acquired the lock");
        assert_eq!(
            output.num_iterations, 0,
            "Should have performed zero iterations when lock not acquired - no processing should occur"
        );

        // Also verify by checking that the tracking entry was NOT updated (no processing occurred)
        let entries = global_store
            .query_unbackfilled_tracking_entries_ordered(true, None, 10)
            .await?;

        if !entries.is_empty() {
            let entry = &entries[0];
            // Should still be at the original values since lock couldn't be acquired
            assert_eq!(entry.last_processed_sequence_id, 0);
            assert_eq!(entry.last_processed_sequence_id_2, 0);
            // Also check that encountered values are unchanged
            assert_eq!(entry.last_encountered_sequence_id, 100);
            assert_eq!(entry.last_encountered_sequence_id_2, 100);
        }

        Ok(())
    }

    /// Tests that `backfill_one_tracking_entry` correctly handles processing
    /// with a small batch size, ensuring that it can handle multiple iterations
    /// to process all the data.
    #[tokio::test]
    async fn test_backfill_one_tracking_entry_with_small_batch() -> Result<()> {
        let tmp_dir_config = TmpDirConfigWithStore::new();
        let wal = tmp_dir_config.config.wal.clone();
        let global_store = tmp_dir_config.config.global_store.clone();

        let tracking_entry = BackfillTrackingEntry {
            project_id: "test_project".to_string(),
            object_type: ObjectType::Experiment,
            last_processed_sequence_id: 0,
            last_encountered_sequence_id: 200,
            last_processed_sequence_id_2: 0,
            last_encountered_sequence_id_2: 0,
            completed_initial_backfill_ts: Some(Utc::now()),
        };

        // Create objects that will require multiple small batches
        let brainstore_objects = vec![
            TestingOnlyBackfillBrainstoreObjectAtom {
                project_id: "test_project".to_string(),
                object_id: FullObjectIdOwned {
                    object_type: ObjectType::Experiment,
                    object_id: ObjectIdOwned::new("obj1".to_string()).unwrap(),
                },
                is_logs2: false,
                sequence_id: 10,
                xact_id: TransactionId(201),
            },
            TestingOnlyBackfillBrainstoreObjectAtom {
                project_id: "test_project".to_string(),
                object_id: FullObjectIdOwned {
                    object_type: ObjectType::Experiment,
                    object_id: ObjectIdOwned::new("obj2".to_string()).unwrap(),
                },
                is_logs2: false,
                sequence_id: 60,
                xact_id: TransactionId(202),
            },
            TestingOnlyBackfillBrainstoreObjectAtom {
                project_id: "test_project".to_string(),
                object_id: FullObjectIdOwned {
                    object_type: ObjectType::Experiment,
                    object_id: ObjectIdOwned::new("obj3".to_string()).unwrap(),
                },
                is_logs2: false,
                sequence_id: 110,
                xact_id: TransactionId(203),
            },
            TestingOnlyBackfillBrainstoreObjectAtom {
                project_id: "test_project".to_string(),
                object_id: FullObjectIdOwned {
                    object_type: ObjectType::Experiment,
                    object_id: ObjectIdOwned::new("obj4".to_string()).unwrap(),
                },
                is_logs2: false,
                sequence_id: 160,
                xact_id: TransactionId(204),
            },
        ];

        insert_object_atoms_into_wal(
            brainstore_objects,
            vec![tracking_entry.clone()],
            wal.clone(),
            global_store.clone(),
            false,
        )
        .await?;

        // Use a very small batch size to force more iterations
        let options = BackfillOneTrackingEntryOptions {
            sequence_id_batch_size: 50, // Small batch size
            max_backfill_iterations: 1000,
        };

        let compaction_loop = CompactionLoop::default();
        let input = BackfillOneTrackingEntryInput {
            tracking_entry: tracking_entry.clone(),
            config: &tmp_dir_config.config,
            process_wal_opts: &ProcessObjectWalOptions::default(),
            compaction_loop: &compaction_loop,
        };

        let output = backfill_one_tracking_entry(input, options).await?;

        // Check the output fields directly
        assert!(output.acquired_lock, "Should have acquired the lock");
        assert_eq!(
            output.num_iterations, 4,
            "Should have performed exactly 4 iterations: 4 for regular logs (0->200 in batches of 50), no logs2 processing needed"
        );

        // Verify that processing completed despite small batch size
        let updated_entries = global_store
            .query_backfill_tracking_entries_by_ids(&[tracking_entry.id()])
            .await?;
        assert_eq!(updated_entries.len(), 1);

        let updated_entry = updated_entries[0].as_ref().unwrap();
        assert_eq!(
            updated_entry.last_processed_sequence_id, 200,
            "Should process all sequence IDs even with small batch size"
        );

        // Check that objects were processed by verifying their WAL entries exist
        // Expected WAL entries per object based on the test data:
        // - obj1: 1 entry (sequence ID 10)
        // - obj2: 1 entry (sequence ID 60)
        // - obj3: 1 entry (sequence ID 110)
        // - obj4: 1 entry (sequence ID 160)
        let expected_wal_counts = vec![
            (ObjectId::new("obj1").unwrap(), 1),
            (ObjectId::new("obj2").unwrap(), 1),
            (ObjectId::new("obj3").unwrap(), 1),
            (ObjectId::new("obj4").unwrap(), 1),
        ];

        for (object_id, expected_count) in expected_wal_counts {
            let full_object_id = FullObjectId {
                object_type: ObjectType::Experiment,
                object_id,
            };
            let wal_entries = stream_object_wal(full_object_id, &tmp_dir_config.config).await?;
            assert_eq!(
                wal_entries.len(),
                expected_count,
                "Object {:?} should have exactly {} WAL entries after processing",
                object_id,
                expected_count
            );
        }

        Ok(())
    }

    /// Tests that the `backfill_worker` correctly processes un-backfilled tracking
    /// entries. This is a helper function parameterized over whether or not we are
    /// working on backfilled or un-backfilled entries.
    ///
    /// This test verifies that the backfill worker:
    /// 1. Continuously polls for un-backfilled tracking entries
    /// 2. Processes them using the configured number of worker tasks
    /// 3. Updates tracking entries with correct sequence IDs after processing
    /// 4. Terminates gracefully when the terminate signal is set
    /// 5. Processes both regular logs and logs2 entries
    /// 6. Creates WAL entries for all processed objects
    ///
    /// The test flow:
    /// 1. Sets up test data with tracking entries that have work to do
    /// 2. Launches the backfill worker with a terminate signal
    /// 3. Allows the worker time to process the data
    /// 4. Sets the terminate signal to stop the worker
    /// 5. Verifies that all expected processing occurred
    async fn test_backfill_worker_helper(has_completed_initial_backfill: bool) -> Result<()> {
        let tmp_dir_config = TmpDirConfigWithStore::new();
        let wal = tmp_dir_config.config.wal.clone();
        let global_store = tmp_dir_config.config.global_store.clone();
        let completed_initial_backfill_ts = if has_completed_initial_backfill {
            Some(Utc::now())
        } else {
            None
        };

        // Create test data with un-backfilled tracking entries
        let tracking_entry = BackfillTrackingEntry {
            project_id: "test_project".to_string(),
            object_type: ObjectType::Experiment,
            last_processed_sequence_id: 0,
            last_encountered_sequence_id: 100, // Has work to do
            last_processed_sequence_id_2: 0,
            last_encountered_sequence_id_2: 50, // Has work to do
            completed_initial_backfill_ts,
        };

        let brainstore_objects = vec![
            TestingOnlyBackfillBrainstoreObjectAtom {
                project_id: "test_project".to_string(),
                object_id: FullObjectIdOwned {
                    object_type: ObjectType::Experiment,
                    object_id: ObjectIdOwned::new("worker_obj1".to_string()).unwrap(),
                },
                is_logs2: false,
                sequence_id: 50,
                xact_id: TransactionId(301),
            },
            TestingOnlyBackfillBrainstoreObjectAtom {
                project_id: "test_project".to_string(),
                object_id: FullObjectIdOwned {
                    object_type: ObjectType::Experiment,
                    object_id: ObjectIdOwned::new("worker_obj2".to_string()).unwrap(),
                },
                is_logs2: true,
                sequence_id: 25,
                xact_id: TransactionId(302),
            },
        ];

        // Insert test data
        insert_object_atoms_into_wal(
            brainstore_objects,
            vec![tracking_entry.clone()],
            wal.clone(),
            global_store.clone(),
            false,
        )
        .await?;

        // Verify initial state - tracking entry should have work to do
        let initial_entries = global_store
            .query_unbackfilled_tracking_entries_ordered(has_completed_initial_backfill, None, 10)
            .await?;
        assert_eq!(initial_entries.len(), 1);
        let initial_entry = &initial_entries[0];
        assert_eq!(initial_entry.last_processed_sequence_id, 0);
        assert_eq!(initial_entry.last_processed_sequence_id_2, 0);

        // Set up terminate signal for controlling the worker and channel for
        // learning about worker progress
        let terminate_signal = Arc::new(AtomicBool::new(false));
        let (testing_signal_produced_entry_sender, testing_signal_produced_entry_receiver) =
            async_channel::unbounded::<()>();

        // Clone the config to avoid lifetime issues
        let config = tmp_dir_config.config.clone();
        let process_wal_opts = ProcessObjectWalOptions::default();
        let terminate_signal_clone = terminate_signal.clone();

        let compaction_loop = CompactionLoop::default();

        // Start the worker in a separate task
        let compaction_loop_clone = compaction_loop.clone();
        let worker_handle = tokio::spawn(async move {
            let worker_input = BackfillWorkerInput {
                config,
                process_wal_opts: process_wal_opts.clone(),
                compaction_loop: compaction_loop_clone,
            };

            let worker_optional_input = BackfillWorkerOptionalInput {
                terminate_signal: Some(terminate_signal_clone),
                testing_signal_produced_entry: Some(testing_signal_produced_entry_sender),
            };

            let worker_options = BackfillWorkerOptions {
                num_backfill_workers_realtime: if has_completed_initial_backfill { 1 } else { 0 },
                num_backfill_workers_historical: if has_completed_initial_backfill { 0 } else { 1 },
                backfill_one_tracking_entry_opts: BackfillOneTrackingEntryOptions::default(),
            };

            backfill_worker(worker_input, worker_optional_input, worker_options).await;
        });

        // Wait until we've received the testing signal
        testing_signal_produced_entry_receiver.recv().await.unwrap();

        // Set terminate signal to stop the worker
        terminate_signal.store(true, std::sync::atomic::Ordering::Relaxed);

        // Wait for the worker to finish
        worker_handle.await?;

        // Verify that backfilling occurred
        let final_entry = global_store
            .query_backfill_tracking_entries_by_ids(&[tracking_entry.id()])
            .await?
            .remove(0)
            .unwrap();
        assert_eq!(
            final_entry.last_processed_sequence_id, 100,
            "Regular logs should be processed up to sequence ID 100"
        );
        assert_eq!(
            final_entry.last_processed_sequence_id_2, 50,
            "Logs2 should be processed up to sequence ID 50"
        );

        // Verify that objects were processed by checking WAL entries
        let expected_objects = vec![
            (ObjectId::new("worker_obj1").unwrap(), 1),
            (ObjectId::new("worker_obj2").unwrap(), 1),
        ];

        for (object_id, expected_count) in expected_objects {
            let full_object_id = FullObjectId {
                object_type: ObjectType::Experiment,
                object_id,
            };
            let wal_entries = stream_object_wal(full_object_id, &tmp_dir_config.config).await?;
            assert_eq!(
                wal_entries.len(),
                expected_count,
                "Object {:?} should have exactly {} WAL entries after worker processing",
                object_id,
                expected_count
            );
        }

        // Verify that some segment ids were compacted
        compaction_loop
            .pop_front()
            .expect("Compaction queue should not be empty");

        Ok(())
    }

    #[tokio::test]
    async fn test_backfill_worker_completed_initial_backfill() -> Result<()> {
        test_backfill_worker_helper(true).await
    }

    #[tokio::test]
    async fn test_backfill_worker_no_completed_initial_backfill() -> Result<()> {
        test_backfill_worker_helper(false).await
    }

    #[test]
    fn test_bookkeeper_obtain_handle_success() {
        let bookkeeper = CurrentlyBackfillingBookkeeper::new();
        let project_id = "test_project";
        let object_type = ObjectType::Experiment;

        // Should be able to obtain handle for new entry
        let handle = bookkeeper
            .obtain_handle(project_id, object_type)
            .expect("Should be able to obtain handle for new entry");

        assert_eq!(handle.key.0, project_id);
        assert_eq!(handle.key.1, object_type);
        assert!(!handle.was_released);
    }

    #[test]
    fn test_bookkeeper_obtain_handle_blocked() {
        let bookkeeper = CurrentlyBackfillingBookkeeper::new();
        let project_id = "test_project";
        let object_type = ObjectType::Experiment;

        // Obtain first handle
        let handle1 = bookkeeper
            .obtain_handle(project_id, object_type)
            .expect("Should be able to obtain first handle");

        // Second attempt should be blocked
        let handle2 = bookkeeper.obtain_handle(project_id, object_type);
        assert!(handle2.is_none(), "Second handle should be blocked");

        // Clean up first handle
        drop(handle1);

        // Now should be able to obtain handle again
        bookkeeper
            .obtain_handle(project_id, object_type)
            .expect("Should be able to obtain handle after release");
    }

    #[test]
    fn test_bookkeeper_different_keys() {
        let bookkeeper = CurrentlyBackfillingBookkeeper::new();
        let project_id1 = "test_project1";
        let project_id2 = "test_project2";
        let object_type = ObjectType::Experiment;

        // Should be able to obtain handles for different project IDs
        bookkeeper
            .obtain_handle(project_id1, object_type)
            .expect("Should be able to obtain handle for project1");

        bookkeeper
            .obtain_handle(project_id2, object_type)
            .expect("Should be able to obtain handle for project2");

        // Should be able to obtain handles for different object types
        bookkeeper
            .obtain_handle(project_id1, ObjectType::Dataset)
            .expect("Should be able to obtain handle for different object type");
    }

    #[test]
    fn test_bookkeeper_mark_locked_and_expiry() {
        let bookkeeper = CurrentlyBackfillingBookkeeper::new();
        let project_id = "test_project";
        let object_type = ObjectType::Experiment;

        // Obtain handle and mark it as locked
        let handle = bookkeeper
            .obtain_handle(project_id, object_type)
            .expect("Should be able to obtain handle");

        handle.mark_locked();

        // Should not be able to obtain handle immediately after marking as locked
        let handle2 = bookkeeper.obtain_handle(project_id, object_type);
        assert!(
            handle2.is_none(),
            "Should not be able to obtain handle when locked"
        );

        // Wait for expiry (we need to wait longer than the TTL)
        std::thread::sleep(std::time::Duration::from_millis(
            BOOKKEEPER_EXPIRY_MAX_MS + 100,
        ));

        // Should be able to obtain handle after expiry
        bookkeeper
            .obtain_handle(project_id, object_type)
            .expect("Should be able to obtain handle after expiry");
    }

    #[test]
    fn test_bookkeeper_release_entry() {
        let bookkeeper = CurrentlyBackfillingBookkeeper::new();
        let project_id = "test_project";
        let object_type = ObjectType::Experiment;

        // Obtain handle
        let handle = bookkeeper
            .obtain_handle(project_id, object_type)
            .expect("Should be able to obtain handle");

        let key = handle.key.clone();

        // Release should return true for existing entry
        drop(handle);

        // Releasing non-existent entry should return false
        let released = bookkeeper.release_entry(&key);
        assert!(!released, "Should return false for non-existent entry");
    }

    #[test]
    fn test_bookkeeper_handle_proper_cleanup() {
        let bookkeeper = CurrentlyBackfillingBookkeeper::new();
        let project_id = "test_project";
        let object_type = ObjectType::Experiment;

        {
            // Obtain handle and properly release it
            bookkeeper
                .obtain_handle(project_id, object_type)
                .expect("Should be able to obtain handle");
        } // Handle should drop without panic

        // Should be able to obtain handle again
        bookkeeper
            .obtain_handle(project_id, object_type)
            .expect("Should be able to obtain handle after proper cleanup");
    }

    #[test]
    fn test_bookkeeper_expiry_time_generation() {
        let start_time = Instant::now();
        let expiry_time = CurrentlyBackfillingBookkeeper::gen_expiry_time();
        let end_time = Instant::now();

        // Expiry time should be between 4-5 seconds from now
        let min_expected = start_time + Duration::from_millis(BOOKKEEPER_EXPIRY_MIN_MS);
        let max_expected = end_time + Duration::from_millis(BOOKKEEPER_EXPIRY_MAX_MS);

        assert!(
            expiry_time >= min_expected,
            "Expiry time should be at least 4 seconds from start"
        );
        assert!(
            expiry_time <= max_expected,
            "Expiry time should be at most 5 seconds from end"
        );
    }
}
