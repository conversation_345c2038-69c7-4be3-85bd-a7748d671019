use std::path::PathBuf;

use serde::{Deserialize, Serialize};

/// A reference to a data blob in the (realtime) object WAL.
/// Keep this in sync with the RowRef type in local/js/api-schema/insert_schemas.ts.
#[derive(Serialize, Deserialize, Debug, Clone)]
pub(crate) struct RowRef {
    pub key: PathBuf,
    pub byte_range_start: u64,
    pub byte_range_end: u64,
    // Optional metadata properties.
    pub key_file_len: Option<usize>,
}
