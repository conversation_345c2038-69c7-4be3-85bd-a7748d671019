use async_trait::async_trait;
use bytes::Bytes;
use futures::future::join_all;
use object_store::ObjectStore;
use otel_common::opentelemetry::metrics::Counter;
use otel_common::opentelemetry::KeyValue;
use std::collections::HashMap;
use std::io::{Cursor, Write};
use std::path::{Path, PathBuf};
use std::sync::Arc;
use tantivy::common::OwnedBytes;
use tracing::{instrument, Instrument};
use util::sha2::{Digest, Sha256};
use util::url_util::ObjectStoreType;

use async_stream::stream;
use futures::{stream::BoxStream, StreamExt};
use util::{
    anyhow::{self, anyhow, Context, Result},
    itertools::Itertools,
    serde_json::{self, Value},
    uuid::Uuid,
    xact::TransactionId,
};

use crate::directory::async_directory::AsyncDirectory;
use crate::directory::AsyncDirectoryArc;
use crate::global_store::{
    compute_next_wal_entry_cursor, GlobalStore, SegmentWalEntriesCursor, SegmentWalEntry,
    UpsertSegmentWalEntry,
};
use crate::healthcheck_util::validate_object_store_connection;
use crate::paths::make_segment_directory_path;
use crate::wal::{
    DeleteFromWalStats, DeleteUpToXactIdInput, DeleteUpToXactIdOptions, WALScope, Wal,
    WalEntryBytesRef, WalEntryRawBytes, WalEntryVariant, WalEntryVariantPreference, WalMetadata,
    WalMetadataStreamOptionalInput,
};
use crate::wal_entry::{WalEntry, WalEntrySystemFields};

const LIST_BYTE_RANGES_BATCH_SIZE: i64 = 1000;

struct ObjectAndGlobalStoreWalMetrics {
    rows_inserted: Counter<u64>,
    bytes_copied: Counter<u64>,
    bytes_ref_rows_ineligible: Counter<u64>,
}

impl ObjectAndGlobalStoreWalMetrics {
    pub fn new() -> Self {
        let meter = otel_common::opentelemetry::global::meter("brainstore");
        Self {
            rows_inserted: meter
                .u64_counter("brainstore.storage.object_and_global_store_wal.rows_inserted")
                .build(),
            bytes_copied: meter
                .u64_counter("brainstore.storage.object_and_global_store_wal.bytes_copied")
                .build(),
            bytes_ref_rows_ineligible: meter
                .u64_counter(
                    "brainstore.storage.object_and_global_store_wal.bytes_ref_rows_ineligible",
                )
                .build(),
        }
    }
}

lazy_static::lazy_static! {
    static ref OBJECT_AND_GLOBAL_STORE_WAL_METRICS: ObjectAndGlobalStoreWalMetrics = ObjectAndGlobalStoreWalMetrics::new();
}

#[derive(Debug, Clone)]
pub struct ObjectAndGlobalStoreWal {
    // The store and directory should be pointing to the same filesystem location.
    pub object_store: Arc<dyn ObjectStore>,
    pub global_store: Arc<dyn GlobalStore>,
    pub directory: AsyncDirectoryArc,
    pub store_prefix: PathBuf,
    pub store_type: ObjectStoreType,
}

pub struct ObjectAndGlobalStoreWalMetadata {
    pub xact_id: TransactionId,
    pub wal_filename: Uuid,
    pub byte_range_start: usize,
    pub byte_range_end: usize,
    pub wal_directory: PathBuf,

    directory: AsyncDirectoryArc,
}

#[derive(Debug, Clone, Default)]
pub struct ObjectAndGlobalStoreWalInsertOptionalInput {
    pub is_compacted: bool,
    pub testing_propagate_file_not_found_errors: bool,
}

#[async_trait]
impl WalMetadata for ObjectAndGlobalStoreWalMetadata {
    fn xact_id(&self) -> TransactionId {
        self.xact_id
    }

    fn num_bytes(&self) -> usize {
        self.byte_range_end - self.byte_range_start
    }

    async fn read_wal_entries(&self, _: WalEntryVariantPreference) -> Result<Vec<WalEntryVariant>> {
        let mut entries = Vec::new();
        let bytes = ObjectAndGlobalStoreWal::read_byte_range(
            self.directory.as_ref(),
            self.wal_directory.as_path(),
            self.wal_filename,
            self.byte_range_start,
            self.byte_range_end,
        )
        .await?;
        let deserializer = {
            let mut de = serde_json::Deserializer::from_slice(&bytes);
            // WAL entries can overflow the default serde recursion limit.
            de.disable_recursion_limit();
            de.into_iter::<Value>()
        };
        for (position_number, value) in deserializer.enumerate() {
            let entry = value
                .map_err(|e| -> anyhow::Error { e.into() })
                .and_then(WalEntry::new)
                .with_context(|| {
                    format!(
                        "Failed to parse JSON value at position {}. xact_id: {} wal_filename: {} byte_range_start: {}. byte_range_end: {}",
                        position_number + 1,
                        self.xact_id,
                        self.wal_filename,
                        self.byte_range_start,
                        self.byte_range_end
                    )
                })?;
            entries.push(WalEntryVariant::Full(entry));
        }
        Ok(entries)
    }
}

impl ObjectAndGlobalStoreWal {
    pub fn wal_directory(&self, scope: WALScope) -> PathBuf {
        match scope {
            WALScope::ObjectId(_, _) => {
                panic!("Object WAL not supported for ObjectAndGlobalStoreWal");
            }
            WALScope::Segment(segment_id) => {
                make_segment_directory_path(&self.store_prefix, segment_id)
                    .join("object-global-store-wal")
            }
        }
    }

    /// Callers that specifically utilize ObjectAndGlobalStoreWal can extra metadata about the WAL
    /// entries by using this method.
    #[instrument(err, skip(self))]
    pub async fn wal_metadata_stream_concrete<'a>(
        &self,
        scope: WALScope<'a>,
        optional_input: WalMetadataStreamOptionalInput,
    ) -> Result<BoxStream<'static, Result<ObjectAndGlobalStoreWalMetadata>>> {
        let segment_id = get_wal_scope_segment_id(&scope);
        let wal_directory = self.wal_directory(scope);
        let global_store = self.global_store.clone();
        // Note: we don't use is_compacted_filter here because we generally want to stream ALL wal
        // entries starting from a particular xact_id in compaction. Skipping already-compacted wal
        // entries beyond the start_xact_id is incorrect for compaction.
        let is_compacted_filter = None;
        let directory = self.directory.clone();
        let ret = stream! {
            let mut total_num_byte_ranges = 0;
            let mut next_page_fut: Option<tokio::task::JoinHandle<Result<Vec<SegmentWalEntry>>>> = Some({
              let global_store = global_store.clone();
              tokio::spawn(async move {
                      global_store.query_segment_wal_entries_batch(
                          segment_id,
                          optional_input.start_xact_id.map(SegmentWalEntriesCursor::XactIdGe),
                          Some(LIST_BYTE_RANGES_BATCH_SIZE),
                          is_compacted_filter,
                      ).await
                  }.instrument(tracing::Span::current())
              )
            });
            'outer: loop {
                let page = match next_page_fut.take() {
                    Some(fut) => fut.await??,
                    None => break,
                };
                let next_page_cursor = compute_next_wal_entry_cursor(&page);
                next_page_fut = next_page_cursor.map(|cursor| {
                    let global_store = global_store.clone();
                    tokio::spawn(async move {
                          global_store.query_segment_wal_entries_batch(
                              segment_id,
                              Some(cursor),
                              Some(LIST_BYTE_RANGES_BATCH_SIZE),
                              is_compacted_filter).await
                    }.instrument(tracing::Span::current()))
                });
                for entry in page {
                    if let Some(end_xact_id) = optional_input.end_xact_id {
                        if entry.xact_id > end_xact_id {
                            break 'outer;
                        }
                    }
                    total_num_byte_ranges += 1;
                    yield Ok(ObjectAndGlobalStoreWalMetadata {
                        xact_id: entry.xact_id,
                        wal_filename: entry.wal_filename,
                        byte_range_start: entry.byte_range_start,
                        byte_range_end: entry.byte_range_end,
                        wal_directory: wal_directory.clone(),
                        directory: directory.clone(),
                    });
                }
            }
            tracing::debug!(num_byte_ranges = total_num_byte_ranges, "Read WAL byte ranges");
        }.instrument(tracing::info_span!("list WAL byte ranges")).boxed();
        Ok(ret)
    }

    #[instrument(err, skip(self, wal_entries), fields(num_entries = wal_entries.len()))]
    pub async fn insert_no_commit_metadata<'a>(
        &self,
        scope: WALScope<'a>,
        wal_entries: Vec<WalEntryVariant>,
        optional_input: ObjectAndGlobalStoreWalInsertOptionalInput,
    ) -> Result<HashMap<Uuid, HashMap<Uuid, Vec<UpsertSegmentWalEntry>>>> {
        if wal_entries.is_empty() {
            return Ok(HashMap::new());
        }
        let segment_id = get_wal_scope_segment_id(&scope);
        let (copied_wal_metadatas, wal_entries) = self
            .handle_bytes_ref_wal_entries(
                scope,
                wal_entries,
                optional_input.is_compacted,
                optional_input.testing_propagate_file_not_found_errors,
            )
            .await?;
        if wal_entries.is_empty() {
            return Ok([(segment_id, copied_wal_metadatas)].into_iter().collect());
        }

        let mut xact_id_to_entries: Vec<(TransactionId, Vec<WalEntryVariant>)> = wal_entries
            .into_iter()
            .into_group_map_by(|entry| entry.xact_id())
            .into_iter()
            .collect();
        xact_id_to_entries.sort_by_key(|(xact_id, _)| *xact_id);

        // Convert the WalEntryVariants into serializable entries.
        let xact_id_to_serializable_entries = async move {
            join_all(
                xact_id_to_entries
                    .into_iter()
                    .map(|(xact_id, entries)| async move {
                        let serializable_entries =
                            join_all(entries.into_iter().map(|entry| async move {
                                match entry {
                                    WalEntryVariant::Full(entry) => {
                                        OBJECT_AND_GLOBAL_STORE_WAL_METRICS
                                            .rows_inserted
                                            .add(1, &[KeyValue::new("wal_entry_variant", "full")]);
                                        Ok(SerializableWalEntry::Value(entry))
                                    }
                                    WalEntryVariant::SystemFields { raw_bytes, .. } => {
                                        let owned_bytes = raw_bytes.to_owned_bytes().await?;
                                        OBJECT_AND_GLOBAL_STORE_WAL_METRICS
                                            .rows_inserted
                                            .add(1, &[KeyValue::new("wal_entry_variant", "bytes")]);
                                        Ok(SerializableWalEntry::RawBytes(owned_bytes))
                                    }
                                }
                            }))
                            .await
                            .into_iter()
                            .collect::<Result<Vec<SerializableWalEntry>>>()?;
                        Ok((xact_id, serializable_entries))
                    }),
            )
            .await
            .into_iter()
            .collect::<Result<Vec<_>>>()
        }
        .instrument(tracing::info_span!("convert to serializable entries"))
        .await?;

        // Serialize each group of entries into a single byte buffer, and track the byte ranges
        // compromising each group.
        let mut buffer: Vec<u8> = Vec::new();
        let mut xact_byte_ranges: Vec<(TransactionId, usize, usize)> = Vec::new();
        {
            let _span = tracing::info_span!("serializing wal entries").entered();
            let mut cursor = Cursor::new(&mut buffer);
            for (xact_id, entries) in xact_id_to_serializable_entries {
                let start_pos = cursor.position() as usize;
                assert!(!entries.is_empty());
                for entry in entries {
                    match entry {
                        SerializableWalEntry::Value(entry) => {
                            serde_json::to_writer(&mut cursor, &entry.to_value())?;
                        }
                        SerializableWalEntry::RawBytes(owned_bytes) => {
                            cursor.write_all(owned_bytes.as_slice())?;
                        }
                    }
                    cursor.write_all(b"\n")?;
                }
                let end_pos = cursor.position() as usize;
                xact_byte_ranges.push((xact_id, start_pos, end_pos));
            }
        }

        // Write to the directory.
        let wal_filename = Uuid::new_v4();
        let fullpath = self.wal_directory(scope).join(wal_filename.to_string());
        {
            self.directory
                .async_atomic_write(&fullpath, &buffer)
                .instrument(tracing::info_span!(
                    "write WAL file",
                    bytes_written = buffer.len(),
                ))
                .await?;
        }

        // Collect the byte ranges to the global store.
        let upsert_segment_wal_entries = xact_byte_ranges
            .into_iter()
            .map(
                |(xact_id, byte_range_start, byte_range_end)| UpsertSegmentWalEntry {
                    is_compacted: optional_input.is_compacted,
                    xact_id,
                    byte_range_start,
                    byte_range_end,
                    digest: Some(compute_wal_entry_digest(
                        &buffer[byte_range_start..byte_range_end],
                    )),
                },
            )
            .collect::<Vec<_>>();

        let mut wal_filename_to_metadatas = HashMap::new();
        wal_filename_to_metadatas.insert(wal_filename, upsert_segment_wal_entries);
        wal_filename_to_metadatas.extend(copied_wal_metadatas);
        Ok([(segment_id, wal_filename_to_metadatas)]
            .into_iter()
            .collect())
    }

    #[instrument(err, skip(self, wal_entries), fields(num_entries = wal_entries.len()))]
    pub async fn insert_full<'a>(
        &self,
        scope: WALScope<'a>,
        wal_entries: Vec<WalEntryVariant>,
        is_compacted: bool,
    ) -> Result<()> {
        let upsert_metadata = self
            .insert_no_commit_metadata(
                scope,
                wal_entries,
                ObjectAndGlobalStoreWalInsertOptionalInput {
                    is_compacted,
                    ..Default::default()
                },
            )
            .await?;
        if upsert_metadata.is_empty() {
            return Ok(());
        }
        // Commit the WAL entries to the global store.
        self.global_store
            .upsert_segment_wal_entries(upsert_metadata)
            .await?;
        Ok(())
    }

    #[instrument(err, level = "debug", skip(directory))]
    async fn read_byte_range(
        directory: &dyn AsyncDirectory,
        wal_directory: &Path,
        wal_filename: Uuid,
        byte_range_start: usize,
        byte_range_end: usize,
    ) -> Result<Bytes> {
        let path = wal_directory.join(wal_filename.to_string());
        let file_handle = directory.async_get_file_handle(&path, None).await?;
        let read_bytes = file_handle
            .async_read_bytes(byte_range_start..byte_range_end)
            .await?;
        Ok::<_, anyhow::Error>(read_bytes.to_vec().into())
    }

    // Implement a sever-side-copy optimization for BytesRef-type WalEntries. If the total size of
    // the collected entries exceeds a certain minimum threshold, then we copy the key file into
    // the segment WAL, rather than batching it with the others.
    //
    // We return the remaining WalEntries that we still have to batch+serialize, and the additional
    // WAL entry metadatas we created from copying key files.
    #[instrument(err, skip(wal_entries), fields(num_entries = wal_entries.len()))]
    async fn handle_bytes_ref_wal_entries(
        &self,
        scope: WALScope<'_>,
        wal_entries: Vec<WalEntryVariant>,
        is_compacted: bool,
        testing_propagate_file_not_found_errors: bool,
    ) -> Result<(
        HashMap<Uuid, Vec<UpsertSegmentWalEntry>>,
        Vec<WalEntryVariant>,
    )> {
        let mut remaining_wal_entries: Vec<WalEntryVariant> = Vec::new();
        let mut key_to_bytes_ref_wal_entries: HashMap<
            PathBuf,
            Vec<(WalEntrySystemFields, WalEntryBytesRef)>,
        > = HashMap::new();
        for wal_entry in wal_entries {
            match wal_entry {
                WalEntryVariant::Full(entry) => {
                    remaining_wal_entries.push(WalEntryVariant::Full(entry));
                }
                WalEntryVariant::SystemFields {
                    system_fields,
                    raw_bytes,
                } => match raw_bytes {
                    WalEntryRawBytes::Bytes(bytes) => {
                        remaining_wal_entries.push(WalEntryVariant::SystemFields {
                            system_fields,
                            raw_bytes: WalEntryRawBytes::Bytes(bytes),
                        });
                    }
                    WalEntryRawBytes::BytesRef(bytes_ref) => {
                        key_to_bytes_ref_wal_entries
                            .entry(bytes_ref.key.clone())
                            .or_default()
                            .push((system_fields, bytes_ref));
                    }
                },
            }
        }

        if key_to_bytes_ref_wal_entries.is_empty() {
            return Ok((HashMap::new(), remaining_wal_entries));
        }

        // Check the key_to_bytes_ref_wal_entries for consistency and completeness. If not
        // eligible, we move those entries back into `remaining_wal_entries`.
        let mut optimizable_keys: Vec<OptimizableKey> = Vec::new();
        for (key, wal_entries) in key_to_bytes_ref_wal_entries {
            if wal_entries.is_empty() {
                continue;
            }
            // Make sure the wal entries are eligible for the optimization.
            let ineligible_reason = (|| {
                let key_file_len = wal_entries[0].1.key_file_len;
                if !wal_entries
                    .iter()
                    .all(|(_, bytes_ref)| bytes_ref.key_file_len == key_file_len)
                {
                    return Some("inconsistent_key_metadata");
                }
                None
            })();
            if let Some(reason) = ineligible_reason {
                OBJECT_AND_GLOBAL_STORE_WAL_METRICS
                    .bytes_ref_rows_ineligible
                    .add(wal_entries.len() as u64, &[KeyValue::new("reason", reason)]);
                remaining_wal_entries.extend(wal_entries.into_iter().map(
                    |(system_fields, bytes_ref)| WalEntryVariant::SystemFields {
                        system_fields,
                        raw_bytes: WalEntryRawBytes::BytesRef(bytes_ref),
                    },
                ));
            } else {
                optimizable_keys.push(OptimizableKey { key, wal_entries });
            }
        }

        // In parallel, we can copy the optimizable key files over to the segment WAL. We group the
        // WalEntries by contiguous byte ranges and generate an UpsertSegmentWalEntry for each one.
        let num_optimizable_keys = optimizable_keys.len();
        if num_optimizable_keys == 0 {
            return Ok((HashMap::new(), remaining_wal_entries));
        }
        let num_optimizable_wal_entries = optimizable_keys
            .iter()
            .map(|k| k.wal_entries.len())
            .sum::<usize>();
        let segment_id = get_wal_scope_segment_id(&scope);
        let nested_optimization_results = join_all(optimizable_keys.into_iter().map(
            |optimizable_key| async move {
                let metadata_entries = construct_upsert_segment_wal_entries(
                    optimizable_key.wal_entries.iter().collect(),
                    segment_id,
                    is_compacted,
                );
                let object_store_key = object_store::path::Path::from(
                    optimizable_key
                        .key
                        .into_os_string()
                        .into_string()
                        .map_err(|_| anyhow!("Failed to convert key path to string"))?,
                );
                let copy_results = {
                    let key_file_len = optimizable_key.wal_entries[0].1.key_file_len;
                    let object_store_key = &object_store_key;
                    join_all(metadata_entries.into_iter().map(
                        |(num_entries, metadata)| async move {
                            // We need a separate WAL filename per metadata entry, since each metadata entry
                            // covers one xact_id, and we cannot have multiple metadata entries for the same
                            // (segment_id, xact_id, wal_filename).
                            let wal_filename = Uuid::new_v4();
                            let wal_fullpath =
                                self.wal_directory(scope).join(wal_filename.to_string());
                            let object_store_wal_fullpath = object_store::path::Path::from(
                                wal_fullpath
                                    .into_os_string()
                                    .into_string()
                                    .map_err(|_| anyhow!("Failed to convert fullpath to string"))?,
                            );
                            let copy_res = self
                                .object_store
                                .copy(object_store_key, &object_store_wal_fullpath)
                                .await;
                            match copy_res {
                                Ok(()) => (),
                                Err(e) => match &e {
                                    object_store::Error::NotFound { path, .. } => {
                                        if testing_propagate_file_not_found_errors {
                                            return Err(e.into());
                                        } else {
                                            tracing::warn!(
                                                xact_id = ?metadata.xact_id,
                                                path = path,
                                                num_entries = num_entries,
                                                "Skipping rows because WAL file is missing",
                                            );
                                            OBJECT_AND_GLOBAL_STORE_WAL_METRICS
                                                .bytes_ref_rows_ineligible
                                                .add(
                                                    num_entries as u64,
                                                    &[KeyValue::new("reason", "file_not_found")],
                                                );
                                            return Ok(None);
                                        }
                                    }
                                    _ => return Err(e.into()),
                                },
                            }
                            OBJECT_AND_GLOBAL_STORE_WAL_METRICS
                                .bytes_copied
                                .add(key_file_len as u64, &[]);
                            Ok::<_, util::anyhow::Error>(Some((
                                wal_filename,
                                num_entries,
                                metadata,
                            )))
                        },
                    ))
                    .await
                    .into_iter()
                    .filter_map(|r| r.transpose())
                    .collect::<Result<Vec<_>>>()
                }?;
                OBJECT_AND_GLOBAL_STORE_WAL_METRICS.rows_inserted.add(
                    copy_results
                        .iter()
                        .map(|(_, num_entries, _)| *num_entries as u64)
                        .sum(),
                    &[KeyValue::new("wal_entry_variant", "bytes_ref")],
                );
                let mut wal_filenames = Vec::with_capacity(copy_results.len());
                let mut metadata_entries = Vec::with_capacity(copy_results.len());
                for (wal_filename, _, metadata) in copy_results {
                    wal_filenames.push(wal_filename);
                    metadata_entries.push(metadata);
                }
                Ok::<_, util::anyhow::Error>((wal_filenames, metadata_entries))
            },
        ))
        .instrument(tracing::info_span!(
            "copy optimizable key files",
            num_files = num_optimizable_keys,
            num_wal_entries = num_optimizable_wal_entries
        ))
        .await
        .into_iter()
        .collect::<Result<Vec<_>>>()?;
        let mut optimization_results_map = HashMap::new();
        for (wal_filenames, metadata_entries) in nested_optimization_results {
            for (wal_filename, metadata_entry) in wal_filenames.into_iter().zip(metadata_entries) {
                optimization_results_map.insert(wal_filename, vec![metadata_entry]);
            }
        }
        Ok((optimization_results_map, remaining_wal_entries))
    }
}

#[async_trait]
impl Wal for ObjectAndGlobalStoreWal {
    async fn insert<'a>(&self, scope: WALScope<'a>, wal_entries: Vec<WalEntry>) -> Result<()> {
        self.insert_full(
            scope,
            wal_entries.into_iter().map(WalEntryVariant::Full).collect(),
            false, /* is_compacted */
        )
        .await
    }

    async fn wal_metadata_stream<'a>(
        &self,
        scope: WALScope<'a>,
        optional_input: WalMetadataStreamOptionalInput,
    ) -> Result<BoxStream<'static, Result<Box<dyn WalMetadata>>>> {
        let concrete_stream = self
            .wal_metadata_stream_concrete(scope, optional_input)
            .await?;
        let ret = concrete_stream
            .map(|x| x.map(|x| Box::new(x) as Box<dyn WalMetadata>))
            .boxed();
        Ok(ret)
    }

    async fn status(&self) -> Result<String> {
        validate_object_store_connection(&self.object_store, &self.store_prefix).await?;
        let global_store_status = self.global_store.status().await?;
        Ok(format!(
            "{:?}-type ObjectStore is ok. {}",
            self.store_type, global_store_status
        ))
    }

    fn remove_local(&self) -> Result<()> {
        let wal_prefix_path = PathBuf::from("/").join(&self.store_prefix);
        if std::fs::metadata(&wal_prefix_path).is_ok() {
            std::fs::remove_dir_all(&wal_prefix_path)?;
        }
        Ok(())
    }

    #[instrument(err, skip(self))]
    async fn delete_up_to_xact_id<'a>(
        &self,
        input: DeleteUpToXactIdInput<'a>,
        _options: &DeleteUpToXactIdOptions,
    ) -> Result<DeleteFromWalStats> {
        // This function deletes segment metadata earlier than `xact_id` from
        // the global store. We don't need to explicitly delete the WAL files
        // here, because without metadata in the global store these entries will
        // never be accessed. Stale WAL files should be removed by vacuuming.

        let segment_ids = vec![get_wal_scope_segment_id(&input.scope)];

        if input.dry_run {
            Ok(DeleteFromWalStats {
                planned_num_deletes: self
                    .global_store
                    .count_segment_wal_entries_up_to_xact_id(
                        &segment_ids,
                        input.min_retained_xact_id,
                    )
                    .await?,
                num_deletes: 0,
                total_bytes: 0,
            })
        } else {
            let num_deleted_rows = self
                .global_store
                .delete_segment_wal_entries_up_to_xact_id(&segment_ids, input.min_retained_xact_id)
                .await?;
            Ok(DeleteFromWalStats {
                planned_num_deletes: num_deleted_rows,
                num_deletes: num_deleted_rows,
                total_bytes: 0,
            })
        }
    }
}

fn get_wal_scope_segment_id(scope: &WALScope) -> Uuid {
    match scope {
        WALScope::ObjectId(_, _) => {
            panic!("Object WAL not supported for ObjectAndGlobalStoreWal");
        }
        WALScope::Segment(segment_id) => *segment_id,
    }
}

fn compute_wal_entry_digest(bytes: &[u8]) -> i64 {
    let mut hasher = Sha256::new();
    hasher.update(bytes);
    let result = hasher.finalize();
    i64::from_le_bytes(result[0..8].try_into().unwrap())
}

fn compute_wal_entry_system_fields_digest<'a, I: Iterator<Item = &'a WalEntrySystemFields>>(
    segment_id: Uuid,
    xact_id: TransactionId,
    entries: I,
) -> i64 {
    let mut hasher = Sha256::new();
    hasher.update(segment_id.as_bytes());
    hasher.update(xact_id.0.to_le_bytes());
    for entry in entries {
        hasher.update(entry.id.as_bytes());
    }
    let result = hasher.finalize();
    i64::from_le_bytes(result[0..8].try_into().unwrap())
}

enum SerializableWalEntry {
    Value(WalEntry),
    RawBytes(OwnedBytes),
}

#[derive(Debug)]
struct OptimizableKey {
    key: PathBuf,
    wal_entries: Vec<(WalEntrySystemFields, WalEntryBytesRef)>,
}

fn construct_upsert_segment_wal_entries(
    mut wal_entries: Vec<&(WalEntrySystemFields, WalEntryBytesRef)>,
    segment_id: Uuid,
    is_compacted: bool,
) -> Vec<(usize, UpsertSegmentWalEntry)> {
    if wal_entries.is_empty() {
        return Vec::new();
    }

    // Group the WAL entries by contiguous xact_id and byte range.
    wal_entries.sort_by_key(|(system_fields, bytes_ref)| {
        (system_fields._xact_id, bytes_ref.byte_range_start)
    });

    let mut out: Vec<(usize, UpsertSegmentWalEntry)> = Vec::new();
    let add_wal_entry =
        |start_idx: usize, end_idx: usize, out: &mut Vec<(usize, UpsertSegmentWalEntry)>| {
            let xact_id = wal_entries[start_idx].0._xact_id;
            let digest = compute_wal_entry_system_fields_digest(
                segment_id,
                xact_id,
                wal_entries[start_idx..end_idx]
                    .iter()
                    .map(|(system_fields, _)| system_fields),
            );
            out.push((
                end_idx - start_idx,
                UpsertSegmentWalEntry {
                    xact_id,
                    byte_range_start: wal_entries[start_idx].1.byte_range_start as usize,
                    byte_range_end: wal_entries[end_idx - 1].1.byte_range_end as usize,
                    is_compacted,
                    digest: Some(digest),
                },
            ));
        };
    let mut start_idx = 0;
    for next_idx in 1..wal_entries.len() {
        let next_entry = wal_entries[next_idx];
        let prev_entry = wal_entries[next_idx - 1];
        // If this wal entry is contiguous with the previous one, continue.
        if next_entry.0._xact_id == prev_entry.0._xact_id
            && next_entry.1.byte_range_start == prev_entry.1.byte_range_end
        {
            continue;
        }
        // Otherwise, we have a break and need to create an entry for the current range and start
        // anew.
        add_wal_entry(start_idx, next_idx, &mut out);
        start_idx = next_idx;
    }
    add_wal_entry(start_idx, wal_entries.len(), &mut out);
    out
}

#[cfg(test)]
pub fn construct_upsert_segment_wal_entries_for_test(
    wal_entries: Vec<&(WalEntrySystemFields, WalEntryBytesRef)>,
    segment_id: Uuid,
    is_compacted: bool,
) -> Vec<(usize, UpsertSegmentWalEntry)> {
    construct_upsert_segment_wal_entries(wal_entries, segment_id, is_compacted)
}

#[cfg(test)]
pub fn compute_wal_entry_system_fields_digest_for_test<
    'a,
    I: Iterator<Item = &'a WalEntrySystemFields>,
>(
    segment_id: Uuid,
    xact_id: TransactionId,
    entries: I,
) -> i64 {
    compute_wal_entry_system_fields_digest(segment_id, xact_id, entries)
}
