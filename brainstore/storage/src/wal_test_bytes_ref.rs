use futures::StreamExt;
use once_cell::sync::Lazy;
use std::sync::Arc;

use util::{serde_json::json, uuid::Uuid, xact::TransactionId};

use crate::{
    global_store::MemoryGlobalStore,
    object_and_global_store_wal::{
        compute_wal_entry_system_fields_digest_for_test, ObjectAndGlobalStoreWal,
        ObjectAndGlobalStoreWalInsertOptionalInput,
    },
    object_store_wal::ObjectStoreWal,
    test_util::TmpDirStore,
    wal::{WALScope, Wal, WalEntryBytesRef, WalEntryRawBytes, WalEntryVariant},
    wal_entry::{WalEntry, WalEntrySystemFields},
};

static WAL_SCOPE_SOURCE: Lazy<WALScope<'static>> = Lazy::new(|| WALScope::Segment(Uuid::new_v4()));
static WAL_SCOPE_DEST: Lazy<WALScope<'static>> = Lazy::new(|| WALScope::Segment(Uuid::new_v4()));

fn collect_wal_entry_system_fields(
    wal_entry_variants: Vec<WalEntryVariant>,
) -> Vec<WalEntrySystemFields> {
    wal_entry_variants
        .into_iter()
        .map(|v| match v {
            WalEntryVariant::SystemFields { system_fields, .. } => system_fields,
            _ => unreachable!("Impossible"),
        })
        .collect()
}

#[test]
fn test_construct_upsert_segment_wal_entries() {
    use crate::object_and_global_store_wal::construct_upsert_segment_wal_entries_for_test;
    use std::path::PathBuf;

    let tmp_dir_store = TmpDirStore::new();
    let segment_id = Uuid::new_v4();
    let dummy_path = PathBuf::from("/tmp/dummy");

    // Helper to create a test entry
    let make_entry = |id: &str, xact_id: u64, start: u64, end: u64| {
        (
            WalEntrySystemFields {
                id: id.to_string(),
                _xact_id: TransactionId(xact_id),
                ..Default::default()
            },
            WalEntryBytesRef {
                directory: tmp_dir_store.store_info.directory.clone(),
                key: dummy_path.clone(),
                byte_range_start: start,
                byte_range_end: end,
                key_file_len: 1000,
            },
        )
    };

    // Test 1: Empty input
    let entries: Vec<(WalEntrySystemFields, WalEntryBytesRef)> = vec![];
    let result =
        construct_upsert_segment_wal_entries_for_test(entries.iter().collect(), segment_id, false);
    assert_eq!(result.len(), 0);

    // Test 2: Single entry
    let entries = [make_entry("row1", 100, 0, 10)];
    let result =
        construct_upsert_segment_wal_entries_for_test(entries.iter().collect(), segment_id, false);
    assert_eq!(result.len(), 1);
    assert_eq!(result[0].1.xact_id, TransactionId(100));
    assert_eq!(result[0].1.byte_range_start, 0);
    assert_eq!(result[0].1.byte_range_end, 10);
    assert!(!result[0].1.is_compacted);
    assert!(result[0].1.digest.is_some());

    // Test 3: Two contiguous entries with same xact_id - should merge
    let entries = vec![
        make_entry("row1", 100, 0, 10),
        make_entry("row2", 100, 10, 20),
    ];
    let result =
        construct_upsert_segment_wal_entries_for_test(entries.iter().collect(), segment_id, false);
    assert_eq!(result.len(), 1);
    assert_eq!(result[0].1.byte_range_start, 0);
    assert_eq!(result[0].1.byte_range_end, 20);

    // Test 4: Three contiguous entries - should merge all
    let entries = vec![
        make_entry("row1", 100, 0, 10),
        make_entry("row2", 100, 10, 20),
        make_entry("row3", 100, 20, 30),
    ];
    let result =
        construct_upsert_segment_wal_entries_for_test(entries.iter().collect(), segment_id, false);
    assert_eq!(result.len(), 1);
    assert_eq!(result[0].1.byte_range_start, 0);
    assert_eq!(result[0].1.byte_range_end, 30);

    // Test 5: Entries given out of order but contiguous - should still merge
    let entries = vec![
        make_entry("row2", 100, 10, 20),
        make_entry("row1", 100, 0, 10),
        make_entry("row3", 100, 20, 30),
    ];
    let result =
        construct_upsert_segment_wal_entries_for_test(entries.iter().collect(), segment_id, false);
    assert_eq!(result.len(), 1);
    assert_eq!(result[0].1.byte_range_start, 0);
    assert_eq!(result[0].1.byte_range_end, 30);

    // Test 6: Non-contiguous entries (gap) - should NOT merge
    let entries = vec![
        make_entry("row1", 100, 0, 10),
        make_entry("row2", 100, 11, 20), // Gap from 10 to 11
    ];
    let result =
        construct_upsert_segment_wal_entries_for_test(entries.iter().collect(), segment_id, false);
    assert_eq!(result.len(), 2);
    assert_eq!(result[0].1.byte_range_start, 0);
    assert_eq!(result[0].1.byte_range_end, 10);
    assert_eq!(result[1].1.byte_range_start, 11);
    assert_eq!(result[1].1.byte_range_end, 20);

    // Test 7: Overlapping entries - should NOT merge
    let entries = vec![
        make_entry("row1", 100, 0, 10),
        make_entry("row2", 100, 9, 20), // Overlaps at 9
    ];
    let result =
        construct_upsert_segment_wal_entries_for_test(entries.iter().collect(), segment_id, false);
    assert_eq!(result.len(), 2);

    // Test 8: Gap in middle of three entries
    let entries = vec![
        make_entry("row1", 100, 0, 10),
        make_entry("row2", 100, 10, 20),
        make_entry("row3", 100, 21, 30), // Gap from 20 to 21
    ];
    let result =
        construct_upsert_segment_wal_entries_for_test(entries.iter().collect(), segment_id, false);
    assert_eq!(result.len(), 2);
    assert_eq!(result[0].1.byte_range_start, 0);
    assert_eq!(result[0].1.byte_range_end, 20);
    assert_eq!(result[1].1.byte_range_start, 21);
    assert_eq!(result[1].1.byte_range_end, 30);

    // Test 9: Different xact_ids - should NOT merge
    let entries = vec![
        make_entry("row1", 100, 0, 10),
        make_entry("row2", 101, 10, 20), // Different xact_id
    ];
    let result =
        construct_upsert_segment_wal_entries_for_test(entries.iter().collect(), segment_id, false);
    assert_eq!(result.len(), 2);
    assert_eq!(result[0].1.xact_id, TransactionId(100));
    assert_eq!(result[1].1.xact_id, TransactionId(101));

    // Test 10: Test is_compacted flag
    let entries = [make_entry("row1", 100, 0, 10)];
    let result =
        construct_upsert_segment_wal_entries_for_test(entries.iter().collect(), segment_id, true);
    assert_eq!(result.len(), 1);
    assert!(result[0].1.is_compacted);
}

#[tokio::test]
async fn test_bytes_ref_optimization_eligible_for_copy() {
    let tmp_dir_store = TmpDirStore::new();

    // Create a source WAL with entries
    let source_wal = ObjectStoreWal {
        store: tmp_dir_store.store_info.store.clone(),
        store_type: tmp_dir_store.store_info.store_type,
        directory: tmp_dir_store.store_info.directory.clone(),
        store_prefix: tmp_dir_store.store_info.prefix.clone(),
    };

    // Write entries to the source WAL - all with the same xact_id so they go to one file
    let wal_entries = vec![
        WalEntry {
            _xact_id: TransactionId(100),
            id: "row1".to_string(),
            data: json!({"data_field": "value1"}).as_object().unwrap().clone(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(100),
            id: "row2".to_string(),
            data: json!({"data_field": "value2"}).as_object().unwrap().clone(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(100),
            id: "row3".to_string(),
            data: json!({"data_field": "value3"}).as_object().unwrap().clone(),
            ..Default::default()
        },
    ];

    source_wal
        .insert(*WAL_SCOPE_SOURCE, wal_entries.clone())
        .await
        .unwrap();

    // Get the metadata for the written file
    let mut metadata_stream = source_wal
        .wal_metadata_stream_concrete(*WAL_SCOPE_SOURCE, Default::default())
        .await
        .unwrap();

    let source_metadata = metadata_stream.next().await.unwrap().unwrap();
    let key_path: std::path::PathBuf = source_metadata.object_meta.location.to_string().into();

    // Create BytesRef entries pointing to the source file - contiguous for all 3 entries
    let mut wal_entry_variants: Vec<WalEntryVariant> = Vec::new();
    for (current_offset, entry) in wal_entries.iter().enumerate() {
        wal_entry_variants.push(WalEntryVariant::SystemFields {
            system_fields: WalEntrySystemFields::from_wal_entry(entry),
            raw_bytes: WalEntryRawBytes::BytesRef(WalEntryBytesRef {
                directory: source_metadata.directory.clone(),
                key: key_path.clone(),
                byte_range_start: current_offset as u64,
                byte_range_end: (current_offset + 1) as u64,
                key_file_len: 1,
            }),
        });
    }

    let segment_id = match *WAL_SCOPE_DEST {
        WALScope::Segment(id) => id,
        _ => panic!("Expected segment scope"),
    };

    // Calculate the expected digest if copy optimization is used
    let expected_digest = compute_wal_entry_system_fields_digest_for_test(
        segment_id,
        TransactionId(100),
        collect_wal_entry_system_fields(wal_entry_variants.clone()).iter(),
    );

    // Now insert these BytesRef entries into ObjectAndGlobalStoreWal
    let object_and_global_wal = ObjectAndGlobalStoreWal {
        object_store: tmp_dir_store.store_info.store.clone(),
        global_store: Arc::new(MemoryGlobalStore::default()),
        directory: tmp_dir_store.store_info.directory.clone(),
        store_prefix: tmp_dir_store.store_info.prefix.clone(),
        store_type: tmp_dir_store.store_info.store_type,
    };

    let result = object_and_global_wal
        .insert_no_commit_metadata(*WAL_SCOPE_DEST, wal_entry_variants, Default::default())
        .await
        .unwrap();

    let wal_metadatas = result.get(&segment_id).expect("Expected segment WAL data");

    // When copy optimization is used, we get one file per distinct xact_id
    // Since all entries have the same xact_id, we expect 1 file
    assert_eq!(
        wal_metadatas.len(),
        1,
        "Expected exactly one WAL file for one xact_id, got {}",
        wal_metadatas.len()
    );

    // Check that the digest matches what we expect from copy optimization
    let has_expected_digest = wal_metadatas
        .values()
        .any(|entries| entries.iter().any(|e| e.digest == Some(expected_digest)));

    if !has_expected_digest {
        println!("Expected digest: {:?}", expected_digest);
        for (_, entries) in wal_metadatas.iter() {
            for e in entries {
                println!("Actual digest: {:?}", e.digest);
            }
        }
    }

    assert!(
        has_expected_digest,
        "Copy optimization should preserve the expected digest"
    );
}

#[tokio::test]
async fn test_bytes_ref_optimization_non_contiguous_ranges() {
    let tmp_dir_store = TmpDirStore::new();

    let source_wal = ObjectStoreWal {
        store: tmp_dir_store.store_info.store.clone(),
        store_type: tmp_dir_store.store_info.store_type,
        directory: tmp_dir_store.store_info.directory.clone(),
        store_prefix: tmp_dir_store.store_info.prefix.clone(),
    };

    let wal_entries = vec![
        WalEntry {
            _xact_id: TransactionId(100),
            id: "row1".to_string(),
            data: json!({"data_field": "value1"}).as_object().unwrap().clone(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(100),
            id: "row2".to_string(),
            data: json!({"data_field": "value2"}).as_object().unwrap().clone(),
            ..Default::default()
        },
    ];

    source_wal
        .insert(*WAL_SCOPE_SOURCE, wal_entries.clone())
        .await
        .unwrap();

    let mut metadata_stream = source_wal
        .wal_metadata_stream_concrete(*WAL_SCOPE_SOURCE, Default::default())
        .await
        .unwrap();

    let source_metadata = metadata_stream.next().await.unwrap().unwrap();
    let key_path: std::path::PathBuf = source_metadata.object_meta.location.to_string().into();

    // Create BytesRef entries with NON-CONTIGUOUS ranges (gap in the middle)
    // Use smaller byte ranges that won't exceed file size
    let wal_entry_variants: Vec<WalEntryVariant> = vec![
        WalEntryVariant::SystemFields {
            system_fields: WalEntrySystemFields::from_wal_entry(&wal_entries[0]),
            raw_bytes: WalEntryRawBytes::BytesRef(WalEntryBytesRef {
                directory: source_metadata.directory.clone(),
                key: key_path.clone(),
                byte_range_start: 0,
                byte_range_end: 50,
                key_file_len: 1,
            }),
        },
        WalEntryVariant::SystemFields {
            system_fields: WalEntrySystemFields::from_wal_entry(&wal_entries[1]),
            raw_bytes: WalEntryRawBytes::BytesRef(WalEntryBytesRef {
                directory: source_metadata.directory.clone(),
                key: key_path.clone(),
                byte_range_start: 100, // Gap from 50-99
                byte_range_end: 150,
                key_file_len: 1,
            }),
        },
    ];
    let wal_entry_system_fields = collect_wal_entry_system_fields(wal_entry_variants.clone());

    let object_and_global_wal = ObjectAndGlobalStoreWal {
        object_store: tmp_dir_store.store_info.store.clone(),
        global_store: Arc::new(MemoryGlobalStore::default()),
        directory: tmp_dir_store.store_info.directory.clone(),
        store_prefix: tmp_dir_store.store_info.prefix.clone(),
        store_type: tmp_dir_store.store_info.store_type,
    };

    let mut result = object_and_global_wal
        .insert_no_commit_metadata(*WAL_SCOPE_DEST, wal_entry_variants, Default::default())
        .await
        .unwrap();

    let segment_id = match *WAL_SCOPE_DEST {
        WALScope::Segment(id) => id,
        _ => panic!("Expected segment scope"),
    };

    let mut wal_metadatas = result
        .remove(&segment_id)
        .expect("Expected segment WAL data")
        .into_iter()
        .flat_map(|(_, entries)| entries)
        .collect::<Vec<_>>();
    wal_metadatas.sort_by_key(|k| k.byte_range_start);

    // We should have two entries, one for each wal entry.
    assert_eq!(wal_metadatas.len(), 2);
    assert_eq!(
        wal_metadatas[0].digest.unwrap(),
        compute_wal_entry_system_fields_digest_for_test(
            segment_id,
            TransactionId(100),
            wal_entry_system_fields[0..1].iter(),
        )
    );
    assert_eq!(
        wal_metadatas[1].digest.unwrap(),
        compute_wal_entry_system_fields_digest_for_test(
            segment_id,
            TransactionId(100),
            wal_entry_system_fields[1..2].iter(),
        )
    );
}

#[tokio::test]
async fn test_bytes_ref_optimization_partial_file_copy() {
    use crate::object_and_global_store_wal::compute_wal_entry_system_fields_digest_for_test;

    let tmp_dir_store = TmpDirStore::new();

    let source_wal = ObjectStoreWal {
        store: tmp_dir_store.store_info.store.clone(),
        store_type: tmp_dir_store.store_info.store_type,
        directory: tmp_dir_store.store_info.directory.clone(),
        store_prefix: tmp_dir_store.store_info.prefix.clone(),
    };

    let wal_entries = vec![
        WalEntry {
            _xact_id: TransactionId(100),
            id: "row1".to_string(),
            data: json!({"data_field": "value1"}).as_object().unwrap().clone(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(100),
            id: "row2".to_string(),
            data: json!({"data_field": "value2"}).as_object().unwrap().clone(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(100),
            id: "row3".to_string(),
            data: json!({"data_field": "value3"}).as_object().unwrap().clone(),
            ..Default::default()
        },
    ];

    source_wal
        .insert(*WAL_SCOPE_SOURCE, wal_entries.clone())
        .await
        .unwrap();

    let mut metadata_stream = source_wal
        .wal_metadata_stream_concrete(*WAL_SCOPE_SOURCE, Default::default())
        .await
        .unwrap();

    let source_metadata = metadata_stream.next().await.unwrap().unwrap();
    let key_path: std::path::PathBuf = source_metadata.object_meta.location.to_string().into();

    // Only include 1 out of 3 entries - should still use copy optimization
    let wal_entry_variants: Vec<WalEntryVariant> = vec![WalEntryVariant::SystemFields {
        system_fields: WalEntrySystemFields::from_wal_entry(&wal_entries[0]),
        raw_bytes: WalEntryRawBytes::BytesRef(WalEntryBytesRef {
            directory: source_metadata.directory.clone(),
            key: key_path,
            byte_range_start: 0,
            byte_range_end: 1,
            key_file_len: 1,
        }),
    }];

    let segment_id = match *WAL_SCOPE_DEST {
        WALScope::Segment(id) => id,
        _ => panic!("Expected segment scope"),
    };

    // Calculate the expected digest if copy optimization is used
    let expected_digest = compute_wal_entry_system_fields_digest_for_test(
        segment_id,
        TransactionId(100),
        collect_wal_entry_system_fields(wal_entry_variants.clone())[0..1].iter(),
    );

    let object_and_global_wal = ObjectAndGlobalStoreWal {
        object_store: tmp_dir_store.store_info.store.clone(),
        global_store: Arc::new(MemoryGlobalStore::default()),
        directory: tmp_dir_store.store_info.directory.clone(),
        store_prefix: tmp_dir_store.store_info.prefix.clone(),
        store_type: tmp_dir_store.store_info.store_type,
    };

    let mut result = object_and_global_wal
        .insert_no_commit_metadata(*WAL_SCOPE_DEST, wal_entry_variants, Default::default())
        .await
        .unwrap();
    let wal_metadatas = result
        .remove(&segment_id)
        .expect("Expected segment WAL data")
        .into_iter()
        .flat_map(|(_, v)| v)
        .collect::<Vec<_>>();

    // Should still create 1 file for the single entry
    assert_eq!(
        wal_metadatas.len(),
        1,
        "Expected 1 WAL file for single entry, got {}",
        wal_metadatas.len()
    );
    assert_eq!(wal_metadatas[0].digest.unwrap(), expected_digest);
}

#[tokio::test]
async fn test_bytes_ref_optimization_conflicting_metadata() {
    use crate::object_and_global_store_wal::compute_wal_entry_system_fields_digest_for_test;

    let tmp_dir_store = TmpDirStore::new();

    let source_wal = ObjectStoreWal {
        store: tmp_dir_store.store_info.store.clone(),
        store_type: tmp_dir_store.store_info.store_type,
        directory: tmp_dir_store.store_info.directory.clone(),
        store_prefix: tmp_dir_store.store_info.prefix.clone(),
    };

    let wal_entries = vec![
        WalEntry {
            _xact_id: TransactionId(100),
            id: "row1".to_string(),
            data: json!({"data_field": "value1"}).as_object().unwrap().clone(),
            ..Default::default()
        },
        WalEntry {
            _xact_id: TransactionId(100),
            id: "row2".to_string(),
            data: json!({"data_field": "value2"}).as_object().unwrap().clone(),
            ..Default::default()
        },
    ];

    source_wal
        .insert(*WAL_SCOPE_SOURCE, wal_entries.clone())
        .await
        .unwrap();

    let mut metadata_stream = source_wal
        .wal_metadata_stream_concrete(*WAL_SCOPE_SOURCE, Default::default())
        .await
        .unwrap();

    let source_metadata = metadata_stream.next().await.unwrap().unwrap();
    let key_path: std::path::PathBuf = source_metadata.object_meta.location.to_string().into();

    let mut wal_entry_variants_conflicting: Vec<WalEntryVariant> = Vec::new();
    for (i, entry) in wal_entries.iter().enumerate() {
        wal_entry_variants_conflicting.push(WalEntryVariant::SystemFields {
            system_fields: WalEntrySystemFields::from_wal_entry(entry),
            raw_bytes: WalEntryRawBytes::BytesRef(WalEntryBytesRef {
                directory: source_metadata.directory.clone(),
                key: key_path.clone(),
                byte_range_start: i as u64,
                byte_range_end: (i + 1) as u64,
                // Conflicting metadata: different key_file_len for each entry
                key_file_len: if i == 0 { 1 } else { 2 },
            }),
        });
    }

    let segment_id = match *WAL_SCOPE_DEST {
        WALScope::Segment(id) => id,
        _ => panic!("Expected segment scope"),
    };

    // Calculate the expected digest if copy optimization were to be used (but it shouldn't be)
    let expected_copy_digest = compute_wal_entry_system_fields_digest_for_test(
        segment_id,
        TransactionId(100),
        collect_wal_entry_system_fields(wal_entry_variants_conflicting.clone()).iter(),
    );

    let object_and_global_wal = ObjectAndGlobalStoreWal {
        object_store: tmp_dir_store.store_info.store.clone(),
        global_store: Arc::new(MemoryGlobalStore::default()),
        directory: tmp_dir_store.store_info.directory.clone(),
        store_prefix: tmp_dir_store.store_info.prefix.clone(),
        store_type: tmp_dir_store.store_info.store_type,
    };

    let mut result = object_and_global_wal
        .insert_no_commit_metadata(
            *WAL_SCOPE_DEST,
            wal_entry_variants_conflicting,
            Default::default(),
        )
        .await
        .unwrap();

    let wal_metadatas = result
        .remove(&segment_id)
        .expect("Expected segment WAL data")
        .into_iter()
        .flat_map(|(_, v)| v)
        .collect::<Vec<_>>();

    // Should still create 1 file but NOT use copy optimization
    assert_eq!(
        wal_metadatas.len(),
        1,
        "Expected 1 WAL file, got {}",
        wal_metadatas.len()
    );

    assert_ne!(wal_metadatas[0].digest.unwrap(), expected_copy_digest);

    // Test 2: Now test with consistent key_file_len - should use copy optimization
    let mut wal_entry_variants_consistent: Vec<WalEntryVariant> = Vec::new();
    for (i, entry) in wal_entries.iter().enumerate() {
        wal_entry_variants_consistent.push(WalEntryVariant::SystemFields {
            system_fields: WalEntrySystemFields::from_wal_entry(entry),
            raw_bytes: WalEntryRawBytes::BytesRef(WalEntryBytesRef {
                directory: source_metadata.directory.clone(),
                key: key_path.clone(),
                byte_range_start: i as u64,
                byte_range_end: (i + 1) as u64,
                key_file_len: 1,
            }),
        });
    }

    // New scope for the second test
    let wal_scope_dest_2 = WALScope::Segment(Uuid::new_v4());
    let segment_id_2 = match wal_scope_dest_2 {
        WALScope::Segment(id) => id,
        _ => panic!("Expected segment scope"),
    };

    let expected_copy_digest_2 = compute_wal_entry_system_fields_digest_for_test(
        segment_id_2,
        TransactionId(100),
        collect_wal_entry_system_fields(wal_entry_variants_consistent.clone()).iter(),
    );

    let mut result2 = object_and_global_wal
        .insert_no_commit_metadata(
            wal_scope_dest_2,
            wal_entry_variants_consistent,
            Default::default(),
        )
        .await
        .unwrap();

    let wal_metadatas_2 = result2
        .remove(&segment_id_2)
        .expect("Expected segment WAL data")
        .into_iter()
        .flat_map(|(_, entries)| entries)
        .collect::<Vec<_>>();

    assert_eq!(
        wal_metadatas_2.len(),
        1,
        "Expected 1 WAL file, got {}",
        wal_metadatas_2.len()
    );
    assert_eq!(wal_metadatas_2[0].digest.unwrap(), expected_copy_digest_2);
}

#[tokio::test]
async fn test_bytes_ref_file_not_found_error_propagation() {
    let tmp_dir_store = TmpDirStore::new();

    // Create BytesRef entries pointing to a nonexistent file
    let nonexistent_path = std::path::PathBuf::from("/nonexistent/path/to/file.jsonl");
    let wal_entry_variants: Vec<WalEntryVariant> = vec![
        WalEntryVariant::SystemFields {
            system_fields: WalEntrySystemFields {
                id: "row1".to_string(),
                _xact_id: TransactionId(100),
                ..Default::default()
            },
            raw_bytes: WalEntryRawBytes::BytesRef(WalEntryBytesRef {
                directory: tmp_dir_store.store_info.directory.clone(),
                key: nonexistent_path.clone(),
                byte_range_start: 0,
                byte_range_end: 100,
                key_file_len: 1000,
            }),
        },
        WalEntryVariant::SystemFields {
            system_fields: WalEntrySystemFields {
                id: "row2".to_string(),
                _xact_id: TransactionId(100),
                ..Default::default()
            },
            raw_bytes: WalEntryRawBytes::BytesRef(WalEntryBytesRef {
                directory: tmp_dir_store.store_info.directory.clone(),
                key: nonexistent_path.clone(),
                byte_range_start: 100,
                byte_range_end: 200,
                key_file_len: 1000,
            }),
        },
    ];

    let object_and_global_wal = ObjectAndGlobalStoreWal {
        object_store: tmp_dir_store.store_info.store.clone(),
        global_store: Arc::new(MemoryGlobalStore::default()),
        directory: tmp_dir_store.store_info.directory.clone(),
        store_prefix: tmp_dir_store.store_info.prefix.clone(),
        store_type: tmp_dir_store.store_info.store_type,
    };

    // Test 1: Without the flag, insertion should succeed (file not found errors are ignored)
    let result_without_flag = object_and_global_wal
        .insert_no_commit_metadata(
            *WAL_SCOPE_DEST,
            wal_entry_variants.clone(),
            ObjectAndGlobalStoreWalInsertOptionalInput {
                is_compacted: false,
                testing_propagate_file_not_found_errors: false,
            },
        )
        .await;

    assert!(
        result_without_flag.is_ok(),
        "Insertion should succeed when testing_propagate_file_not_found_errors is false"
    );

    // Test 2: With the flag, insertion should fail with a file not found error
    let new_scope = WALScope::Segment(Uuid::new_v4());
    let result_with_flag = object_and_global_wal
        .insert_no_commit_metadata(
            new_scope,
            wal_entry_variants,
            ObjectAndGlobalStoreWalInsertOptionalInput {
                is_compacted: false,
                testing_propagate_file_not_found_errors: true,
            },
        )
        .await;

    assert!(
        result_with_flag.is_err(),
        "Insertion should fail when testing_propagate_file_not_found_errors is true and file doesn't exist"
    );

    // Verify the error is related to file not found
    let error_message = result_with_flag.unwrap_err().to_string();
    assert!(
        error_message.contains("not found") || error_message.contains("No such file"),
        "Error should be about file not found, got: {}",
        error_message
    );
}
