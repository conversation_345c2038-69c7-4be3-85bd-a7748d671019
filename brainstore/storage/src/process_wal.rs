use std::{
    collections::{HashMap, HashSet, VecDeque},
    sync::Arc,
};

use async_util::{
    await_spawn_blocking, spawn_blocking_util::spawn_blocking_with_async_timeout,
    test_util::TwoWaySyncPointSendAndWait,
};
use clap::Parser;
use futures::{
    future::{join_all, BoxFuture},
    join, stream, StreamExt,
};
use lazy_static::lazy_static;
use otel_common::opentelemetry::{
    metrics::{Counter, Histogram},
    KeyValue,
};
use serde::{Deserialize, Serialize};
use tantivy::TantivyDocument;
use tracing::{instrument, Instrument};
use util::{
    anyhow::{anyhow, Result},
    functional::merge_options,
    itertools::{merge_join_by, EitherOrBoth, Itertools},
    system_types::{FullObjectId, FullRowId, FullRowIdOwned},
    uuid::{self, Uuid},
    xact::{PaginationKey, TransactionId},
};

use crate::{
    config_with_store::{ConfigWithStore, StoreInfo},
    global_locks_manager::GlobalLocksManager,
    global_store::{
        GlobalStore, IdSegmentMembershipType, LastCompactedIndexMeta, LastIndexOperation,
        LastIndexOperationDetails, ObjectMetadataUpdate, SegmentFieldStatistics, SegmentMetadata,
        SegmentMetadataUpdate, UpsertSegmentWalEntry,
    },
    index_document::IndexDocument,
    legacy_sanitizers,
    object_and_global_store_wal::{
        ObjectAndGlobalStoreWal, ObjectAndGlobalStoreWalInsertOptionalInput,
    },
    static_sync_runtime::STATIC_SYNC_RUNTIME,
    status_updater::{ProgressCounter, StatusUpdate, StatusUpdater},
    tantivy_footer::{make_footer, MakeFooterInput},
    tantivy_index::{
        validate_tantivy_index, IndexMetaJson, TantivyIndexScope, TantivyIndexWriterOpts,
    },
    tantivy_index_wrapper::ReadWriteTantivyIndexWrapper,
    wal::{
        default_target_batch_size, wal_stream_generic, PreferWalEntry, PreferWalEntrySystemFields,
        WALScope, WalEntryVariant, WalMetadataStreamOptionalInput, WalStreamMetrics,
        WalStreamOptions,
    },
    wal_entry::WalEntry,
    write_index_operation_harness::{
        write_index_operation_harness, WriteIndexOperationHarnessInput,
        WriteIndexOperationHarnessOptionalInput, WriteIndexOperationHarnessTaskAdditionalInput,
        WriteIndexOperationHarnessTestingSyncPoints,
    },
};

struct CompactionMetrics {
    pub ingest_to_compaction_delay_seconds: Histogram<u64>,
}

impl Default for CompactionMetrics {
    fn default() -> Self {
        let meter = otel_common::opentelemetry::global::meter("brainstore");
        Self {
            ingest_to_compaction_delay_seconds: meter
                .u64_histogram("brainstore.storage.ingest_to_compaction_delay_seconds")
                .with_unit("s")
                .with_description("Time delay in seconds between when a record was ingested and when it was compacted")
                .build(),
        }
    }
}

struct ProcessingMetrics {
    num_rows: Counter<u64>,
    num_transactions: Counter<u64>,
    num_bytes: Counter<u64>,
    segment_num_rows: Counter<u64>,
}

impl ProcessingMetrics {
    pub fn new() -> Self {
        let meter = otel_common::opentelemetry::global::meter("brainstore");
        Self {
            num_rows: meter
                .u64_counter("brainstore.storage.process_object_wal.num_rows")
                .build(),
            num_transactions: meter
                .u64_counter("brainstore.storage.process_object_wal.num_transactions")
                .build(),
            num_bytes: meter
                .u64_counter("brainstore.storage.process_object_wal.num_bytes")
                .build(),
            segment_num_rows: meter
                .u64_counter("brainstore.storage.process_segment_wal.num_rows")
                .build(),
        }
    }
}

lazy_static! {
    static ref PROCESSING_METRICS: ProcessingMetrics = ProcessingMetrics::new();
    static ref COMPACTION_METRICS: CompactionMetrics = CompactionMetrics::default();
}

// Threshold in seconds for warning about slow compaction (8 minutes)
pub const SLOW_COMPACTION_THRESHOLD_SECS: u64 = 480;

#[derive(Clone)]
pub struct ProcessObjectWalInput<'a> {
    pub object_id: FullObjectId<'a>,
    pub config: &'a ConfigWithStore,
}

#[derive(Default)]
pub struct ProcessObjectWalOptionalInput {
    pub testing_sync_points: ProcessObjectWalTestingSyncPoints,

    // For certain backfill-style operations, we may not be able to process WAL entries in
    // strictly-increasing xact_id order. Thus we allow running process_object_wal over a custom
    // range of xact_ids, which can essentially override the default of starting from
    // last_processed_xact_id.
    //
    // Thus in the backfill context, last_processed_xact_id does not necessarily imply that we have
    // processed all WAL entries up to that point, but it can still be a reasonable marker for
    // where the segment WAL ends. The expectation is that eventually we will have backfilled all
    // WAL entries before last_processed_xact_id.
    /// Start reading from the WAL at min(start_xact_id, last_processed_xact_id + 1).
    pub start_xact_id: Option<TransactionId>,

    /// Stop reading from the WAL at end_xact_id (inclusive).
    pub end_xact_id: Option<TransactionId>,

    // Certain operations (e.g. backfill on the comments table) may not want to trigger compaction
    // over the new WAL entries. This flag marks the new WAL entries as "compacted", so they are
    // not picked up for compaction.
    pub mark_as_compacted: bool,

    // A source identifier that the caller can use to tag the invocation with extra information.
    pub source: Option<String>,
}

#[derive(Default)]
pub struct ProcessObjectWalTestingSyncPoints {
    // These will only be used for the first batch of WAL entries.
    pub process_object_wal_loaded_testing_sync_points: ProcessObjectWalLoadedTestingSyncPoints,
    pub before_update_object_metadata: Option<TwoWaySyncPointSendAndWait>,
}

#[derive(Debug, Clone, Parser, Serialize, Deserialize)]
pub struct ProcessObjectWalOptions {
    /// The maximum number of unique rows we try to keep in a segment, before spilling over to a
    /// new segment.
    #[arg(
        long,
        default_value_t = default_max_rows_per_segment(),
        env = "BRAINSTORE_MAX_ROWS_PER_SEGMENT"
    )]
    #[serde(default = "default_max_rows_per_segment")]
    pub max_rows_per_segment: usize,

    #[command(flatten)]
    #[serde(flatten)]
    pub wal_stream_options: ProcessObjectWalStreamOptions,
}

pub fn default_max_rows_per_segment() -> usize {
    100000
}

impl Default for ProcessObjectWalOptions {
    fn default() -> Self {
        Self {
            max_rows_per_segment: default_max_rows_per_segment(),
            wal_stream_options: Default::default(),
        }
    }
}

// A clone of WalStreamOptions specific to process_object_wal, so that we can tune the WAL stream
// for this operation separately from others.
#[derive(Debug, Clone, Parser, Serialize, Deserialize)]
pub struct ProcessObjectWalStreamOptions {
    #[arg(
        long,
        value_parser = util::ByteSize::parse_to_u32,
        default_value_t = default_target_batch_size(),
        help = format!("Target size of each batch of WAL entries to stream in parallel (defaults to {})", util::ByteSize::from(default_target_batch_size())),
        env = "BRAINSTORE_PROCESS_OBJECT_WAL_TARGET_BATCH_SIZE",
    )]
    #[serde(default = "default_target_batch_size")]
    pub process_object_wal_target_batch_size: u32,
}

impl Default for ProcessObjectWalStreamOptions {
    fn default() -> Self {
        Self {
            process_object_wal_target_batch_size: default_target_batch_size(),
        }
    }
}

#[derive(Debug, Default, Clone)]
pub struct ProcessObjectWalOutput {
    /// The set of segment IDs that were added to.
    pub modified_segment_ids: HashSet<Uuid>,
}

#[instrument(err, skip(input, optional_input, options), fields(object_id = input.object_id.to_string(), source = optional_input.source, options = ?options))]
pub async fn process_object_wal(
    input: ProcessObjectWalInput<'_>,
    optional_input: ProcessObjectWalOptionalInput,
    options: ProcessObjectWalOptions,
) -> Result<ProcessObjectWalOutput> {
    // Global-level procedure:
    //
    // 1. Acquire a lock on the object-level WAL for this object_id.
    //
    // 2. Read the ObjectMetadata for this object_id from the global store. We will start
    //    processing from min(start_xact_id, last_processed_xact_id + 1), and keep track of the
    //    last xact ID we processed in each loop iteration.
    //
    // Begin loop:
    //
    //      3. Read the WAL entries starting from the next xact_id to process, until we reach the
    //         maximum number of entries, exceed end_xact_id, or reach the end of the WAL.
    //
    //      4. Run the process_object_wal_loaded helper method over the loaded WAL entries. Collect
    //         the set of segment IDs that were modified. If any task fails, abort the task. We
    //         should always be able to re-process the same WAL entries another time if needed.
    //
    // 5. If everything succeeded, update the last-processed xact_id if next_xact_id > the
    //    original last_processed_xact_id.

    let _lock = input
        .config
        .locks_manager
        .write(&WALScope::ObjectId(input.object_id, Uuid::default()).lock_name())
        .instrument(tracing::info_span!("acquire segment lock"))
        .await?;

    let global_store_object_metadata = input
        .config
        .global_store
        .query_object_metadatas(&[input.object_id])
        .instrument(tracing::debug_span!(
            "reading_object_metadata",
            object_id = input.object_id.to_string()
        ))
        .await?
        .remove(0);

    log::debug!(
        "Read last-processed xact_id: {:?}",
        global_store_object_metadata.last_processed_xact_id
    );

    log::debug!(
        "Acquired lock for process_object_wal over object {}",
        input.object_id
    );

    let wal_scope = WALScope::ObjectId(input.object_id, global_store_object_metadata.wal_token);
    let wal = &input.config.wal;
    let mut wal_stream = {
        let next_xact_id = merge_options(
            optional_input.start_xact_id,
            global_store_object_metadata
                .last_processed_xact_id
                .map(|x| TransactionId(x.0 + 1)),
            std::cmp::min,
        );
        wal_stream_generic(
            wal.wal_metadata_stream(
                wal_scope,
                WalMetadataStreamOptionalInput {
                    start_xact_id: next_xact_id,
                    end_xact_id: optional_input.end_xact_id,
                    ..Default::default()
                },
            )
            .await?,
            PreferWalEntrySystemFields,
            WalStreamOptions {
                target_batch_size: options
                    .wal_stream_options
                    .process_object_wal_target_batch_size,
                metrics: Some(WalStreamMetrics {
                    num_rows: PROCESSING_METRICS.num_rows.clone(),
                    num_transactions: PROCESSING_METRICS.num_transactions.clone(),
                    num_bytes: PROCESSING_METRICS.num_bytes.clone(),
                }),
                ..Default::default()
            },
        )
    };

    let mut last_processed_xact_id: Option<TransactionId> = None;
    let mut modified_segment_ids: HashSet<Uuid> = HashSet::new();
    let mut opt_process_object_wal_loaded_testing_sync_points = Some(
        optional_input
            .testing_sync_points
            .process_object_wal_loaded_testing_sync_points,
    );
    log::debug!("Opened WAL stream.");
    while let Some(wal_item) = wal_stream.next().await {
        let all_wal_entries = wal_item
            .item?
            .entries
            .into_iter()
            .map(|e| (e.0, e.1))
            .collect::<Vec<_>>();
        tracing::debug!(
            "Read {} WAL entries. Time to store them in segments.",
            num_wal_entries(&all_wal_entries)
        );

        // We avoid updating the last_processed_xact_id if this batch of WAL entries consists of
        // only standalone comments. This is sort-of a hack to address the issue that comments must
        // be backfilled independently of normal log rows. This can lead to issues with realtime
        // queries in scenarios like the following:
        //
        //   1. Insert a row {id: "x", input: "foo" }.
        //
        //   2. Do a single insert with an update and a comment to that row: [{id: "x", input:
        //      "bar"}, {id: [uuid], comment: { origin: { id: "x" }, ... } }]. Both of these
        //      objects will get transaction id X.
        //
        //   3. The comment happens to get backfilled first. So the last_processed_xact_id for the
        //      segment becomes X.
        //
        //   4. When we do a read, the index_wal_reader only reads rows from the realtime WAL later
        //      than transaction ID X, so it misses the update row and only sees the comment.
        //
        // Technically this scenario could happen with just regular rows, because rows on the same
        // transaction can be backfilled in separate batches, but it is far less likely. By
        // skipping the metadata update for comments-only batches, we avoid this issue.
        //
        // Note: we do the same logic in compaction to avoid the same issue with realtime reading
        // the compacted index vs. the segment WAL.

        let max_batch_xact_id = all_wal_entries
            .iter()
            .map(|x| {
                if x.1.iter().all(|entry| entry.is_standalone_comment()) {
                    None
                } else {
                    Some(x.0)
                }
            })
            .max()
            .flatten();
        last_processed_xact_id =
            merge_options(last_processed_xact_id, max_batch_xact_id, std::cmp::max);

        let output = process_object_wal_loaded(
            ProcessObjectWalLoadedInput {
                orig_input: input.clone(),
                xact_wal_entries: all_wal_entries,
            },
            ProcessObjectWalLoadedOptionalInput {
                testing_sync_points: opt_process_object_wal_loaded_testing_sync_points
                    .take()
                    .unwrap_or_default(),
                mark_as_compacted: optional_input.mark_as_compacted,
            },
            ProcessObjectWalLoadedOptions {
                max_rows_per_segment: options.max_rows_per_segment,
            },
        )
        .await?;
        modified_segment_ids.extend(output.modified_segment_ids);
    }

    if let Some(sync_point) = optional_input
        .testing_sync_points
        .before_update_object_metadata
    {
        sync_point.send_and_wait().await;
    }

    if let Some(last_processed_xact_id) = last_processed_xact_id {
        let global_store_last_processed_xact_id =
            global_store_object_metadata.last_processed_xact_id;
        if Some(last_processed_xact_id) > global_store_last_processed_xact_id
            && !input
                .config
                .global_store
                .upsert_object_metadatas(
                    [(
                        input.object_id,
                        ObjectMetadataUpdate {
                            last_processed_xact_id: Some((
                                global_store_last_processed_xact_id,
                                Some(last_processed_xact_id),
                            )),
                        },
                    )]
                    .into_iter()
                    .collect(),
                )
                .await?
        {
            return Err(anyhow!(
                "global store last_processed_xact_id was concurrently modified by another task"
            ));
        }
    }

    Ok(ProcessObjectWalOutput {
        modified_segment_ids,
    })
}

pub struct ProcessObjectWalLoadedInput<'a> {
    pub orig_input: ProcessObjectWalInput<'a>,
    pub xact_wal_entries: Vec<(TransactionId, Vec<WalEntryVariant>)>,
}

#[derive(Default)]
pub struct ProcessObjectWalLoadedTestingSyncPoints {
    pub after_collect_segment_infos: Option<TwoWaySyncPointSendAndWait>,
}

#[derive(Default)]
pub struct ProcessObjectWalLoadedOptionalInput {
    pub testing_sync_points: ProcessObjectWalLoadedTestingSyncPoints,
    pub mark_as_compacted: bool,
}

#[derive(Debug, Clone)]
pub struct ProcessObjectWalLoadedOptions {
    pub max_rows_per_segment: usize,
}

#[derive(Debug, Clone)]
pub struct ProcessObjectWalLoadedOutput {
    pub modified_segment_ids: HashSet<Uuid>,
}

#[instrument(
    err,
    skip(input, optional_input, options),
    fields(object_id = input.orig_input.object_id.to_string(),
    num_wal_entries = num_wal_entries(&input.xact_wal_entries))
)]
async fn process_object_wal_loaded(
    input: ProcessObjectWalLoadedInput<'_>,
    optional_input: ProcessObjectWalLoadedOptionalInput,
    options: ProcessObjectWalLoadedOptions,
) -> Result<ProcessObjectWalLoadedOutput> {
    // Object-level procedure:
    //
    // 1. Verify that all wal entries belong to the given object id.
    //
    // 2. Grab the list of live segments for this object.
    //
    // 3. In parallel, query for all documents matching any of the FullRowIds
    // out of the set of WalEntries. Collect the set of matching { full_row_id }
    // and the set of their corresponding { root_span_id }. Also return the
    // total number of docs in the segment, as well as the minimum WAL xact_id
    // for the segment.
    //
    // 4. Collect the results into a mapping { segment_id -> SEGMENT INFO }.
    // Check that no full_row_id / root_span_id appears in more than one
    // segment_id, and that the minimum WAL xact_ids are unique.
    //
    // 5. Allocate any rows in the WalEntries to a segment. We can allocate rows
    // which match by FullRowId or by root_span_id. Prefer matching by FullRowId
    // (ignoring any inconsistencies between the WalEntry's root_span_id and the
    // retrieved one, which we can resolve during compaction).
    //
    // 6. Any un-allocated rows belong to new traces. We can now spill these
    // rows into the segment with the greatest minimum WAL xact_id. If that
    // segment is full, we spill everything over to a new segment.
    //
    // 7. Now we have a mapping { segment_id -> [(TransactionId, [WalEntry])] }.
    // We can launch parallel tasks to add these entries to the various
    // segments.
    //
    // 8. If we added a new segment, add it to the global store.
    //
    // 9. Return the set of modified segment IDs.
    check_xact_wal_entries(&input.xact_wal_entries)?;
    let object_id = input.orig_input.object_id;
    for (_, wal_entries) in &input.xact_wal_entries {
        for wal_entry in wal_entries {
            if wal_entry.full_object_id() != object_id {
                return Err(anyhow!(
                    "All WAL entries must belong to object {}",
                    object_id
                ));
            }
        }
    }

    let input_num_wal_entries = num_wal_entries(&input.xact_wal_entries);
    log::debug!("Verified {} WAL entries.", input_num_wal_entries);

    let segment_ids = input
        .orig_input
        .config
        .global_store
        .list_segment_ids(&[object_id], None)
        .await?
        .remove(0);
    let (mut row_id_segment_membership, mut root_span_id_segment_membership, segment_id_to_info) =
        collect_segment_infos(&input.orig_input, &segment_ids, &input.xact_wal_entries).await?;
    let orig_row_id_segment_membership = row_id_segment_membership.clone();
    let orig_root_span_id_segment_membership = root_span_id_segment_membership.clone();

    if let Some(after_collect_segment_infos) = optional_input
        .testing_sync_points
        .after_collect_segment_infos
    {
        after_collect_segment_infos.send_and_wait().await;
    }

    log::debug!("Collected info for {} segments.", segment_ids.len());

    // Allocate WalEntries to each segment.
    let mut segment_allocation: HashMap<Uuid, Vec<(TransactionId, Vec<WalEntryVariant>)>> =
        HashMap::new();
    let mut leftover_wal_entries: Vec<(TransactionId, Vec<WalEntryVariant>)> = Vec::new();
    for (xact_id, wal_entries) in input.xact_wal_entries {
        let mut this_segment_allocation: HashMap<Uuid, Vec<WalEntryVariant>> = HashMap::new();
        let mut this_leftover_wal_entries: Vec<WalEntryVariant> = Vec::new();
        for mut wal_entry in wal_entries {
            let full_row_id = wal_entry.full_row_id().to_owned();
            let mut full_root_span_id = wal_entry.full_root_span_id().to_owned();

            // In some cases, a WAL entry may have a root span ID which points to one segment and a
            // row ID which points to a different segment. In this case, we force-override the
            // root_span_id to the one available from the row ID path, to avoid a situation where a
            // root span ID occurs in multiple segments (which would make it difficult to allocate
            // future spans with this root_span_id to a segment).
            //
            // In general this should be an anomaly since we expect root_span_ids either match a
            // consistent trace or are unique UUIDs which have not occurred before.
            if let (Some(row_id_segment_id), Some(root_span_id_segment_id)) = (
                row_id_segment_membership.get(&full_row_id).cloned(),
                root_span_id_segment_membership
                    .get(&full_root_span_id)
                    .cloned(),
            ) {
                if row_id_segment_id != root_span_id_segment_id {
                    log::warn!(
                        "WAL entry points to segment id {} through its row id ({}) and segment id {} through its root_span_id ({}). To maintain consistency, will force-override the root_span_id to a unique value",
                        row_id_segment_id,
                        full_row_id.id,
                        root_span_id_segment_id,
                        full_root_span_id,
                    );
                    let mut modified_wal_entry =
                        std::mem::take(&mut wal_entry).to_wal_entry().await?;
                    modified_wal_entry.root_span_id = Uuid::new_v4().to_string();
                    full_root_span_id = modified_wal_entry.full_root_span_id().to_owned();
                    wal_entry = WalEntryVariant::Full(modified_wal_entry);
                }
            }
            if let Some(segment_id) = row_id_segment_membership.get(&full_row_id) {
                // Add it to the root_span_id_segment_membership mapping as well, so that future
                // occurrences of the root span in the WAL will be allocated to the same segment.
                root_span_id_segment_membership.insert(full_root_span_id.clone(), *segment_id);
                this_segment_allocation
                    .entry(*segment_id)
                    .or_default()
                    .push(wal_entry);
            } else if let Some(segment_id) = root_span_id_segment_membership.get(&full_root_span_id)
            {
                // Add it to the row_id_segment_membership mapping as well, so that future
                // occurrences of the row in the WAL will be allocated to the same segment.
                row_id_segment_membership.insert(full_row_id.clone(), *segment_id);
                this_segment_allocation
                    .entry(*segment_id)
                    .or_default()
                    .push(wal_entry);
            } else {
                this_leftover_wal_entries.push(wal_entry);
            }
        }
        for (segment_id, wal_entries) in this_segment_allocation {
            segment_allocation
                .entry(segment_id)
                .or_default()
                .push((xact_id, wal_entries));
        }
        if !this_leftover_wal_entries.is_empty() {
            leftover_wal_entries.push((xact_id, this_leftover_wal_entries));
        }
    }

    log::debug!(
        "Mapped {} WAL entries to {} segments ({} leftover).",
        input_num_wal_entries,
        segment_allocation.len(),
        num_wal_entries(&leftover_wal_entries),
    );

    // Process the leftover entries. Return some metadata info about the leftover entries that we
    // use to update the global store.
    //
    let leftover_segment_info: Option<LeftoverSegmentInfo> = (|| {
        if leftover_wal_entries.is_empty() {
            return None;
        }

        let minimum_pagination_key = leftover_wal_entries
            .iter()
            .flat_map(|(_, x)| x.iter().map(|x| x.pagination_key()))
            .min()
            .unwrap();

        let mut row_ids: HashSet<FullRowIdOwned> = HashSet::new();
        let mut root_span_ids: HashSet<FullRowIdOwned> = HashSet::new();
        for (_, wal_entries) in &leftover_wal_entries {
            for wal_entry in wal_entries {
                row_ids.insert(wal_entry.full_row_id().to_owned());
                root_span_ids.insert(wal_entry.full_root_span_id().to_owned());
            }
        }

        // Try to fit the leftover entries into existing segments, ordered by greatest
        // minimum_pagination_key to least. This will heuristically ensure that segments are
        // roughly clustered by pagination key ranges, but will still try to fill each existing
        // segment to capacity before creating a new one.
        let mut segment_ids_by_min_pagination_key = segment_id_to_info
            .iter()
            .map(|(segment_id, info)| (segment_id, info.minimum_pagination_key))
            .collect::<Vec<_>>();
        segment_ids_by_min_pagination_key.sort_by(|(_, key1), (_, key2)| key2.cmp(key1));
        let (segment_id, is_new_segment) = (|| {
            for (segment_id, _) in segment_ids_by_min_pagination_key {
                if segment_id_to_info.get(segment_id).unwrap().num_rows
                    < options.max_rows_per_segment as u64
                {
                    // Merge the segment allocation for this segment and the leftover entries. Since the
                    // streams are sorted we can do a "merge join".
                    let allocation_ref = segment_allocation.entry(*segment_id).or_default();
                    let mut existing_allocation: Vec<(TransactionId, Vec<WalEntryVariant>)> =
                        Vec::new();
                    existing_allocation.append(allocation_ref);
                    *allocation_ref = merge_wal_streams(existing_allocation, leftover_wal_entries);
                    return (*segment_id, false);
                }
            }
            let new_segment_id = uuid::Uuid::new_v4();
            segment_allocation.insert(new_segment_id, leftover_wal_entries);
            (new_segment_id, true)
        })();

        Some(LeftoverSegmentInfo {
            segment_id,
            is_new_segment,
            row_ids,
            root_span_ids,
            minimum_pagination_key,
        })
    })();

    if let Some(info) = &leftover_segment_info {
        // Associate the leftover row_ids and root_span_ids to the segment we added them to.
        for row_id in &info.row_ids {
            row_id_segment_membership.insert(row_id.clone(), info.segment_id);
        }
        for root_span_id in &info.root_span_ids {
            root_span_id_segment_membership.insert(root_span_id.clone(), info.segment_id);
        }

        // Create an initial segment metadata entry for the new segment.
        if info.is_new_segment
            && !input
                .orig_input
                .config
                .global_store
                .upsert_segment_metadatas(
                    [(
                        info.segment_id,
                        SegmentMetadataUpdate {
                            minimum_pagination_key: Some((
                                PaginationKey(0),
                                info.minimum_pagination_key,
                            )),
                            ..Default::default()
                        },
                    )]
                    .into_iter()
                    .collect(),
                )
                .await?
        {
            return Err(anyhow!(
                "minimum_pagination_key for new segment was concurrently modified by another task"
            ));
        }
    }

    // Kick off tasks to add the segment_id -> row_ids/root_span_ids mappings to
    // the global store. Run these in parallel with writing the data to the
    // various segments, but wait to commit the WAL entry metadata until after
    // the row/root_span_id mappings have been committed. This guarantees that
    // if adding the mapping fails, we won't have inadvertently added visible
    // WAL entries to the wrong segment.
    let row_id_fut = {
        let global_store = input.orig_input.config.global_store.clone();
        tokio::spawn(
            async move {
                let segment_to_row_ids = row_id_segment_membership
                    .iter()
                    .filter(|(k, _)| !orig_row_id_segment_membership.contains_key(*k))
                    .map(|(k, v)| (*v, k.as_ref()))
                    .into_group_map();
                let segment_id_to_num_added_rows: HashMap<Uuid, usize> = segment_to_row_ids
                    .iter()
                    .map(|(k, v)| (*k, v.len()))
                    .collect();
                global_store
                    .add_id_segment_membership(IdSegmentMembershipType::RowId, segment_to_row_ids)
                    .await?;
                Ok::<_, util::anyhow::Error>(segment_id_to_num_added_rows)
            }
            .instrument(tracing::info_span!("add_segment_row_id_entries")),
        )
    };
    let root_span_id_fut = {
        let global_store = input.orig_input.config.global_store.clone();
        tokio::spawn(
            async move {
                let segment_to_root_span_ids = root_span_id_segment_membership
                    .iter()
                    .filter(|(k, _)| !orig_root_span_id_segment_membership.contains_key(*k))
                    .map(|(k, v)| (*v, k.as_ref()))
                    .into_group_map();
                global_store
                    .add_id_segment_membership(
                        IdSegmentMembershipType::RootSpanId,
                        segment_to_root_span_ids,
                    )
                    .await
            }
            .instrument(tracing::info_span!("add_segment_root_span_id_entries")),
        )
    };

    // Launch parallel tasks to add the entries to the various segments. We
    // defer committing the segment metadata until the row/root_span_id mappings
    // have successfully been committed.
    let modified_segment_ids: HashSet<Uuid> = segment_allocation.keys().cloned().collect();
    let all_upsert_segment_wal_entries = join_all(segment_allocation.into_iter().map(
        |(segment_id, xact_wal_entries)| {
            let orig_input = input.orig_input.clone();
            let leftover_segment_info = &leftover_segment_info;
            let is_new_segment = if let Some(info) = leftover_segment_info {
                info.is_new_segment && info.segment_id == segment_id
            } else {
                false
            };
            process_segment_wal(
                ProcessSegmentWalInput {
                    orig_input,
                    segment_id,
                    xact_wal_entries,
                    is_new_segment,
                },
                ProcessSegmentWalOptionalInput {
                    mark_as_compacted: optional_input.mark_as_compacted,
                },
            )
        },
    ))
    .await;
    let mut upsert_segment_wal_entries: HashMap<Uuid, HashMap<Uuid, Vec<UpsertSegmentWalEntry>>> =
        HashMap::new();
    for item in all_upsert_segment_wal_entries {
        let segment_id_entries = item?;
        for (segment_id, wal_entries) in segment_id_entries {
            if upsert_segment_wal_entries
                .insert(segment_id, wal_entries)
                .is_some()
            {
                panic!(
                    "Unexpected: multiple subtasks inserting WAL entries into segment {}",
                    segment_id
                )
            }
        }
    }

    // Before we commit the WAL entries, commit the row_id
    // and root_span_id operations.
    let (segment_id_to_num_added_rows, root_span_id_res) = join!(row_id_fut, root_span_id_fut,);
    let segment_id_to_num_added_rows = segment_id_to_num_added_rows??;
    root_span_id_res??;

    input
        .orig_input
        .config
        .global_store
        .upsert_segment_wal_entries(upsert_segment_wal_entries)
        .await?;

    log::debug!(
        "Processed WAL data into {} segments",
        modified_segment_ids.len()
    );

    // Propagate the added rows to the global store segment metadatas.
    if !segment_id_to_num_added_rows.is_empty()
        && !input
            .orig_input
            .config
            .global_store
            .upsert_segment_metadatas(
                segment_id_to_num_added_rows
                    .iter()
                    .map(|(segment_id, num_added_rows)| {
                        (
                            *segment_id,
                            SegmentMetadataUpdate {
                                add_num_rows: Some(*num_added_rows as u64),
                                ..Default::default()
                            },
                        )
                    })
                    .collect(),
            )
            .await?
    {
        return Err(anyhow!("Failed to incrument num_rows for segments"));
    }

    // If we added a new segment, mark it as live in the global store.
    if let Some(info) = &leftover_segment_info {
        if info.is_new_segment {
            input
                .orig_input
                .config
                .global_store
                .update_segment_ids(&[(object_id, &[info.segment_id], &[])])
                .await?;
        }
    }

    Ok(ProcessObjectWalLoadedOutput {
        modified_segment_ids,
    })
}

pub struct ProcessSegmentWalInput<'a> {
    pub orig_input: ProcessObjectWalInput<'a>,
    pub segment_id: Uuid,
    pub xact_wal_entries: Vec<(TransactionId, Vec<WalEntryVariant>)>,
    // If true, this is a new segment, not expected to be globally visible yet.
    pub is_new_segment: bool,
}

#[derive(Default)]
pub struct ProcessSegmentWalOptionalInput {
    pub mark_as_compacted: bool,
}

#[instrument(err, skip(input, optional_input), fields(segment_id = %input.segment_id))]
async fn process_segment_wal(
    input: ProcessSegmentWalInput<'_>,
    optional_input: ProcessSegmentWalOptionalInput,
) -> Result<HashMap<Uuid, HashMap<Uuid, Vec<UpsertSegmentWalEntry>>>> {
    // Segment-level procedure:
    //
    // 1. Write all the WAL entries as one batch into the segment. It's possible
    // that we are overwriting/duplicating entries from past (failed) runs. This
    // is fine, since the data coming from upstream should be the same. Instead
    // of committing the new WAL metadata entries to the global store, return
    // them so the caller can commit them at the right time.

    check_xact_wal_entries(&input.xact_wal_entries)?;
    let wal_scope = WALScope::Segment(input.segment_id);

    let segment_index_wal = input.orig_input.config.segment_index_wal();
    let all_wal_entries: Vec<WalEntryVariant> = input
        .xact_wal_entries
        .into_iter()
        .flat_map(|(_, wal_entries)| wal_entries)
        .collect();
    PROCESSING_METRICS
        .segment_num_rows
        .add(all_wal_entries.len() as u64, &[]);
    let res = segment_index_wal
        .insert_no_commit_metadata(
            wal_scope,
            all_wal_entries,
            ObjectAndGlobalStoreWalInsertOptionalInput {
                is_compacted: optional_input.mark_as_compacted,
                ..Default::default()
            },
        )
        .await?;
    Ok(res)
}

#[derive(Clone)]
pub struct CompactSegmentWalInput {
    pub segment_id: Uuid,
    pub index_store: StoreInfo,
    pub schema: util::schema::Schema,
    pub global_store: Arc<dyn GlobalStore>,
    pub locks_manager: Arc<dyn GlobalLocksManager>,
}

#[derive(Default, Debug)]
pub struct CompactSegmentWalOptionalInput {
    pub testing_sync_points: CompactSegmentWalTestingSyncPoints,

    // If true, initialize and send updates about the progress of the compaction with a
    // StatusUpdater.
    pub use_status_updater: bool,

    // We allow re-compacting over a custom range of xact ids, instead of the default of starting
    // with the earliest un-compacted xact id and continuing to the end of the WAL. The motivation
    // is the same as for the start/end_xact_id inputs in ProcessObjectWalOptionalInput.
    //
    // Start reading from the WAL at min(start_xact_id, earliest_uncompacted_xact_id,
    // last_compacted_xact_id + 1).
    pub start_xact_id: Option<TransactionId>,

    /// If specified, stop reading from the WAL at max(end_xact_id, latest_compacted_xact_id)
    /// (inclusive). Note that we cannot stop before last_compacted_xact_id, otherwise our
    /// compacted index will be in an inconsistent state with respect to the recorded
    /// last_compacted_xact_id.
    pub end_xact_id: Option<TransactionId>,

    // If true, will try to acquire the lock for the segment, and return quickly if we can't.
    pub try_acquire: bool,

    pub testing_skip_field_statistics: bool,
    pub testing_override_missing_del_file_retries: Option<usize>,
}

#[derive(Default, Debug)]
pub struct CompactSegmentWalTestingSyncPoints {
    pub after_acquire_index_lock: Option<TwoWaySyncPointSendAndWait>,
}

#[derive(Debug, Clone, Parser, Serialize, Deserialize)]
pub struct CompactSegmentWalOptions {
    /// Configuration options for writing to the segment index.
    #[command(flatten)]
    #[serde(flatten)]
    pub writer_opts: TantivyIndexWriterOpts,

    #[command(flatten)]
    #[serde(flatten)]
    pub wal_stream_options: CompactSegmentWalStreamOptions,

    /// Sleep for this duration in milliseconds after acquiring the lock.
    #[arg(long, env = "BRAINSTORE_COMPACT_SEGMENT_WAL_SLEEP_AFTER_LOCK_MS")]
    pub compact_segment_wal_sleep_after_lock_ms: Option<usize>,
}

impl Default for CompactSegmentWalOptions {
    fn default() -> Self {
        Self {
            writer_opts: TantivyIndexWriterOpts::default(),
            wal_stream_options: Default::default(),
            compact_segment_wal_sleep_after_lock_ms: None,
        }
    }
}

// A clone of WalStreamOptions specific to compact_segment_wal, so that we can tune the WAL stream
// for this operation separately from others.
#[derive(Debug, Clone, Parser, Serialize, Deserialize)]
pub struct CompactSegmentWalStreamOptions {
    #[arg(
        long,
        value_parser = util::ByteSize::parse_to_u32,
        default_value_t = default_target_batch_size(),
        help = format!("Target size of each batch of WAL entries to stream in parallel (defaults to {})", util::ByteSize::from(default_target_batch_size())),
        env = "BRAINSTORE_COMPACT_SEGMENT_WAL_TARGET_BATCH_SIZE",
    )]
    #[serde(default = "default_target_batch_size")]
    pub compact_segment_wal_target_batch_size: u32,
}

impl Default for CompactSegmentWalStreamOptions {
    fn default() -> Self {
        Self {
            compact_segment_wal_target_batch_size: default_target_batch_size(),
        }
    }
}

#[derive(Debug, Clone, Default)]
pub struct CompactSegmentWalOutput {
    pub num_wal_entries_compacted: usize,
}

#[instrument(err, skip(input, optional_input, options), fields(segment_id = %input.segment_id, optional_input = ?optional_input, options = ?options))]
pub async fn compact_segment_wal(
    input: CompactSegmentWalInput,
    optional_input: CompactSegmentWalOptionalInput,
    options: CompactSegmentWalOptions,
) -> Result<CompactSegmentWalOutput> {
    let harness_input = WriteIndexOperationHarnessInput {
        segment_id: input.segment_id,
        index_store: input.index_store.clone(),
        schema: Some(input.schema.clone()),
        global_store: input.global_store.clone(),
        locks_manager: input.locks_manager.clone(),
        validate_opts: options.writer_opts.validate_opts.clone(),
        index_type: crate::write_index_operation_harness::IndexOperationType::Compact,
    };
    let harness_optional_input = WriteIndexOperationHarnessOptionalInput {
        use_status_updater: optional_input.use_status_updater,
        try_acquire: optional_input.try_acquire,
        testing_override_missing_del_file_retries: optional_input
            .testing_override_missing_del_file_retries,
        sleep_after_acquire_lock_ms: options.compact_segment_wal_sleep_after_lock_ms,
        testing_sync_points: WriteIndexOperationHarnessTestingSyncPoints {
            after_acquire_index_lock: optional_input.testing_sync_points.after_acquire_index_lock,
        },
        ..Default::default()
    };
    let task_input = CompactSegmentWalInnerInput {
        input: input.clone(),
        start_xact_id: optional_input.start_xact_id,
        end_xact_id: optional_input.end_xact_id,
        testing_skip_field_statistics: optional_input.testing_skip_field_statistics,
    };
    let task_options = CompactSegmentWalInnerOptions {
        writer_opts: options.writer_opts,
        wal_stream_options: options.wal_stream_options,
    };
    let task_noop_output = CompactSegmentWalOutput::default();
    write_index_operation_harness(
        harness_input,
        harness_optional_input,
        compact_segment_wal_inner_boxed,
        task_input,
        task_options,
        task_noop_output,
    )
    .await
}

#[derive(Clone)]
struct CompactSegmentWalInnerInput {
    input: CompactSegmentWalInput,
    // Optional inputs
    pub start_xact_id: Option<TransactionId>,
    pub end_xact_id: Option<TransactionId>,
    pub testing_skip_field_statistics: bool,
}

#[derive(Clone)]
struct CompactSegmentWalInnerOptions {
    pub writer_opts: TantivyIndexWriterOpts,
    pub wal_stream_options: CompactSegmentWalStreamOptions,
}

async fn compact_segment_wal_inner(
    full_input: CompactSegmentWalInnerInput,
    options: CompactSegmentWalInnerOptions,
    additional_input: WriteIndexOperationHarnessTaskAdditionalInput<'_>,
) -> Result<CompactSegmentWalOutput> {
    // Procedure:
    //
    // 1. Find the earliest
    //    un-compacted xact id for this segment by querying the global store. We will start
    //    compacting from min(start_xact_id, earliest_uncompacted_xact_id, last_compacted_xact_id +
    //    1). If end_xact_id is specified, we will stop compacting at max(end_xact_id,
    //    last_compacted_xact_id).
    //
    // 2. Open up a tantivy index and WAL reader on this segment.
    //
    // Begin loop:
    //
    //      3. Iterate over the WAL entries starting from the next xact_id to process, up to the
    //         maximum number of WAL entries per iteration, or up to end_xact_id. As we go along,
    //         we merge WAL entries on the same FullRowId.
    //
    //      4. With the complete set of WALs, we should have one per FullRowId. Search for all
    //         existing documents matching one of the FullRowIds.
    //
    //      5. For each matching document, convert it to an IndexDocument and merge it with the
    //         corresponding WAL entry. Remove the document from the store and re-add the
    //         newly-merged version.
    //
    //      6. For all remaining rows, create fresh IndexDocuments and add them to the store.
    //
    //      7. If we have compacted up to the last_compacted_index_meta.xact_id we read from the
    //         global store, jointly update the last_compacted_index_meta and field statistics for
    //         this segment. Next, mark all WAL entries we read in the loop as compacted, so that
    //         we don't consider them next time compaction runs.
    //
    //         The reason we need to guard this check by the global store's last_compacted_xact_id
    //         is as follows. Imagine we first run compaction from xact ids [0, 10]. Then we
    //         process some more WAL entries between [0, 5] and re-run compaction from [0, 10]. If
    //         we were to just go through xacts 0-5 in the first loop and update our metadata, then
    //         readers would see an inconsistent snapshot of the data between xacts 5-10.
    //
    //      8. If we have reached the end of the WAL stream, break out of the loop.
    //
    // End loop.
    //
    // 9. If we have exited the loop without processing any WAL entries (0 loop iterations) and the
    //    segment metadata is non-empty, check if any field statistics on the segment are null, and
    //    update them if so. This check is necessary for backfilling a segment which was created
    //    before we introduced any particular set of field statistics.
    let CompactSegmentWalInnerInput {
        input,
        start_xact_id: input_start_xact_id,
        end_xact_id: input_end_xact_id,
        testing_skip_field_statistics,
    } = full_input;

    tracing::info!(
        "Acquired lock for segment. Time to start compaction. {}",
        input.segment_id
    );

    let mut global_store_last_compacted_index_meta =
        additional_input.global_store_last_compacted_index_meta;
    let status_updater = additional_input.status_updater;
    let global_store_earliest_uncompacted_xact_id = {
        let segment_id_arr = [input.segment_id];
        let mut min_uncompacted_xact_id = input
            .global_store
            .query_segment_wal_min_uncompacted_xact_id(&segment_id_arr)
            .await?;
        min_uncompacted_xact_id.remove(0)
    };

    status_updater.update(LastIndexOperation {
        stage: Some("preparing WAL stream".to_string()),
        ..Default::default()
    });

    let index_store = input.index_store;
    let index_scope = TantivyIndexScope::Segment(input.segment_id);
    let tantivy_index_wrapper = ReadWriteTantivyIndexWrapper::new(
        index_store.directory.clone(),
        input.schema.clone(),
        &index_store.prefix,
        &index_scope,
    )
    .await?;
    let full_row_id_field = tantivy_index_wrapper
        .tantivy_schema()
        .get_field("_full_row_id")?;
    let segment_index_wal = ObjectAndGlobalStoreWal {
        object_store: index_store.store.clone(),
        global_store: input.global_store.clone(),
        directory: index_store.directory.clone(),
        store_prefix: index_store.prefix.clone(),
        store_type: index_store.store_type,
    };

    let next_xact_id = merge_options(
        input_start_xact_id,
        global_store_earliest_uncompacted_xact_id,
        std::cmp::min,
    );

    // Store the initial compaction delay info to record later when we know the object_type
    let initial_compaction_delay =
        next_xact_id.map(|xact_id| (xact_id, xact_id.duration_since_now()));

    let mut wal_stream = {
        let end_xact_id =
            input_end_xact_id.map(|x| match &global_store_last_compacted_index_meta {
                Some(y) => std::cmp::max(x, y.xact_id),
                None => x,
            });
        let wal_metadata_stream = match next_xact_id {
            Some(next_xact_id) => {
                segment_index_wal
                    .wal_metadata_stream_concrete(
                        WALScope::Segment(input.segment_id),
                        WalMetadataStreamOptionalInput {
                            start_xact_id: Some(next_xact_id),
                            end_xact_id,
                        },
                    )
                    .await?
            }
            None => stream::empty().boxed(),
        };
        wal_stream_generic(
            wal_metadata_stream,
            PreferWalEntry,
            WalStreamOptions {
                target_batch_size: options
                    .wal_stream_options
                    .compact_segment_wal_target_batch_size,
                ..Default::default()
            },
        )
    };

    status_updater.update(LastIndexOperation {
        stage: Some("preparing index state".to_string()),
        ..Default::default()
    });
    let mut index_state = {
        let tantivy_index_wrapper = &tantivy_index_wrapper;
        let writer_opts = &options.writer_opts;
        await_spawn_blocking!(
            move |tantivy_index_wrapper: &ReadWriteTantivyIndexWrapper,
                  writer_opts: &TantivyIndexWriterOpts| {
                Ok::<IndexState, util::anyhow::Error>(IndexState {
                    writer: tantivy_index_wrapper.make_writer_blocking(writer_opts)?,
                    reader: tantivy_index_wrapper.make_reader_blocking()?,
                })
            },
            tantivy_index_wrapper,
            writer_opts
        )???
    };

    let progress_counter = ProgressCounter::new(&status_updater);
    let mut total_num_wal_entries = 0;
    // We distinguish between the last xact_id we encountered in the raw stream, and the last xact
    // ID we encountered that we want to publish, mainly for the sake of standalone comments.
    let mut last_xact_id_raw: Option<TransactionId> = None;
    let mut last_xact_id_to_publish: Option<TransactionId> = None;
    let mut object_type: Option<util::system_types::ObjectType> = None;
    let mut object_id: Option<String> = None;
    let mut delay_recorded = false;
    // The pending_compacted_entries are the WAL entries that we have compacted but not yet marked
    // as `is_compacted` in the global store. They may roll over across WAL stream iterations, in
    // case we do not complete the 'metadata_update section because we haven't caught up to the
    // last_compacted_xact_id yet.
    let mut pending_compacted_entries: Vec<(Uuid, TransactionId, Uuid)> = Vec::new();

    status_updater.update(LastIndexOperation {
        stage: Some("starting compaction".to_string()),
        ..Default::default()
    });
    while let Some(full_entry) = wal_stream.next().await {
        let full_entry = full_entry.item?;

        status_updater.update(LastIndexOperation {
            stage: Some("processing WAL entries".to_string()),
            estimated_progress: Some(0.0),
            ..Default::default()
        });

        // Similar to process_object_wal, we skip updating the metadata xact_id for WAL batches
        // that consist only of standalone comments. See comment in process_object_wal for details.
        let max_xact_id_raw = full_entry.entries.iter().map(|x| x.0).max();
        let max_xact_id_to_publish = full_entry
            .entries
            .iter()
            .map(|x| {
                if x.1
                    .iter()
                    .all(|entry| entry._is_standalone_comment.unwrap_or(false))
                {
                    None
                } else {
                    Some(x.0)
                }
            })
            .max()
            .flatten();
        last_xact_id_raw = merge_options(last_xact_id_raw, max_xact_id_raw, std::cmp::max);
        last_xact_id_to_publish = merge_options(
            last_xact_id_to_publish,
            max_xact_id_to_publish,
            std::cmp::max,
        );
        pending_compacted_entries.extend(
            full_entry
                .entries
                .iter()
                .flat_map(|(_, _, metadatas)| {
                    metadatas
                        .iter()
                        .map(|metadata| (input.segment_id, metadata.xact_id, metadata.wal_filename))
                })
                .collect::<HashSet<_>>(),
        );
        let mut merged_wal_entries: HashMap<FullRowIdOwned, WalEntry> = HashMap::new();
        let mut new_wal_entries = 0;
        for (_, wal_entries, _) in full_entry.entries {
            for wal_entry in wal_entries {
                new_wal_entries += 1;
                // Capture object_type and object_id from the first WAL entry we see
                if object_type.is_none() {
                    object_type = Some(wal_entry._object_type);
                    object_id = Some(wal_entry._object_id.to_string());
                }
                let full_row_id = wal_entry.full_row_id().to_owned();
                merged_wal_entries
                    .entry(full_row_id)
                    .and_modify(|existing_entry| {
                        // This should never fail, since we are merging entries with the same
                        // FullRowId.
                        existing_entry.merge(wal_entry.clone()).unwrap()
                    })
                    .or_insert(wal_entry);
            }
        }

        progress_counter.add_total(new_wal_entries);
        total_num_wal_entries += new_wal_entries;

        // For tracking purposes, it's useful to include the object_type as an otel attribute so we
        // can track how well we do on experiments vs. logs.
        if !delay_recorded {
            if let (Some((xact_id, delay_duration)), Some(obj_type)) =
                (&initial_compaction_delay, &object_type)
            {
                let delay_seconds = delay_duration.as_secs();
                COMPACTION_METRICS
                    .ingest_to_compaction_delay_seconds
                    .record(
                        delay_seconds,
                        &[KeyValue::new("object_type", obj_type.to_string())],
                    );
                // < 1000000000 helps us avoid printing this message spuriously during tests where
                // we use transaction id values like 1, 2, 3, 4...
                if delay_seconds > SLOW_COMPACTION_THRESHOLD_SECS && delay_seconds < 1000000000 {
                    tracing::warn!(
                    "Slow compaction for segment {} ({}:{}) with first uncompacted transaction ID {} (delay: {} seconds)",
                    input.segment_id,
                    obj_type,
                    object_id.as_ref().unwrap_or(&"unknown".to_string()),
                    xact_id,
                    delay_seconds,
                );
                }
                tracing::debug!(
                    "Compacting segment {} ({}:{}) with first uncompacted transaction ID {} (delay: {} seconds)",
                    input.segment_id,
                    obj_type,
                    object_id.as_ref().unwrap_or(&"unknown".to_string()),
                    xact_id,
                    delay_seconds,
                );
                delay_recorded = true;
            }
        }

        {
            let tantivy_index_wrapper = &tantivy_index_wrapper;
            let writer_opts = &options.writer_opts;
            let progress_counter = &progress_counter;
            let index_state = &mut index_state;
            await_spawn_blocking!(
                move |tantivy_index_wrapper: &ReadWriteTantivyIndexWrapper,
                      writer_opts: &TantivyIndexWriterOpts,
                      progress_counter: &ProgressCounter,
                      index_state: &mut IndexState| {
                    merge_wal_entries_into_segment(
                        input.segment_id,
                        tantivy_index_wrapper,
                        merged_wal_entries,
                        full_row_id_field,
                        writer_opts,
                        index_state,
                        progress_counter,
                    )
                },
                tantivy_index_wrapper,
                writer_opts,
                progress_counter;
                index_state
            )???;
        }

        // Update segment metadata and field statistics if we compacted anything and have made it
        // past the previous last_compacted_index_meta.xact_id.
        'metadata_update: {
            // We use this to determine if we have compacted enough to update the metadata.
            let last_xact_id_raw = match last_xact_id_raw {
                Some(x) => x,
                None => break 'metadata_update,
            };
            // We use this as the xact_id to publish for the metadata update.
            let last_xact_id_to_publish_resolved = last_xact_id_to_publish
                .or(global_store_last_compacted_index_meta
                    .as_ref()
                    .map(|x| x.xact_id))
                .unwrap_or(TransactionId(0));
            if let Some(global_store_last_compacted_index_meta) =
                &global_store_last_compacted_index_meta
            {
                if last_xact_id_raw < global_store_last_compacted_index_meta.xact_id {
                    break 'metadata_update;
                }
            }
            let prev_last_compacted_index_meta = global_store_last_compacted_index_meta.take();
            let next_last_compacted_index_meta = update_metadata_and_field_statistics(
                input.global_store.as_ref(),
                &tantivy_index_wrapper,
                &index_state,
                input.segment_id,
                &index_store,
                &index_scope,
                status_updater,
                prev_last_compacted_index_meta,
                last_xact_id_to_publish_resolved,
                testing_skip_field_statistics,
                &options.writer_opts,
            )
            .await?;
            input
                .global_store
                .update_segment_wal_entries_is_compacted_non_atomic(
                    &pending_compacted_entries,
                    true,
                )
                .await?;
            pending_compacted_entries.clear();
            global_store_last_compacted_index_meta = Some(next_last_compacted_index_meta);
        }
    }

    // Sanity check that we got past the original last_compacted_xact_id if we compacted anything.
    if let Some(last_xact_id) = last_xact_id_raw {
        if let Some(global_store_last_compacted_index_meta) =
            &global_store_last_compacted_index_meta
        {
            assert!(last_xact_id >= global_store_last_compacted_index_meta.xact_id,
                    "segment_id: {} last_xact_id: {} global_store_last_compacted_index_meta.xact_id: {}",
                    input.segment_id, last_xact_id, global_store_last_compacted_index_meta.xact_id);
        }
    }

    // Recompute any null field statistics if we didn't process any WAL entries.
    'backfill_field_statistics: {
        if testing_skip_field_statistics {
            break 'backfill_field_statistics;
        }
        if total_num_wal_entries > 0 {
            break 'backfill_field_statistics;
        }
        let current_field_statistics = input
            .global_store
            .query_field_statistics(&[input.segment_id], &FIELD_STATISTICS_FIELD_NAMES)
            .await?;
        if current_field_statistics
            .values()
            .map(|x| x.len())
            .sum::<usize>()
            == FIELD_STATISTICS_FIELD_NAMES.len()
        {
            break 'backfill_field_statistics;
        }
        status_updater.update(LastIndexOperation {
            stage: Some("backfilling field statistics".to_string()),
            ..Default::default()
        });
        let field_statistics = recompute_field_statistics(&index_state)
            .await?
            .map(|(field_name, field_statistics)| (input.segment_id, field_name, field_statistics));
        input
            .global_store
            .upsert_field_statistics(field_statistics.to_vec())
            .await?;
    }

    status_updater.update(LastIndexOperation {
        stage: Some("done".to_string()),
        finished: Some(true),
        estimated_progress: Some(1.0),
        details: Some(LastIndexOperationDetails::Compact {
            num_wal_entries: total_num_wal_entries as u64,
        }),
        ..Default::default()
    });

    tracing::info!(
        "Finished compacting {} WAL entries for segment {}",
        total_num_wal_entries,
        input.segment_id
    );

    Ok(CompactSegmentWalOutput {
        num_wal_entries_compacted: total_num_wal_entries,
    })
}

fn compact_segment_wal_inner_boxed(
    full_input: CompactSegmentWalInnerInput,
    options: CompactSegmentWalInnerOptions,
    additional_input: WriteIndexOperationHarnessTaskAdditionalInput<'_>,
) -> BoxFuture<'_, Result<CompactSegmentWalOutput>> {
    Box::pin(compact_segment_wal_inner(
        full_input,
        options,
        additional_input,
    ))
}

fn make_full_row_id_term(
    field: tantivy::schema::Field,
    full_row_id: &FullRowIdOwned,
) -> tantivy::Term {
    tantivy::schema::Term::from_field_text(field, &full_row_id.to_string())
}

pub(crate) struct IndexState {
    pub writer: tantivy::IndexWriter,
    pub reader: tantivy::IndexReader,
}

#[instrument(
    err,
    skip(tantivy_index_wrapper, merged_wal_entries, full_row_id_field, index_state, progress_counter),
    fields(
        writer_opts = ?writer_opts,
    ),
    name = "merge_wal_entries_into_segment"
)]
fn merge_wal_entries_into_segment(
    segment_id: Uuid,
    tantivy_index_wrapper: &ReadWriteTantivyIndexWrapper,
    mut merged_wal_entries: HashMap<FullRowIdOwned, WalEntry>,
    full_row_id_field: tantivy::schema::Field,
    writer_opts: &TantivyIndexWriterOpts,
    index_state: &mut IndexState,
    progress_counter: &ProgressCounter,
) -> Result<()> {
    let serialize_fields = &tantivy_index_wrapper.writable_schema.fields;
    let invert_fields = &tantivy_index_wrapper.writable_schema.invert_fields;

    let writer = &mut index_state.writer;
    index_state.reader.reload()?;
    let searcher = &mut index_state.reader.searcher();

    // Find all existing documents in the segment that match the FullRowIds of the WAL
    // entries.
    let matching_docs: HashSet<tantivy::DocAddress>;
    {
        let full_row_ids_query = tantivy::query::TermSetQuery::new(
            merged_wal_entries
                .keys()
                .map(|full_row_id| make_full_row_id_term(full_row_id_field, full_row_id)),
        );
        let collector = tantivy::collector::DocSetCollector {};
        matching_docs = searcher.search(&full_row_ids_query, &collector)?;
    }

    let merge_docs = STATIC_SYNC_RUNTIME
        .block_on(
            join_all(
                matching_docs
                    .iter()
                    .map(|doc_address| searcher.doc_async::<TantivyDocument>(*doc_address)),
            )
            .instrument(tracing::info_span!("fetch existing docs")),
        )
        .into_iter()
        .collect::<Result<Vec<_>, tantivy::TantivyError>>()?;

    let mut tantivy_ops: VecDeque<tantivy::indexer::UserOperation<TantivyDocument>> =
        VecDeque::new();
    let mut num_docs = 0;

    let try_flush_tantivy_ops =
        |tantivy_ops: &mut VecDeque<tantivy::indexer::UserOperation<TantivyDocument>>,
         num_docs: &mut usize|
         -> Result<()> {
            let chunk: Vec<_> = tantivy_ops
                .drain(..tantivy_ops.len().min(writer_opts.index_batch_size))
                .collect();
            let chunk_len = chunk.len();
            tracing::info_span!("run merge ops chunk", num_ops = chunk_len)
                .in_scope(|| writer.run(chunk))?;
            progress_counter.add_processed(*num_docs);
            *num_docs = 0;
            Ok(())
        };

    tracing::info_span!("merge existing docs").in_scope(|| {
        // Iterate through each doc and merge it with the corresponding WAL entry.
        //
        // Note: occasionally we see errors where duplicate docs end up in the index, and
        // `merge_docs` will match both of them. Then this merge step fails because it has already
        // used up the `merged_wal_entry` on the first matching version of the doc.
        //
        // We don't know precisely why this is happening, but for now we
        // explicitly de-duplicate the docs by FullRowId, choosing the one with
        // the highest pagination key.
        let mut row_id_to_index_doc: HashMap<FullRowIdOwned, IndexDocument> = HashMap::new();
        for doc in merge_docs {
            let index_doc = IndexDocument::from_tantivy_document(&doc, invert_fields)?;
            let full_row_id = FullRowIdOwned {
                object_type: index_doc.wal_entry._object_type,
                object_id: index_doc.wal_entry._object_id.clone(),
                id: index_doc.wal_entry.id.clone(),
            };
            use std::collections::hash_map::Entry;
            match row_id_to_index_doc.entry(full_row_id) {
                Entry::Vacant(entry) => {
                    entry.insert(index_doc);
                }
                Entry::Occupied(mut entry) => {
                    log::warn!(
                        "Duplicate document in index for segment {}: row ID {}, first pagination key {}, second pagination key {}. Preferring the one with the higher pagination key.",
                        segment_id,
                        entry.get().wal_entry.full_row_id(),
                        entry.get().wal_entry._pagination_key,
                        index_doc.wal_entry._pagination_key
                    );
                    if entry.get().wal_entry._pagination_key < index_doc.wal_entry._pagination_key {
                        entry.insert(index_doc);
                    }
                }
            }
        }

        for (full_row_id, mut index_doc) in row_id_to_index_doc {
            num_docs += 1;
            let wal_entry = merged_wal_entries.remove(&full_row_id).ok_or_else(|| {
                anyhow!(
                    "Matching FullRowId should be in merged_wal_entries: {}",
                    full_row_id.to_string()
                )
            })?;
            index_doc.merge_wal_entry(&wal_entry)?;
            // This should delete any duplicate docs from the index that we
            // skipped due to having the same row ID.
            tantivy_ops.push_back(tantivy::indexer::UserOperation::Delete(
                make_full_row_id_term(full_row_id_field, &full_row_id),
            ));
            // Only add the document if it's not a delete or a standalone comment.
            if index_doc.wal_entry._object_delete.unwrap_or(false)
                || index_doc.wal_entry._is_standalone_comment.unwrap_or(false)
            {
                continue;
            }
            legacy_sanitize_index_document(&mut index_doc)?;
            let doc = index_doc.to_tantivy_document(serialize_fields)?;
            tantivy_ops.push_back(tantivy::indexer::UserOperation::Add(doc));

            if tantivy_ops.len() >= writer_opts.index_batch_size {
                try_flush_tantivy_ops(&mut tantivy_ops, &mut num_docs)?;
            }
        }

        Ok::<(), util::anyhow::Error>(())
    })?;

    // Add the remaining WalEntries as new documents.
    tracing::info_span!("add new docs", num_docs = merged_wal_entries.len()).in_scope(|| {
        for (_, wal_entry) in merged_wal_entries {
            num_docs += 1;

            // Only add the document if it's not a delete or a standalone comment.
            if wal_entry._object_delete.unwrap_or(false)
                || wal_entry._is_standalone_comment.unwrap_or(false)
            {
                continue;
            }

            let mut index_doc = IndexDocument { wal_entry };
            legacy_sanitize_index_document(&mut index_doc)?;
            let doc = index_doc.to_tantivy_document(serialize_fields)?;
            tantivy_ops.push_back(tantivy::indexer::UserOperation::Add(doc));

            if tantivy_ops.len() >= writer_opts.index_batch_size {
                try_flush_tantivy_ops(&mut tantivy_ops, &mut num_docs)?;
            }
        }

        Ok::<(), util::anyhow::Error>(())
    })?;

    while !tantivy_ops.is_empty() {
        try_flush_tantivy_ops(&mut tantivy_ops, &mut num_docs)?;
    }

    tracing::info_span!("commit").in_scope(|| writer.commit())?;
    Ok(())
}

async fn collect_segment_infos(
    orig_input: &ProcessObjectWalInput<'_>,
    segment_ids: &[Uuid],
    xact_wal_entries: &[(TransactionId, Vec<WalEntryVariant>)],
) -> Result<(
    HashMap<FullRowIdOwned, Uuid>,
    HashMap<FullRowIdOwned, Uuid>,
    HashMap<Uuid, SegmentMetadata>,
)> {
    let (all_row_ids, all_root_span_ids): (HashSet<FullRowId>, HashSet<FullRowId>) =
        xact_wal_entries
            .iter()
            .flat_map(|(_, wal_entries)| {
                wal_entries
                    .iter()
                    .map(|wal_entry| (wal_entry.full_row_id(), wal_entry.full_root_span_id()))
            })
            .collect();
    let all_row_ids_vec = all_row_ids.iter().cloned().collect::<Vec<FullRowId>>();
    let all_root_span_ids_vec = all_root_span_ids
        .iter()
        .cloned()
        .collect::<Vec<FullRowId>>();
    let (row_id_segment_membership, root_span_id_segment_membership, segment_metadatas) = join!(
        orig_input.config.global_store.query_id_segment_membership(
            IdSegmentMembershipType::RowId,
            segment_ids,
            &all_row_ids_vec,
        ),
        orig_input.config.global_store.query_id_segment_membership(
            IdSegmentMembershipType::RootSpanId,
            segment_ids,
            &all_root_span_ids_vec,
        ),
        orig_input
            .config
            .global_store
            .query_segment_metadatas(segment_ids)
    );
    let row_id_segment_membership = row_id_segment_membership?;
    let root_span_id_segment_membership = root_span_id_segment_membership?;
    let segment_metadatas = segment_ids
        .iter()
        .zip(segment_metadatas?.into_iter())
        .map(|(segment_id, metadata)| (*segment_id, metadata))
        .collect::<HashMap<_, _>>();

    Ok((
        row_id_segment_membership,
        root_span_id_segment_membership,
        segment_metadatas,
    ))
}

fn check_xact_wal_entries(
    xact_wal_entries: &[(TransactionId, Vec<WalEntryVariant>)],
) -> Result<()> {
    let mut prev_xact_id: Option<TransactionId> = None;
    for (xact_id, wal_entries) in xact_wal_entries {
        if let Some(prev_xact_id) = prev_xact_id {
            if xact_id <= &prev_xact_id {
                return Err(anyhow!("Transaction IDs must be strictly increasing"));
            }
        }
        prev_xact_id = Some(*xact_id);

        let mut full_row_ids: HashSet<FullRowId> = HashSet::new();
        for wal_entry in wal_entries {
            if xact_id != &wal_entry.xact_id() {
                return Err(anyhow!("Transaction ID mismatch within a WAL transaction"));
            }
            if !full_row_ids.insert(wal_entry.full_row_id()) {
                return Err(anyhow!(
                    "Duplicate {:?} within a WAL transaction",
                    wal_entry.full_row_id()
                ));
            }
        }
    }
    Ok(())
}

fn merge_wal_streams(
    lhs: Vec<(TransactionId, Vec<WalEntryVariant>)>,
    rhs: Vec<(TransactionId, Vec<WalEntryVariant>)>,
) -> Vec<(TransactionId, Vec<WalEntryVariant>)> {
    merge_join_by(lhs, rhs, |(xact_id1, _), (xact_id2, _)| {
        xact_id1.cmp(xact_id2)
    })
    .map(|either_or_both| match either_or_both {
        EitherOrBoth::Both((xact_id, wal_entries1), (_, wal_entries2)) => {
            // Since individual WalEntries within a transaction should have different row IDs, the
            // ordering here shouldn't matter.
            (
                xact_id,
                wal_entries1.into_iter().chain(wal_entries2).collect(),
            )
        }
        EitherOrBoth::Left(x) => x,
        EitherOrBoth::Right(x) => x,
    })
    .collect()
}

struct LeftoverSegmentInfo {
    segment_id: Uuid,
    is_new_segment: bool,
    row_ids: HashSet<FullRowIdOwned>,
    root_span_ids: HashSet<FullRowIdOwned>,
    minimum_pagination_key: PaginationKey,
}

pub const PAGINATION_KEY_FIELD: &str = "_pagination_key";
pub const XACT_ID_FIELD: &str = "_xact_id";
pub const FIELD_STATISTICS_FIELD_NAMES: [&str; 3] =
    [PAGINATION_KEY_FIELD, "created", XACT_ID_FIELD];

// The returned field statistics are aligned with FIELD_STATISTICS_FIELD_NAMES.
#[instrument(err, skip(index_state), name = "recompute_field_statistics")]
async fn recompute_field_statistics(
    index_state: &IndexState,
) -> Result<[(&'static str, SegmentFieldStatistics); 3]> {
    let searcher = await_spawn_blocking!(
        move |index_state: &IndexState| -> Result<tantivy::Searcher> {
            index_state.reader.reload()?;
            Ok(index_state.reader.searcher())
        },
        index_state
    )???;

    // Concurrently iterate over the segment readers of the index and collect the field statistics.
    // This requires retaining references to the segment reader over a bunch of async tasks, so we
    // unsafely cast the segment readers to 'static for the duration of the concurrent work.
    let results = {
        let static_segment_readers: &'static [tantivy::SegmentReader] =
            unsafe { std::mem::transmute(searcher.segment_readers()) };
        join_all(static_segment_readers.iter().map(|segment_reader| {
            spawn_blocking_with_async_timeout(
                &tokio::runtime::Handle::current(),
                move || -> Result<[Option<SegmentFieldStatistics>; 3]> {
                    let fast_fields = segment_reader.fast_fields();
                    Ok([
                        collect_field_statistics(
                            fast_fields.u64(FIELD_STATISTICS_FIELD_NAMES[0])?,
                        )?,
                        collect_field_statistics(
                            fast_fields
                                .date(FIELD_STATISTICS_FIELD_NAMES[1])?
                                .to_u64_monotonic(),
                        )?,
                        collect_field_statistics(
                            fast_fields.u64(FIELD_STATISTICS_FIELD_NAMES[2])?,
                        )?,
                    ])
                },
                Default::default(),
                || "recompute_field_statistics".into(),
            )
        }))
        .await
        .into_iter()
        .map(|x| x??)
        .collect::<Result<Vec<_>>>()?
    };
    let merged_results = results.into_iter().fold([None, None, None], |acc, x| {
        let [acc0, acc1, acc2] = acc;
        let [x0, x1, x2] = x;
        [
            merge_options(acc0, x0, SegmentFieldStatistics::merge),
            merge_options(acc1, x1, SegmentFieldStatistics::merge),
            merge_options(acc2, x2, SegmentFieldStatistics::merge),
        ]
    });
    let [f0, f1, f2] = merged_results;
    // For completely empty segments, instead of returning None, we return a
    // regular statistics object over the range [0, 0], indicating that the
    // segment is (nearly) empty.
    //
    // We need something because consumers of the field statistics must
    // distinguish between segments which have not computed any field statistics
    // (maximally permissive) vs. empty segments (maximally conservative). If we
    // used None, the consumer code would have to distinguish between None and
    // 'missing', making the code a lot more complicated.
    let default_empty_segment_field_statistics = SegmentFieldStatistics::new(0, 0).unwrap();
    Ok([
        (
            FIELD_STATISTICS_FIELD_NAMES[0],
            f0.unwrap_or(default_empty_segment_field_statistics.clone()),
        ),
        (
            FIELD_STATISTICS_FIELD_NAMES[1],
            f1.unwrap_or(default_empty_segment_field_statistics.clone()),
        ),
        (
            FIELD_STATISTICS_FIELD_NAMES[2],
            f2.unwrap_or(default_empty_segment_field_statistics.clone()),
        ),
    ])
}

fn collect_field_statistics(
    column: tantivy::columnar::Column,
) -> Result<Option<SegmentFieldStatistics>> {
    if column.num_docs() == 0 {
        return Ok(None);
    }
    Ok(Some(SegmentFieldStatistics::new(
        column.min_value(),
        column.max_value(),
    )?))
}

#[cfg(test)]
pub async fn process_object_wal_loaded_for_testing(
    input: ProcessObjectWalLoadedInput<'_>,
    optional_input: ProcessObjectWalLoadedOptionalInput,
    options: ProcessObjectWalLoadedOptions,
) -> Result<ProcessObjectWalLoadedOutput> {
    process_object_wal_loaded(input, optional_input, options).await
}

#[cfg(test)]
pub async fn process_segment_wal_for_testing(
    input: ProcessSegmentWalInput<'_>,
    optional_input: ProcessSegmentWalOptionalInput,
) -> Result<HashMap<Uuid, HashMap<Uuid, Vec<UpsertSegmentWalEntry>>>> {
    process_segment_wal(input, optional_input).await
}

fn num_wal_entries<T>(xact_wal_entries: &[(TransactionId, Vec<T>)]) -> usize {
    xact_wal_entries
        .iter()
        .map(|(_, wal_entries)| wal_entries.len())
        .sum()
}

// NOTE: remove this legacy sanitization once we have gotten through backfills.
//
// For now, we may have legacy data which made it through WAL processing but fails the
// tantivy index schema validation. So we must apply some sanitization here too.
fn legacy_sanitize_index_document(index_doc: &mut IndexDocument) -> Result<()> {
    legacy_sanitizers::normalize_list_field(&mut index_doc.wal_entry.data, "tags")?;
    Ok(())
}

pub(crate) async fn update_metadata_and_field_statistics(
    global_store: &dyn GlobalStore,
    tantivy_index_wrapper: &ReadWriteTantivyIndexWrapper,
    index_state: &IndexState,
    segment_id: Uuid,
    index_store: &StoreInfo,
    index_scope: &TantivyIndexScope,
    status_updater: &'_ Option<StatusUpdater>,
    prev_last_compacted_index_meta: Option<LastCompactedIndexMeta>,
    last_xact_id: TransactionId,
    testing_skip_field_statistics: bool,
    writer_opts: &TantivyIndexWriterOpts,
) -> Result<LastCompactedIndexMeta> {
    let field_statistics = if testing_skip_field_statistics {
        vec![]
    } else {
        status_updater.update(LastIndexOperation {
            stage: Some("recomputing field statistics".to_string()),
            ..Default::default()
        });
        recompute_field_statistics(&index_state).await?.to_vec()
    };
    status_updater.update(LastIndexOperation {
        stage: Some("fetching index metadata".to_string()),
        ..Default::default()
    });

    let tantivy_meta = await_spawn_blocking!(
        |tantivy_index_wrapper: &ReadWriteTantivyIndexWrapper| {
            tantivy_index_wrapper.index.load_metas()
        },
        tantivy_index_wrapper
    )???;

    status_updater.update(LastIndexOperation {
        stage: Some("validating index".to_string()),
        ..Default::default()
    });
    let validation_result = validate_tantivy_index(
        IndexMetaJson::from_tantivy_meta(&tantivy_meta)?,
        &index_store.store,
        &index_store.prefix,
        &index_scope,
        &writer_opts.validate_opts,
    )
    .await?;
    let next_last_compacted_index_meta = LastCompactedIndexMeta {
        xact_id: last_xact_id,
        tantivy_meta: validation_result.check_success()?,
    };

    status_updater.update(LastIndexOperation {
        stage: Some("updating segment metadata".to_string()),
        ..Default::default()
    });

    if !global_store
        .upsert_segment_metadatas_and_field_statistics(
            [(
                segment_id,
                (
                    SegmentMetadataUpdate {
                        last_compacted_index_meta: Some((
                            prev_last_compacted_index_meta.clone(),
                            Some(next_last_compacted_index_meta.clone()),
                        )),
                        ..Default::default()
                    },
                    field_statistics.to_vec(),
                ),
            )]
            .into_iter()
            .collect(),
        )
        .await?
    {
        let latest_value = global_store
            .query_segment_metadatas(&[segment_id])
            .await?
            .remove(0)
            .last_compacted_index_meta;
        return Err(anyhow!(
            "last_compacted_index_meta was concurrently modified by another task. Expected value is {}. Latest value is {}",
            serde_json::to_string(&prev_last_compacted_index_meta)?, serde_json::to_string(&latest_value)?
        ));
    }

    status_updater.update(LastIndexOperation {
        stage: Some("writing segment footer".to_string()),
        estimated_progress: Some(0.0),
        ..Default::default()
    });

    make_footer(MakeFooterInput {
        index_store,
        segment_id,
        index_meta: &next_last_compacted_index_meta.tantivy_meta,
    })
    .await?;

    status_updater.update(LastIndexOperation {
        stage: Some("updating segment metadata".to_string()),
        estimated_progress: Some(1.0),
        ..Default::default()
    });

    Ok(next_last_compacted_index_meta)
}
