#!/usr/bin/env python3

import argparse
import re

import boto3
from cfn_tools import dump_yaml, load_yaml
from packaging import version


def update_brainstore_version(file_content, brainstore_tag="latest"):
    """
    Sets the BRAINSTORE_RELEASE_VERSION default value in the CF template
    with the specific version tag from ECR rather than "latest".
    This lets us lock our releases to customers on a specific version.
    """
    session = boto3.Session()
    ecr_client = session.client("ecr-public", region_name="us-east-1")

    response = ecr_client.describe_images(repositoryName="brainstore", imageIds=[{"imageTag": brainstore_tag}])

    if not response["imageDetails"]:
        raise Exception(f"No image details found for brainstore:{brainstore_tag}")

    brainstore_version = None
    if brainstore_tag == "latest":
        # Our docker release process should always tag the image with the commit hash, "latest", and
        # optionally a semver tag (e.g. "v1.2.3") before pushing.
        image_tags = response["imageDetails"][0].get("imageTags", [])

        semver_tags = []
        for tag in image_tags:
            try:
                version.parse(tag)
                semver_tags.append(tag)
            except version.InvalidVersion:
                # This will skip SHA tags
                pass

        # Prefer semver tags over other tags
        if semver_tags:
            semver_tags.sort(key=lambda x: version.parse(x))
            brainstore_version = semver_tags[-1]
            print(f"Found Brainstore semver version: {brainstore_version}")
        else:
            # Fall back to any other tag (not just SHA)
            other_tags = [tag for tag in image_tags if tag != "latest"]
            if len(other_tags) == 0:
                raise Exception(
                    f"No semver or other tags found for brainstore:{brainstore_tag}. Can't find the specific version to lock onto."
                )
            else:
                # If multiple other tags, just pick the first one
                brainstore_version = other_tags[0]
                print(f"Found multiple tags, using first: {brainstore_version} (available: {other_tags})")
    else:
        # If we're already using a SHA tag or a semver tag, then just lock on that
        brainstore_version = brainstore_tag

    # Update the BRAINSTORE_RELEASE_VERSION env var in the UserData script
    print(f"Updating Brainstore version in CF template to: {brainstore_version}")
    file_content = re.sub(
        r"BRAINSTORE_RELEASE_VERSION=.*", f"BRAINSTORE_RELEASE_VERSION={brainstore_version}", file_content
    )

    return file_content


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("src_file", help="Source file")
    parser.add_argument("dst_file", help="Destination file")
    parser.add_argument("--quarantine-only", action="store_true", help="Only include the quarantine VPC")
    parser.add_argument("--brainstore-version", default="latest", help="Version tag for brainstore image")
    args = parser.parse_args()

    with open(args.src_file, "r") as f:
        file_content = f.read()

    file_content = update_brainstore_version(file_content, args.brainstore_version)

    contents = load_yaml(file_content)

    VPCS = ["", "Quarantine"] if not args.quarantine_only else ["Quarantine"]

    # This is a vanilla boilerplate VPC that supports public and private network access copied from:
    # https://github.com/awsdocs/aws-lambda-developer-guide/blob/main/templates/vpc-privatepublic.yaml
    for vpc_prefix in VPCS:
        for base_field in ["Parameters", "Resources", "Conditions"]:
            if not contents.get(base_field):
                contents[base_field] = {}

        contents["Parameters"][f"{vpc_prefix}VPCCIDR"] = {
            "Type": "String",
            "Description": "CIDR for the VPC",
            "Default": "**********/16",
        }

        if vpc_prefix != "":
            contents["Parameters"][f"Enable{vpc_prefix}"] = {
                "Type": "String",
                "Description": f"Enable {vpc_prefix} VPC",
                "Default": "false",
                "AllowedValues": ["true", "false"],
            }
            contents["Conditions"][f"Has{vpc_prefix}"] = {"Fn::Equals": [{"Ref": f"Enable{vpc_prefix}"}, "true"]}
            contents["Conditions"][f"EmptyCIDR{vpc_prefix}"] = {"Fn::Equals": [{"Ref": f"{vpc_prefix}VPCCIDR"}, ""]}
        else:
            contents["Conditions"][f"Has{vpc_prefix}"] = {"Fn::Equals": [True, True]}
            contents["Conditions"][f"EmptyCIDR{vpc_prefix}"] = {"Fn::Equals": [True, False]}

        contents["Resources"][f"{vpc_prefix}pubPrivateVPC"] = {
            "Type": "AWS::EC2::VPC",
            "Condition": f"Has{vpc_prefix}",
            "Properties": {
                "CidrBlock": {
                    "Fn::If": [
                        # This is just a hack to get the default value to be **********/16
                        # since for historical reasons, we used the VPCCIDR's existence as the
                        # condition for the VPC's existence.
                        f"EmptyCIDR{vpc_prefix}",
                        "**********/16",
                        {"Ref": f"{vpc_prefix}VPCCIDR"},
                    ]
                },
                "Tags": [{"Key": "Name", "Value": {"Ref": "AWS::StackName"}}],
            },
        }

        for idx in range(4):
            flavor = "Public" if idx == 0 else "Private"
            i = 1 if idx == 0 else idx
            contents["Parameters"][f"{vpc_prefix}{flavor}Subnet{i}AZ"] = {
                "Type": "String",
                "Description": f"Availability zone for the {flavor.lower()} subnet (defaults to {idx}th AZ)",
                "Default": "",
            }

            if idx == 0:
                cidr_val = 0
            elif idx == 1:
                cidr_val = 3
            elif idx == 2:
                cidr_val = 2
            elif idx == 3:
                cidr_val = 4
            else:
                raise ValueError(f"Unexpected value for i: {i}")

            contents["Parameters"][f"{vpc_prefix}{flavor}Subnet{i}CIDR"] = {
                "Type": "String",
                "Description": f"CIDR for the {flavor.lower()} subnet",
                "Default": f"172.29.{cidr_val}.0/24",
            }

            contents["Conditions"][f"Has{flavor}Subnet{i}AZ"] = {
                "Fn::Not": [{"Fn::Equals": [{"Ref": f"{vpc_prefix}{flavor}Subnet{i}AZ"}, ""]}]
            }

            contents["Resources"][f"{vpc_prefix}{flavor.lower()}Subnet{i}"] = {
                "Type": "AWS::EC2::Subnet",
                "Condition": f"Has{vpc_prefix}",
                "Properties": {
                    "VpcId": {"Ref": f"{vpc_prefix}pubPrivateVPC"},
                    "AvailabilityZone": {
                        "Fn::If": [
                            f"Has{flavor}Subnet{i}AZ",
                            {"Ref": f"{vpc_prefix}{flavor}Subnet{i}AZ"},
                            {"Fn::Select": [i - 1, {"Fn::GetAZs": ""}]},
                        ]
                    },
                    "CidrBlock": {"Ref": f"{vpc_prefix}{flavor}Subnet{i}CIDR"},
                    "MapPublicIpOnLaunch": flavor == "Public",
                    "Tags": [
                        {
                            "Key": "Name",
                            "Value": {
                                "Fn::Join": [
                                    "-",
                                    [{"Ref": "AWS::StackName"}, vpc_prefix, f"{flavor.lower()}-subnet-{chr(96 + i)}"],
                                ]
                            },
                        }
                    ],
                },
            }

        contents["Resources"][f"{vpc_prefix}internetGateway"] = {
            "Type": "AWS::EC2::InternetGateway",
            "Condition": f"Has{vpc_prefix}",
            "Properties": {
                "Tags": [
                    {"Key": "Name", "Value": {"Fn::Join": ["-", [{"Ref": "AWS::StackName"}, vpc_prefix, "gateway"]]}}
                ],
            },
        }
        contents["Resources"][f"{vpc_prefix}gatewayToInternet"] = {
            "Type": "AWS::EC2::VPCGatewayAttachment",
            "Condition": f"Has{vpc_prefix}",
            "Properties": {
                "VpcId": {"Ref": f"{vpc_prefix}pubPrivateVPC"},
                "InternetGatewayId": {"Ref": f"{vpc_prefix}internetGateway"},
            },
        }
        contents["Resources"][f"{vpc_prefix}publicRouteTable"] = {
            "Type": "AWS::EC2::RouteTable",
            "Condition": f"Has{vpc_prefix}",
            "Properties": {"VpcId": {"Ref": f"{vpc_prefix}pubPrivateVPC"}},
        }
        contents["Resources"][f"{vpc_prefix}publicRoute"] = {
            "Type": "AWS::EC2::Route",
            "Condition": f"Has{vpc_prefix}",
            "DependsOn": f"{vpc_prefix}gatewayToInternet",
            "Properties": {
                "RouteTableId": {"Ref": f"{vpc_prefix}publicRouteTable"},
                "DestinationCidrBlock": "0.0.0.0/0",
                "GatewayId": {"Ref": f"{vpc_prefix}internetGateway"},
            },
        }
        contents["Resources"][f"{vpc_prefix}publicSubnet1RouteTableAssociation"] = {
            "Type": "AWS::EC2::SubnetRouteTableAssociation",
            "Condition": f"Has{vpc_prefix}",
            "Properties": {
                "SubnetId": {"Ref": f"{vpc_prefix}publicSubnet1"},
                "RouteTableId": {"Ref": f"{vpc_prefix}publicRouteTable"},
            },
        }
        contents["Resources"][f"{vpc_prefix}natGateway"] = {
            "Type": "AWS::EC2::NatGateway",
            "Condition": f"Has{vpc_prefix}",
            "DependsOn": f"{vpc_prefix}natPublicIP",
            "Properties": {
                "AllocationId": {"Fn::GetAtt": [f"{vpc_prefix}natPublicIP", "AllocationId"]},
                "SubnetId": {"Ref": f"{vpc_prefix}publicSubnet1"},
            },
        }
        contents["Resources"][f"{vpc_prefix}natPublicIP"] = {
            "Type": "AWS::EC2::EIP",
            "Condition": f"Has{vpc_prefix}",
            "DependsOn": f"{vpc_prefix}pubPrivateVPC",
            "Properties": {"Domain": "vpc"},
        }
        contents["Resources"][f"{vpc_prefix}privateRouteTable"] = {
            "Type": "AWS::EC2::RouteTable",
            "Condition": f"Has{vpc_prefix}",
            "Properties": {"VpcId": {"Ref": f"{vpc_prefix}pubPrivateVPC"}},
        }
        contents["Resources"][f"{vpc_prefix}privateRoute"] = {
            "Type": "AWS::EC2::Route",
            "Condition": f"Has{vpc_prefix}",
            "Properties": {
                "RouteTableId": {"Ref": f"{vpc_prefix}privateRouteTable"},
                "DestinationCidrBlock": "0.0.0.0/0",
                "NatGatewayId": {"Ref": f"{vpc_prefix}natGateway"},
            },
        }
        for i in range(1, 4):
            contents["Resources"][f"{vpc_prefix}privateSubnet{i}RouteTableAssociation"] = {
                "Type": "AWS::EC2::SubnetRouteTableAssociation",
                "Condition": f"Has{vpc_prefix}",
                "Properties": {
                    "SubnetId": {"Ref": f"{vpc_prefix}privateSubnet{i}"},
                    "RouteTableId": {"Ref": f"{vpc_prefix}privateRouteTable"},
                },
            }

        if vpc_prefix == "":
            contents["Resources"][f"{vpc_prefix}s3Endpoint"] = {
                "Type": "AWS::EC2::VPCEndpoint",
                "Condition": f"Has{vpc_prefix}",
                "Properties": {
                    "PolicyDocument": {
                        "Version": "2012-10-17",
                        "Statement": [
                            {
                                "Effect": "Allow",
                                "Principal": "*",
                                "Action": ["s3:*"],
                                "Resource": ["*"],
                            }
                        ],
                    },
                    "RouteTableIds": [{"Ref": f"{vpc_prefix}privateRouteTable"}],
                    "ServiceName": {"Fn::Sub": f"com.amazonaws.${{AWS::Region}}.s3"},
                    "VpcId": {"Ref": f"{vpc_prefix}pubPrivateVPC"},
                },
            }
            contents["Resources"][f"{vpc_prefix}dynamoDBEndpoint"] = {
                "Type": "AWS::EC2::VPCEndpoint",
                "Condition": f"Has{vpc_prefix}",
                "Properties": {
                    "PolicyDocument": {
                        "Version": "2012-10-17",
                        "Statement": [
                            {
                                "Effect": "Allow",
                                "Principal": "*",
                                "Action": ["dynamodb:*"],
                                "Resource": ["*"],
                            }
                        ],
                    },
                    "RouteTableIds": [{"Ref": f"{vpc_prefix}privateRouteTable"}],
                    "ServiceName": {"Fn::Sub": f"com.amazonaws.${{AWS::Region}}.dynamodb"},
                    "VpcId": {"Ref": f"{vpc_prefix}pubPrivateVPC"},
                },
            }

    with open(args.dst_file, "w") as f:
        f.write("### THIS IS AN AUTO GENERATED FILE. IF YOU EDIT IT, YOUR CHANGES WILL BE OVERWRITTEN ###\n\n")
        f.write(dump_yaml(contents))
