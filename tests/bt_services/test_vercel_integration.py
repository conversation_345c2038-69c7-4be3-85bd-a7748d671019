import hashlib
import hmac
import json
import os
import time
from datetime import datetime, timezone
from typing import Any, Dict, Optional
from uuid import uuid4

import redis
import requests
from braintrust_local.constants import LOCAL_REDIS_HOST, LOCAL_REDIS_PORT

from tests.braintrust_app_test_base import LOCAL_APP_URL, BraintrustAppTestBase

VERCEL_BASE_URL = f"{LOCAL_APP_URL}/api/partners/vercel/v1"


def generate_vercel_account_name():
    return f"_unit_test_org_vercel_account_{str(uuid4())}"


def generate_vercel_user_email():
    return f"_unit_test_user_vercel_{str(uuid4())}@company.com"


def generate_system_claims(**kwargs):
    base_claims = dict(
        iss="https://marketplace.vercel.com",
        aud="oac_9f4YG9JFjgKkRlxoaaGG0y05",
        account_id=f"team_{str(uuid4())}",
        sub=f"account:{str(uuid4())}:user:{str(uuid4())}",
        installation_id=f"icfg_{str(uuid4()).replace('-', '')}",
        iat=**********,
        exp=**********,
    )
    base_claims.update(kwargs)
    return base_claims


def generate_user_claims(**kwargs):
    base_claims = generate_system_claims()
    user_claims = dict(
        user_id=f"user_{str(uuid4())}",
        user_role="ADMIN",
        user_name="Some User",
        user_email=generate_vercel_user_email(),
    )
    base_claims.update(user_claims)
    base_claims.update(kwargs)
    return base_claims


def get_details_from_user_claims(**kwargs):
    claims = generate_user_claims(**kwargs)
    return (claims["installation_id"], claims["user_email"], claims["user_name"], claims)


def get_details_from_system_claims(**kwargs):
    claims = generate_system_claims(**kwargs)
    return (claims["installation_id"], claims)


class VercelIntegrationTest(BraintrustAppTestBase):
    """Tests for Vercel integration API endpoints"""

    def setUp(self):
        super().setUp()
        self.redis = redis.Redis(host=LOCAL_REDIS_HOST, port=LOCAL_REDIS_PORT, db=0, decode_responses=True)
        self.webhook_url = f"{LOCAL_APP_URL}/api/webhooks/vercel/integration"
        self.webhook_secret = os.environ.get("VERCEL_INTEGRATION_CLIENT_SECRET", "test-vercel-client-secret")
        self.vercel_api_url = f"{LOCAL_APP_URL}/api/partners/vercel/v1"

    def make_vercel_request(
        self,
        method: str,
        endpoint: str,
        claims: dict,
        body: Dict[str, Any],
        auth_type: str = "user",  # "user" or "system"
        jwt_override: Optional[str] = None,
        headers: Optional[Dict[str, str]] = None,
        expect_error: bool = False,
    ) -> requests.Response:
        """Helper to make requests to Vercel API endpoints with proper auth"""
        url = f"{VERCEL_BASE_URL}{endpoint}"

        # Set up JWT token based on auth type
        token = None

        if auth_type == "user":
            token = "valid_user_jwt_token"
        elif auth_type == "system":
            token = "valid_system_jwt_token"

        if jwt_override is not None:
            token = jwt_override

        # Set up headers
        request_headers = {
            "x-vercel-auth": auth_type,
            "x-bt-testing-only-vercel-auth-override": json.dumps(claims),
            **(headers or {}),
        }
        request_headers["Content-Type"] = "application/json"

        if token is not None:
            request_headers["Authorization"] = f"Bearer {token}"

        return self.run_request(
            method.lower(),
            url,
            headers=request_headers,
            json=body,
            expect_error=expect_error,
        )

    def get_user(self, email: str) -> bool:
        """Check if a user with the given email exists in the database"""
        with self.connect_app_db() as conn:
            with conn.cursor() as cursor:
                # Get user
                cursor.execute(
                    "SELECT id, given_name, family_name, email FROM users WHERE email = %s",
                    (email,),
                )
                row = cursor.fetchone()
                if not row:
                    return None
                id, given_name, family_name, email = row

                # Get vercel user mappings
                cursor.execute(
                    "SELECT vercel_user_id FROM vercel_user_mappings WHERE user_id = %s ORDER BY vercel_user_id",
                    (id,),
                )
                vercel_user_ids = [row[0] for row in cursor.fetchall()]

                return dict(
                    id=id, given_name=given_name, family_name=family_name, email=email, vercel_user_ids=vercel_user_ids
                )

    def check_access_token_persisted(self, installation_id: str, access_token: str) -> str:
        """Check if access token is stored in the database (encrypted)"""
        with self.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    "SELECT access_token FROM vercel_installations WHERE installation_id = %s",
                    (installation_id,),
                )
                row = cursor.fetchone()
                if not row or not row[0]:
                    return False
                # Token is encrypted in DB, so we just check if it exists
                return row[0] is not None

    def get_org_by_installation_id(self, installation_id: str):
        """Get organization by Vercel installation ID from metadata"""
        with self.connect_app_db() as conn:
            with conn.cursor() as cursor:
                # Get org_id and account info from installation
                cursor.execute(
                    """SELECT org_id, account_id, integration_type
                       FROM vercel_installations
                       WHERE installation_id = %s""",
                    (installation_id,),
                )
                row = cursor.fetchone()
                if not row:
                    print(f"No vercel_installation found for {installation_id}")
                    return None
                org_id, account_id, integration_type = row

                # Get org details
                cursor.execute(
                    "SELECT id, name FROM organizations WHERE id = %s",
                    (org_id,),
                )
                org_row = cursor.fetchone()
                if not org_row:
                    print(f"No org found for {installation_id} ({org_id})")
                    return None
                id, name = org_row

                # Get all installation_ids for this org
                cursor.execute(
                    "SELECT installation_id FROM vercel_installations WHERE org_id = %s ORDER BY installation_id",
                    (org_id,),
                )
                installation_ids = [row[0] for row in cursor.fetchall()]

                return dict(
                    id=id,
                    name=name,
                    installation_ids=installation_ids,
                    account_id=account_id,
                    integration_type=integration_type,
                )

    def check_user_is_org_owner(self, user_email: str, org_id: str) -> bool:
        """Check if user is owner of the organization via groups"""
        with self.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    """SELECT 1 FROM group_users gu
                       JOIN groups g ON g.id = gu.group_id
                       JOIN users u ON u.id = gu.user_id
                       WHERE u.email = %s
                         AND g.org_id = %s
                         AND g.name = 'Owners'""",
                    (user_email, org_id),
                )
                return cursor.fetchone() is not None

    def get_project(self, org_id: str, project_id: str):
        """Get project by name within an organization"""
        with self.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    "SELECT id, name, org_id FROM projects WHERE id = %s AND org_id = %s", (project_id, org_id)
                )
                row = cursor.fetchone()
                if not row:
                    return None
                id, name, org_id = row
                return dict(id=id, name=name, org_id=org_id)

    def get_project_metadata(self, project_id: str):
        """Get project metadata"""
        with self.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute("SELECT internal_metadata as metadata FROM projects WHERE id = %s", (project_id,))
                row = cursor.fetchone()
                if not row:
                    return None
                return row[0]

    def get_service_token(self, installation_id: str, api_key: str):
        """Get service token"""
        with self.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    "SELECT id, name, internal_metadata as metadata FROM api_keys WHERE internal_metadata->'vercel'->>'installation_id' = %s AND preview_name = %s",
                    (installation_id, f"bt-st-{api_key[-4:]}"),
                )
                row = cursor.fetchone()
                if not row:
                    return None
                id, name, metadata = row
                return dict(id=id, name=name, metadata=metadata)

    def get_service_account(self, org_id: str, email_pattern: str):
        """Get service account by email pattern"""
        with self.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    """SELECT u.id, u.email, u.internal_metadata
                    FROM users u
                    JOIN members m ON m.user_id = u.id
                    JOIN organizations o on m.org_id = o.id
                    WHERE o.id = %s
                    AND u.email LIKE %s
                    AND u.user_type = 'service_account'""",
                    (org_id, email_pattern),
                )
                row = cursor.fetchone()
                if not row:
                    return None
                id, email, metadata = row
                return dict(id=id, email=email, metadata=metadata)

    def verify_service_token_project_access(self, token: str, project_name: str, should_work: bool = True):
        """Verify that service token can/cannot access specific project"""
        import braintrust

        try:
            # Initialize logger with the token and project
            logger = braintrust.init_logger(
                project=project_name,
                api_key=token,
                force_login=True,  # Force login to handle multiple tokens in tests
            )

            # Log a simple trace
            logger.log(input={"test": "input"}, output={"test": "output"}, metadata={"source": "vercel"})

            # If we expect it to fail but it succeeded
            if not should_work:
                assert False, "should not have allowed logging"
        except Exception as e:
            # If we expect it to work but it failed
            if should_work:
                assert False, f"Service token access failed: {e}"

    def verify_api_key_works(self, api_key: str, project_name: str) -> bool:
        """Verify that API key can be used to log to the project using braintrust logger"""
        try:
            import braintrust

            # Initialize logger with the API key and project
            logger = braintrust.init_logger(
                project=project_name,
                api_key=api_key,
                force_login=True,  # Force login to handle multiple API keys in tests
            )

            # Log a simple trace
            logger.log(input={"test": "input"}, output={"test": "output"}, metadata={"source": "vercel"})

            # Flush to ensure the log is sent
            logger.flush()
            return True
        except Exception as e:
            print(f"API key verification failed: {e}")
            return False

    def get_org_members(self, org_id: str):
        """Get all members of an organization"""
        with self.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    """SELECT u.id, u.email, u.given_name, u.family_name
                       FROM users u
                       JOIN members m ON m.user_id = u.id
                       WHERE m.org_id = %s""",
                    (org_id,),
                )
                rows = cursor.fetchall()
                return [dict(id=row[0], email=row[1], given_name=row[2], family_name=row[3]) for row in rows]

    def get_user_groups(self, org_id: str, email: str):
        """Get groups that a user belongs to in an organization"""

        with self.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    """SELECT g.name
                       FROM groups g
                       JOIN group_users gu ON gu.group_id = g.id
                       JOIN users u ON gu.user_id = u.id
                       WHERE g.org_id = %s AND u.email = %s""",
                    (org_id, email),
                )
                rows = cursor.fetchall()
                return [row[0] for row in rows]

    def check_user_is_org_owner(self, user_email: str, org_id: str) -> bool:
        """Check if user is owner of the organization via groups"""
        return self.get_user_groups(org_id, user_email) == ["Owners"]

    # ============================================
    # PUT /v1/installations/{installationId} Tests
    # ============================================

    def test_put_installation_success(self):
        """Test successful installation creation"""
        installation_id, email, name, claims = get_details_from_user_claims()
        idempotency_key = "unique-idempotency-key-12345"

        body = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_123",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": email,
                    "name": name,
                },
            },
        }

        response = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id}",
            claims=claims,
            auth_type="user",
            body=body,
            headers={"Idempotency-Key": idempotency_key},
        )

        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn("billingPlan", data)
        self.assertNotIn("notification", data)

        # Check that a user was created with the requester email
        user = self.get_user(email)
        self.assertEqual(user["given_name"], name)  # dont bother splitting names
        self.assertEqual(user["family_name"], "")
        self.assertEqual(
            user["vercel_user_ids"],
            [claims["user_id"]],
        )

        # Check that auth token is persisted
        access_token = body["credentials"]["access_token"]
        token_persisted = self.check_access_token_persisted(installation_id, access_token)
        self.assertTrue(
            token_persisted, f"Auth token {access_token} should be persisted for installation {installation_id}"
        )

        # Check that organization was created with correct metadata
        org = self.get_org_by_installation_id(installation_id)
        self.assertIsNotNone(org, f"Organization should be created for installation {installation_id}")
        self.assertIn(installation_id, org["installation_ids"])
        self.assertEqual(org["account_id"], claims["account_id"])

        # Check that user is owner of the organization
        user_is_owner = self.check_user_is_org_owner(email, org["id"])
        self.assertTrue(user_is_owner, f"User {email} should be owner of org {org['id']}")

        # Second request with same idempotency key
        response2 = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id}",
            claims=claims,
            auth_type="user",
            body=body,
            headers={"Idempotency-Key": idempotency_key},
        )

        self.assertEqual(response2.status_code, 200)

        # Check that a user was created with the requester email
        user = self.get_user(email)
        self.assertEqual(user["given_name"], claims["user_name"])
        self.assertEqual(user["family_name"], "")
        self.assertEqual(
            user["vercel_user_ids"],
            [claims["user_id"]],
        )

        # Check that auth token is persisted
        access_token = body["credentials"]["access_token"]
        token_persisted = self.check_access_token_persisted(installation_id, access_token)
        self.assertTrue(
            token_persisted, f"Auth token {access_token} should be persisted for installation {installation_id}"
        )

    def test_put_installation_errors(self):
        """Test PUT installation with various error cases"""
        installation_id, email, name, claims = get_details_from_user_claims()

        body = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_123",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": email,
                    "name": "Test User",
                },
            },
        }

        # Make request without auth token
        url = f"{VERCEL_BASE_URL}/installations/{installation_id}"
        response = self.run_request(
            "put",
            url,
            headers={"Content-Type": "application/json"},
            json=body,
            expect_error=True,
        )

        self.assertEqual(response.status_code, 401)
        self.assertIn("authorization", response.text.lower())

        # Verify no user was created
        self.assertIsNone(self.get_user(email))

        # Make request with invalid JWT token
        response = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id}",
            claims="invalid_claims_or_jwt_token",
            body=body,
            expect_error=True,
        )

        self.assertEqual(response.status_code, 401)
        self.assertIn("verification failed", response.text.lower())

        # Verify no user was created
        self.assertIsNone(self.get_user(email))

        # Test invalid email
        invalid_email = "foobar"

        body = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_123",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": invalid_email,
                    "name": "Test User",
                },
            },
        }

        response = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id}",
            claims=claims,
            auth_type="user",
            body=body,
            expect_error=True,
        )

        self.assertEqual(response.status_code, 400)

        # Verify no user was created
        self.assertIsNone(self.get_user(email))

        # JWT installation_id doesn't match path
        body = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_123",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": email,
                    "name": "Test User",
                },
            },
        }

        response = self.make_vercel_request(
            "PUT",
            f"/installations/icfg_somethingelse",
            claims=claims,
            jwt_override="jwt_with_different_installation_id",
            body=body,
            expect_error=True,
        )

        self.assertEqual(response.status_code, 403)
        data = response.json()
        self.assertEqual(data["error"]["code"], "forbidden")
        self.assertIn("Access denied", data["error"]["message"])

        # Verify no user was created
        self.assertIsNone(self.get_user(email))

        # Use system auth instead of user auth (should fail)
        response = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id}",
            claims=claims,
            auth_type="system",
            body=body,
            expect_error=True,
        )

        # Will fail with 401 currently
        self.assertEqual(response.status_code, 401)

        # Verify no user was created
        self.assertIsNone(self.get_user(email))

        # Test missing policies
        body = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                # Missing "eula" policy
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_123",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": email,
                    "name": "Test User",
                },
            },
        }

        response = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id}",
            claims=claims,
            auth_type="user",
            body=body,
            expect_error=True,
        )

        # Should fail if TOS not accepted
        self.assertEqual(response.status_code, 400)
        data = response.json()
        self.assertEqual(data["error"]["code"], "validation_error")
        self.assertIn("Must accept EULA and Privacy Policy", data["error"]["message"])

        # Verify no user was created
        self.assertIsNone(self.get_user(email))

        body = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                # Missing "privacy" policy
            },
            "credentials": {
                "access_token": "test_access_token_123",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": email,
                    "name": "Test User",
                },
            },
        }

        response = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id}",
            claims=claims,
            auth_type="user",
            body=body,
            expect_error=True,
        )

        # Should fail if privacy policy not accepted
        self.assertNotEqual(response.status_code, 200)

        # Verify no user was created
        self.assertIsNone(self.get_user(email))

    def test_put_installation_conflicting_org_names(self):
        """Test PUT installation with conflicting org names appends UUID and creates new org"""
        # Use a specific company name that will be duplicated
        company_name = generate_vercel_account_name()

        # Create first installation
        installation_id1, email1, name1, claims1 = get_details_from_user_claims()

        body1 = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_conflict1",
                "token_type": "Bearer",
            },
            "account": {
                "name": company_name,
                "url": "https://example.com",
                "contact": {
                    "email": email1,
                    "name": "Test User 1",
                },
            },
        }

        response1 = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id1}",
            claims=claims1,
            auth_type="user",
            body=body1,
            headers={"Idempotency-Key": "conflict-test-key-1"},
        )

        self.assertEqual(response1.status_code, 200)

        # Verify first org was created with original name
        org1 = self.get_org_by_installation_id(installation_id1)
        self.assertIsNotNone(org1)
        self.assertEqual(org1["name"], company_name)

        # Verify first user is owner of first org
        user1 = self.get_user(email1)
        self.assertIsNotNone(user1)
        self.assertTrue(self.check_user_is_org_owner(email1, org1["id"]))

        # Create second installation with same company name
        installation_id2, email2, name2, claims2 = get_details_from_user_claims()

        body2 = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_conflict2",
                "token_type": "Bearer",
            },
            "account": {
                "name": company_name,  # Same name as first installation
                "url": "https://example.com",
                "contact": {
                    "email": email2,
                    "name": "Test User 2",
                },
            },
        }

        response2 = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id2}",
            claims=claims2,
            auth_type="user",
            body=body2,
            headers={"Idempotency-Key": "conflict-test-key-2"},
        )

        # Second installation should succeed (not crash)
        self.assertEqual(response2.status_code, 200)

        # Verify second org was created with UUID appended to name
        org2 = self.get_org_by_installation_id(installation_id2)
        self.assertIsNotNone(org2)
        self.assertNotEqual(org2["name"], company_name, "Second org name should be different from first")
        self.assertTrue(
            org2["name"].startswith(f"{company_name}-"),
            f"Second org name '{org2['name']}' should start with '{company_name}-'",
        )

        # Verify the appended part is the account id
        suffix = org2["name"][len(f"{company_name}-") :]
        self.assertEqual(suffix, claims2["account_id"])

        # Verify both orgs have different IDs
        self.assertNotEqual(org1["id"], org2["id"], "Organizations should have different IDs")

        # Verify second user is owner of second org
        user2 = self.get_user(email2)
        self.assertIsNotNone(user2)
        self.assertTrue(self.check_user_is_org_owner(email2, org2["id"]))

        # Verify both orgs have correct Vercel metadata
        self.assertIn(installation_id1, org1["installation_ids"])

        self.assertIn(installation_id2, org2["installation_ids"])

        # Verify users have different IDs
        self.assertNotEqual(user1["id"], user2["id"], "Users should have different IDs")

        # Verify auth tokens are persisted for both installations
        self.assertTrue(self.check_access_token_persisted(installation_id1, "test_access_token_conflict1"))
        self.assertTrue(self.check_access_token_persisted(installation_id2, "test_access_token_conflict2"))

    # ============================================
    # GET /v1/installations/{installationId} Tests
    # ============================================

    def test_get_installation_success(self):
        """Test successful retrieval of installation"""
        # Get user claims for creating the installation
        installation_id, email, name, user_claims = get_details_from_user_claims()

        # Get system claims for the same installation
        _, system_claims = get_details_from_system_claims(installation_id=installation_id)

        idempotency_key = "unique-idempotency-key-get-test"

        # First, create an installation using PUT
        put_body = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_get_123",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": email,
                    "name": "Test User",
                },
            },
        }

        put_response = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id}",
            claims=user_claims,
            auth_type="user",
            body=put_body,
            headers={"Idempotency-Key": idempotency_key},
        )
        self.assertEqual(put_response.status_code, 200)

        # Now test GET with system claims
        get_response = self.make_vercel_request(
            "GET",
            f"/installations/{installation_id}",
            claims=system_claims,
            auth_type="system",
            body={},  # GET requests don't need a body
        )

        self.assertEqual(get_response.status_code, 200)
        data = get_response.json()

        # Verify response structure
        self.assertIn("billingPlan", data)

        # Should have billing plan information
        if data["billingPlan"]:
            self.assertIn("id", data["billingPlan"])
            self.assertIn("scope", data["billingPlan"])
            self.assertEqual(data["billingPlan"]["scope"], "installation")

        # Should not have notification by default
        self.assertIsNone(data.get("notification"))

    def test_get_installation_errors(self):
        """Test GET installation with various error cases"""
        # Get user claims for creating the installation
        installation_id, email, name, user_claims = get_details_from_user_claims()

        # Get system claims for the same installation
        _, system_claims = get_details_from_system_claims(installation_id=installation_id)

        # First, create an installation to test against
        put_body = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_error_123",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": email,
                    "name": "Test User",
                },
            },
        }

        put_response = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id}",
            claims=user_claims,
            auth_type="user",
            body=put_body,
            headers={"Idempotency-Key": "unique-idempotency-key-error-test"},
        )
        self.assertEqual(put_response.status_code, 200)

        # Test 1: Request without auth token
        url = f"{VERCEL_BASE_URL}/installations/{installation_id}"
        response = self.run_request(
            "get",
            url,
            headers={"Content-Type": "application/json"},
            json={},
            expect_error=True,
        )
        self.assertEqual(response.status_code, 401)
        self.assertIn("authorization", response.text.lower())

        # Test 2: Invalid JWT token
        response = self.make_vercel_request(
            "GET",
            f"/installations/{installation_id}",
            claims="invalid_claims_or_jwt_token",
            auth_type="system",
            body={},
            expect_error=True,
        )
        self.assertEqual(response.status_code, 401)
        self.assertIn("verification failed", response.text.lower())

        # Test 3: Use user auth instead of system auth (should fail)
        response = self.make_vercel_request(
            "GET",
            f"/installations/{installation_id}",
            claims=user_claims,
            auth_type="user",
            body={},
            expect_error=True,
        )
        # Will fail with 401 currently (user auth not allowed for GET)
        self.assertEqual(response.status_code, 401)

        # Test 4: Installation not found
        non_existent_id = f"icfg_{str(uuid4()).replace('-', '')}"
        _, non_existent_claims = get_details_from_system_claims(installation_id=non_existent_id)

        response = self.make_vercel_request(
            "GET",
            f"/installations/{non_existent_id}",
            claims=non_existent_claims,
            auth_type="system",
            body={},
            expect_error=True,
        )
        self.assertEqual(response.status_code, 403)
        data = response.json()
        self.assertEqual(data["error"]["code"], "forbidden")
        self.assertIn("Access denied", data["error"]["message"])

        # Test 5: JWT installation_id doesn't match path parameter
        different_installation_id = f"icfg_{str(uuid4()).replace('-', '')}"
        _, mismatched_claims = get_details_from_system_claims(installation_id=different_installation_id)

        response = self.make_vercel_request(
            "GET",
            f"/installations/{installation_id}",  # Using original installation_id
            claims=mismatched_claims,  # But claims have different installation_id
            auth_type="system",
            body={},
            expect_error=True,
        )
        self.assertEqual(response.status_code, 403)
        data = response.json()
        self.assertEqual(data["error"]["code"], "forbidden")
        self.assertIn("Access denied", data["error"]["message"])

        # Test 6: Deleted installation
        # First create another installation to delete
        deleted_installation_id, deleted_email, deleted_name, deleted_user_claims = get_details_from_user_claims()

        deleted_put_body = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_deleted_123",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": deleted_email,
                    "name": "Test User",
                },
            },
        }

        # Create the installation
        delete_put_response = self.make_vercel_request(
            "PUT",
            f"/installations/{deleted_installation_id}",
            claims=deleted_user_claims,
            auth_type="user",
            body=deleted_put_body,
            headers={"Idempotency-Key": "unique-idempotency-key-delete-test"},
        )
        self.assertEqual(delete_put_response.status_code, 200)

        # Delete the installation
        delete_response = self.make_vercel_request(
            "DELETE",
            f"/installations/{deleted_installation_id}",
            claims=deleted_user_claims,
            auth_type="user",
            body={"cascadeResourceDeletion": True},
        )
        self.assertEqual(delete_response.status_code, 200)

        # Try to GET the deleted installation
        _, deleted_system_claims = get_details_from_system_claims(installation_id=deleted_installation_id)

        response = self.make_vercel_request(
            "GET",
            f"/installations/{deleted_installation_id}",
            claims=deleted_system_claims,
            auth_type="system",
            body={},
            expect_error=True,
        )
        self.assertEqual(response.status_code, 403)
        data = response.json()
        self.assertEqual(data["error"]["code"], "forbidden")
        self.assertIn("Access denied", data["error"]["message"])

    # ============================================
    # DELETE /v1/installations/{installationId} Tests
    # ============================================

    def test_delete_installation_success(self):
        """Test successful deletion of installation"""
        # Create installation first
        installation_id, email, name, user_claims = get_details_from_user_claims()
        idempotency_key = "unique-idempotency-key-delete-success"

        put_body = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_delete_success",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": email,
                    "name": "Test User",
                },
            },
        }

        put_response = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id}",
            claims=user_claims,
            auth_type="user",
            body=put_body,
            headers={"Idempotency-Key": idempotency_key},
        )
        self.assertEqual(put_response.status_code, 200)

        # Delete the installation
        delete_body = {"cascadeResourceDeletion": False}

        delete_response = self.make_vercel_request(
            "DELETE",
            f"/installations/{installation_id}",
            claims=user_claims,
            auth_type="user",
            body=delete_body,
        )

        # Should return 200 with finalized: true (free plan)
        self.assertEqual(delete_response.status_code, 200)
        delete_data = delete_response.json()
        self.assertEqual(delete_data["finalized"], True)

        # Verify user still exists (deletion only affects installation)
        user = self.get_user(email)
        self.assertIsNotNone(user)

        # Verify org no longer contains the installation_id
        org = self.get_org_by_installation_id(installation_id)
        self.assertIsNone(org, "Organization should not be found after uninstall")

        # Verify subsequent GET returns 403 (installation marked as deleted)
        _, system_claims = get_details_from_system_claims(installation_id=installation_id)

        get_response = self.make_vercel_request(
            "GET",
            f"/installations/{installation_id}",
            claims=system_claims,
            auth_type="system",
            body={},
            expect_error=True,
        )
        self.assertEqual(get_response.status_code, 403)

        # Test already deleted: Delete again, verify 204 on second attempt
        delete_response2 = self.make_vercel_request(
            "DELETE",
            f"/installations/{installation_id}",
            claims=user_claims,
            auth_type="user",
            body=delete_body,
        )
        self.assertEqual(delete_response2.status_code, 204)

    def test_delete_installation_system_auth(self):
        """Test successful deletion of installation with system auth"""
        # Create installation first
        installation_id, email, name, user_claims = get_details_from_user_claims()
        idempotency_key = "unique-idempotency-key-delete-system"

        put_body = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_delete_system",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": email,
                    "name": "Test User",
                },
            },
        }

        put_response = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id}",
            claims=user_claims,
            auth_type="user",
            body=put_body,
            headers={"Idempotency-Key": idempotency_key},
        )
        self.assertEqual(put_response.status_code, 200)

        # Delete with system auth
        _, system_claims = get_details_from_system_claims(installation_id=installation_id)
        delete_body = {"cascadeResourceDeletion": True}

        delete_response = self.make_vercel_request(
            "DELETE",
            f"/installations/{installation_id}",
            claims=system_claims,
            auth_type="system",
            body=delete_body,
        )

        # Should return 200 with finalized: true (free plan)
        self.assertEqual(delete_response.status_code, 200)
        delete_data = delete_response.json()
        self.assertEqual(delete_data["finalized"], True)

        # Verify user still exists
        user = self.get_user(email)
        self.assertIsNotNone(user)

    def test_delete_installation_errors(self):
        """Test DELETE installation with various error cases"""
        # Create installation for testing
        installation_id, email, name, user_claims = get_details_from_user_claims()

        put_body = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_delete_errors",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": email,
                    "name": "Test User",
                },
            },
        }

        put_response = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id}",
            claims=user_claims,
            auth_type="user",
            body=put_body,
            headers={"Idempotency-Key": "unique-idempotency-key-delete-errors"},
        )
        self.assertEqual(put_response.status_code, 200)

        delete_body = {"cascadeResourceDeletion": True}

        # Test 1: Request without auth token
        url = f"{VERCEL_BASE_URL}/installations/{installation_id}"
        response = self.run_request(
            "delete",
            url,
            headers={"Content-Type": "application/json"},
            json=delete_body,
            expect_error=True,
        )
        self.assertEqual(response.status_code, 401)
        self.assertIn("authorization", response.text.lower())

        # Test 2: Invalid JWT token
        response = self.make_vercel_request(
            "DELETE",
            f"/installations/{installation_id}",
            claims="invalid_claims_or_jwt_token",
            auth_type="user",
            body=delete_body,
            expect_error=True,
        )
        self.assertEqual(response.status_code, 401)
        self.assertIn("verification failed", response.text.lower())

        # Test 3: Installation not found (should return 204)
        non_existent_id = f"icfg_{str(uuid4()).replace('-', '')}"
        _, non_existent_claims = get_details_from_system_claims(installation_id=non_existent_id)

        response = self.make_vercel_request(
            "DELETE",
            f"/installations/{non_existent_id}",
            claims=non_existent_claims,
            auth_type="system",
            body=delete_body,
        )
        self.assertEqual(response.status_code, 204)

        # Test 4: JWT installation_id doesn't match path parameter
        different_installation_id = f"icfg_{str(uuid4()).replace('-', '')}"
        _, mismatched_claims = get_details_from_system_claims(installation_id=different_installation_id)

        response = self.make_vercel_request(
            "DELETE",
            f"/installations/{installation_id}",  # Using original installation_id
            claims=mismatched_claims,  # But claims have different installation_id
            auth_type="system",
            body=delete_body,
            expect_error=True,
        )
        self.assertEqual(response.status_code, 403)
        data = response.json()
        self.assertEqual(data["error"]["code"], "forbidden")
        self.assertIn("Access denied", data["error"]["message"])

        # Test 5: Missing cascadeResourceDeletion parameter
        invalid_body = {}  # Missing required parameter

        response = self.make_vercel_request(
            "DELETE",
            f"/installations/{installation_id}",
            claims=user_claims,
            auth_type="user",
            body=invalid_body,
            expect_error=True,
        )
        self.assertEqual(response.status_code, 400)

    def test_multiple_installations_same_account_id(self):
        """Test that multiple installations with same account_id map to same org"""
        # Create first installation
        installation_id1 = f"icfg_{str(uuid4()).replace('-', '')}"
        account_id = f"acct_{str(uuid4()).replace('-', '')}"
        email = generate_vercel_user_email()
        name = "Test User Multiple Installs"

        user_claims1 = generate_user_claims(
            user_email=email,
            user_name=name,
            account_id=account_id,
            installation_id=installation_id1,
        )

        put_body1 = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_multi_1",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": email,
                    "name": name,
                },
            },
        }

        put_response1 = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id1}",
            claims=user_claims1,
            auth_type="user",
            body=put_body1,
            headers={"Idempotency-Key": f"unique-key-multi-1-{installation_id1}"},
        )
        self.assertEqual(put_response1.status_code, 200)

        # Get the org created for first installation
        org1 = self.get_org_by_installation_id(installation_id1)
        self.assertIsNotNone(org1)
        self.assertIn(installation_id1, org1["installation_ids"])
        self.assertEqual(org1["account_id"], account_id)

        # Create second installation with same account_id
        installation_id2 = f"icfg_{str(uuid4()).replace('-', '')}"

        user_claims2 = generate_user_claims(
            user_email=email,
            user_name=name,
            account_id=account_id,  # Same account_id
            installation_id=installation_id2,
        )

        put_body2 = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_multi_2",
                "token_type": "Bearer",
            },
            "account": {
                "name": put_body1["account"]["name"],  # Same account name
                "url": "https://example.com",
                "contact": {
                    "email": email,
                    "name": name,
                },
            },
        }

        put_response2 = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id2}",
            claims=user_claims2,
            auth_type="user",
            body=put_body2,
            headers={"Idempotency-Key": f"unique-key-multi-2-{installation_id2}"},
        )
        self.assertEqual(put_response2.status_code, 200)

        # Verify both installations map to the SAME org
        org2_via_install1 = self.get_org_by_installation_id(installation_id1)
        org2_via_install2 = self.get_org_by_installation_id(installation_id2)

        self.assertIsNotNone(org2_via_install1)
        self.assertIsNotNone(org2_via_install2)
        self.assertEqual(
            org2_via_install1["id"], org2_via_install2["id"], "Both installations should map to the same org"
        )

        # Verify org metadata contains BOTH installation_ids
        combined_org = org2_via_install1
        installation_ids = combined_org["installation_ids"]
        self.assertIn(installation_id1, installation_ids)
        self.assertIn(installation_id2, installation_ids)
        self.assertEqual(len(installation_ids), 2, "Org should contain exactly 2 installation_ids")

        # Verify account_id is correct
        self.assertEqual(combined_org["account_id"], account_id)

    # ============================================
    # PATCH /v1/installations/{installationId} Tests
    # ============================================

    def test_patch_installation_success(self):
        """Test successful update of installation billing plan"""
        # Create installation first (starts with free plan)
        installation_id, email, name, user_claims = get_details_from_user_claims()
        idempotency_key = "unique-idempotency-key-patch-success"

        put_body = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_patch_success",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": email,
                    "name": "Test User",
                },
            },
        }

        put_response = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id}",
            claims=user_claims,
            auth_type="user",
            body=put_body,
            headers={"Idempotency-Key": idempotency_key},
        )
        self.assertEqual(put_response.status_code, 200)

        # Verify initial billing plan (should be free)
        put_data = put_response.json()
        self.assertEqual(put_data["billingPlan"]["id"], "free")

        # Update to pro plan
        patch_body = {"billingPlanId": "pro"}

        patch_response = self.make_vercel_request(
            "PATCH",
            f"/installations/{installation_id}",
            claims=user_claims,
            auth_type="user",
            body=patch_body,
        )

        # Should return 200 with updated billing plan
        self.assertEqual(patch_response.status_code, 200)
        patch_data = patch_response.json()

        # Verify billing plan updated to pro
        self.assertIn("billingPlan", patch_data)
        self.assertEqual(patch_data["billingPlan"]["id"], "pro")
        self.assertEqual(patch_data["billingPlan"]["scope"], "installation")
        self.assertEqual(patch_data["billingPlan"]["name"], "Pro")

        # Verify GET also returns updated billing plan
        _, system_claims = get_details_from_system_claims(installation_id=installation_id)

        get_response = self.make_vercel_request(
            "GET",
            f"/installations/{installation_id}",
            claims=system_claims,
            auth_type="system",
            body={},
        )

        self.assertEqual(get_response.status_code, 200)
        get_data = get_response.json()
        # TODO: update test when billing is enabled
        # self.assertEqual(get_data["billingPlan"]["id"], "pro")

    def test_patch_installation_errors(self):
        """Test PATCH installation with various error cases"""
        # Create installation for testing
        installation_id, email, name, user_claims = get_details_from_user_claims()

        put_body = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_patch_errors",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": email,
                    "name": "Test User",
                },
            },
        }

        put_response = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id}",
            claims=user_claims,
            auth_type="user",
            body=put_body,
            headers={"Idempotency-Key": "unique-idempotency-key-patch-errors"},
        )
        self.assertEqual(put_response.status_code, 200)

        patch_body = {"billingPlanId": "pro"}

        # Test 1: Request without auth token
        url = f"{VERCEL_BASE_URL}/installations/{installation_id}"
        response = self.run_request(
            "patch",
            url,
            headers={"Content-Type": "application/json"},
            json=patch_body,
            expect_error=True,
        )
        self.assertEqual(response.status_code, 401)
        self.assertIn("authorization", response.text.lower())

        # Test 2: Invalid JWT token
        response = self.make_vercel_request(
            "PATCH",
            f"/installations/{installation_id}",
            claims="invalid_claims_or_jwt_token",
            auth_type="user",
            body=patch_body,
            expect_error=True,
        )
        self.assertEqual(response.status_code, 401)
        self.assertIn("verification failed", response.text.lower())

        # Test 3: System auth instead of user auth (should fail)
        _, system_claims = get_details_from_system_claims(installation_id=installation_id)

        response = self.make_vercel_request(
            "PATCH",
            f"/installations/{installation_id}",
            claims=system_claims,
            auth_type="system",
            body=patch_body,
            expect_error=True,
        )
        # Will fail with 401 (PATCH requires user auth)
        self.assertEqual(response.status_code, 401)

        # Test 4: Installation not found
        non_existent_id = f"icfg_{str(uuid4()).replace('-', '')}"
        _, _, _, non_existent_claims = get_details_from_user_claims(installation_id=non_existent_id)

        response = self.make_vercel_request(
            "PATCH",
            f"/installations/{non_existent_id}",
            claims=non_existent_claims,
            auth_type="user",
            body=patch_body,
            expect_error=True,
        )
        self.assertEqual(response.status_code, 403)

        # Test 5: JWT installation_id doesn't match path parameter
        different_installation_id = f"icfg_{str(uuid4()).replace('-', '')}"
        _, _, _, mismatched_claims = get_details_from_user_claims(installation_id=different_installation_id)

        response = self.make_vercel_request(
            "PATCH",
            f"/installations/{installation_id}",  # Using original installation_id
            claims=mismatched_claims,  # But claims have different installation_id
            auth_type="user",
            body=patch_body,
            expect_error=True,
        )
        self.assertEqual(response.status_code, 403)

        # Test 6: Invalid billing plan ID
        invalid_plan_body = {"billingPlanId": "nonexistent_plan"}

        response = self.make_vercel_request(
            "PATCH",
            f"/installations/{installation_id}",
            claims=user_claims,
            auth_type="user",
            body=invalid_plan_body,
        )
        # Should succeed but billing plan will be undefined
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIsNone(data.get("billingPlan"))

        # Test 7: Missing billingPlanId parameter
        invalid_body = {}  # Missing required parameter

        response = self.make_vercel_request(
            "PATCH",
            f"/installations/{installation_id}",
            claims=user_claims,
            auth_type="user",
            body=invalid_body,
            expect_error=True,
        )
        self.assertEqual(response.status_code, 400)

    # ============================================
    # POST /v1/installations/{installationId}/resources Tests
    # ============================================

    def test_post_resources_success(self):
        """Test successful creation of resource"""
        # Create installation first
        installation_id, email, name, user_claims = get_details_from_user_claims()
        idempotency_key = "unique-idempotency-key-post-resource-success"

        put_body = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_post_resource",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": email,
                    "name": "Test User",
                },
            },
        }

        put_response = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id}",
            claims=user_claims,
            auth_type="user",
            body=put_body,
            headers={"Idempotency-Key": idempotency_key},
        )
        self.assertEqual(put_response.status_code, 200)

        # Get the org that was created
        org = self.get_org_by_installation_id(installation_id)
        self.assertIsNotNone(org)

        # Create resource
        resource_body = {
            "productId": "traces",
            "name": "test-bt-traces",
            "metadata": {},
            "billingPlanId": "free",
        }

        post_response = self.make_vercel_request(
            "POST",
            f"/installations/{installation_id}/resources",
            claims=user_claims,
            auth_type="user",
            body=resource_body,
        )

        # Should return 201 with resource details
        self.assertEqual(post_response.status_code, 201)
        data = post_response.json()

        # Verify resource structure
        self.assertIn("id", data)
        self.assertEqual(data["name"], "test-bt-traces")
        self.assertEqual(data["productId"], "traces")
        self.assertEqual(data["status"], "ready")

        # Verify metadata includes created org and user
        self.assertIn("metadata", data)
        self.assertEqual(data["metadata"]["org_id"], org["id"])
        self.assertEqual(data["metadata"]["user_id"], self.get_user(email)["id"])
        self.assertEqual(
            data["metadata"]["project_id"], self.get_project(org["id"], data["metadata"]["project_id"])["id"]
        )

        # Verify billing plan
        self.assertEqual(data["billingPlan"]["id"], "free")

        # Verify secrets contain real API key and project name
        self.assertIn("secrets", data)
        secrets = {secret["name"]: secret["value"] for secret in data["secrets"]}
        self.assertIn("BRAINTRUST_API_KEY", secrets)
        self.assertIn("BRAINTRUST_PROJECT_ID", secrets)

        api_key = secrets["BRAINTRUST_API_KEY"]
        project_id = secrets["BRAINTRUST_PROJECT_ID"]

        # Verify project was created in database
        project = self.get_project(org["id"], project_id)
        project_name = project["name"]
        self.assertIsNotNone(project, f"Project {project_name} should be created in org {org['id']}")

        # Verify project has resource metadata stored
        project_metadata = self.get_project_metadata(project["id"])
        self.assertIsNotNone(project_metadata)
        self.assertIn("vercel", project_metadata)
        self.assertEqual(project_metadata["vercel"]["installation_id"], installation_id)

        # Verify API key works for logging to the project
        api_key_works = self.verify_api_key_works(api_key, project_name)
        self.assertTrue(api_key_works, f"API key should allow logging to project {project_name}")

        # Verify service token has resource metadata stored
        service_token = self.get_service_token(installation_id, api_key)
        self.assertIsNotNone(service_token)
        self.assertIn("vercel", service_token["metadata"])
        self.assertEqual(
            service_token["metadata"]["vercel"], {"installation_id": installation_id, "resource_id": project["id"]}
        )

        # Verify service account was created with correct pattern
        service_account = self.get_service_account(org["id"], f"bt::sp::custom::vercel-account-{project_name}-%")
        self.assertIsNotNone(service_account, "Service account should be created")
        self.assertIn("vercel", service_account["metadata"])
        self.assertEqual(service_account["metadata"]["vercel"]["installation_id"], installation_id)
        self.assertEqual(service_account["metadata"]["vercel"]["resource_id"], project["id"])

    def test_post_resources_errors(self):
        """Test POST resources with various error cases"""
        # Create installation first
        installation_id, email, name, user_claims = get_details_from_user_claims()

        put_body = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_post_resource_errors",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": email,
                    "name": "Test User",
                },
            },
        }

        put_response = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id}",
            claims=user_claims,
            auth_type="user",
            body=put_body,
            headers={"Idempotency-Key": "unique-idempotency-key-post-resource-errors"},
        )
        self.assertEqual(put_response.status_code, 200)

        resource_body = {
            "productId": "redis",
            "name": "test-redis",
            "metadata": {"project_name": "Test Project"},
            "billingPlanId": "pro",
        }

        # Test 1: Request without auth token
        url = f"{VERCEL_BASE_URL}/installations/{installation_id}/resources"
        response = self.run_request(
            "post",
            url,
            headers={"Content-Type": "application/json"},
            json=resource_body,
            expect_error=True,
        )
        self.assertEqual(response.status_code, 401)
        self.assertIn("authorization", response.text.lower())

        # Test 2: Invalid JWT token
        response = self.make_vercel_request(
            "POST",
            f"/installations/{installation_id}/resources",
            claims="invalid_claims_or_jwt_token",
            auth_type="user",
            body=resource_body,
            expect_error=True,
        )
        self.assertEqual(response.status_code, 401)
        self.assertIn("verification failed", response.text.lower())

        # Test 3: System auth instead of user auth (should fail)
        _, system_claims = get_details_from_system_claims(installation_id=installation_id)

        response = self.make_vercel_request(
            "POST",
            f"/installations/{installation_id}/resources",
            claims=system_claims,
            auth_type="system",
            body=resource_body,
            expect_error=True,
        )
        self.assertEqual(response.status_code, 401)

        # Test 4: JWT installation_id doesn't match path parameter
        different_installation_id = f"icfg_{str(uuid4()).replace('-', '')}"
        _, _, _, mismatched_claims = get_details_from_user_claims(installation_id=different_installation_id)

        response = self.make_vercel_request(
            "POST",
            f"/installations/{installation_id}/resources",
            claims=mismatched_claims,
            auth_type="user",
            body=resource_body,
            expect_error=True,
        )
        self.assertEqual(response.status_code, 403)

        # Test 5: Invalid billing plan
        invalid_plan_body = {**resource_body, "billingPlanId": "nonexistent_plan"}

        response = self.make_vercel_request(
            "POST",
            f"/installations/{installation_id}/resources",
            claims=user_claims,
            auth_type="user",
            body=invalid_plan_body,
            expect_error=True,
        )
        self.assertEqual(response.status_code, 400)  # Unknown billing plan validation error
        data = response.json()
        self.assertEqual(data["error"]["code"], "validation_error")

        # Test 6: Missing required fields
        missing_fields_body = {
            "productId": "redis",
            # Missing name, metadata, billingPlanId
        }

        response = self.make_vercel_request(
            "POST",
            f"/installations/{installation_id}/resources",
            claims=user_claims,
            auth_type="user",
            body=missing_fields_body,
            expect_error=True,
        )
        self.assertEqual(response.status_code, 400)

    def test_service_token_project_isolation(self):
        """Test that service tokens are isolated to their specific projects"""
        # Create installation first
        installation_id, email, name, user_claims = get_details_from_user_claims()

        put_body = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_project_isolation",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": email,
                    "name": "Test User",
                },
            },
        }

        put_response = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id}",
            claims=user_claims,
            auth_type="user",
            body=put_body,
        )
        self.assertEqual(put_response.status_code, 200)

        org = self.get_org_by_installation_id(installation_id)
        self.assertIsNotNone(org)

        # Create resource 1
        resource1_body = {
            "productId": "traces",
            "name": "isolation-project-1",
            "metadata": {},
            "billingPlanId": "free",
        }

        response1 = self.make_vercel_request(
            "POST",
            f"/installations/{installation_id}/resources",
            claims=user_claims,
            auth_type="user",
            body=resource1_body,
        )
        self.assertEqual(response1.status_code, 201)
        resource1 = response1.json()
        token1 = resource1["secrets"][0]["value"]  # BRAINTRUST_API_KEY
        project1_name = resource1["secrets"][1]["value"]  # BRAINTRUST_PROJECT_ID

        # Create resource 2
        resource2_body = {
            "productId": "traces",
            "name": "isolation-project-2",
            "metadata": {},
            "billingPlanId": "free",
        }

        response2 = self.make_vercel_request(
            "POST",
            f"/installations/{installation_id}/resources",
            claims=user_claims,
            auth_type="user",
            body=resource2_body,
        )
        self.assertEqual(response2.status_code, 201)
        resource2 = response2.json()
        token2 = resource2["secrets"][0]["value"]  # BRAINTRUST_API_KEY
        project2_name = resource2["secrets"][1]["value"]  # BRAINTRUST_PROJECT_ID

        # Test token 1 can log to project 1
        self.verify_service_token_project_access(token1, project1_name, should_work=True),

        # Test token 2 can log to project 2
        self.verify_service_token_project_access(token2, project2_name, should_work=True),

        # Test token 1 CANNOT log to project 2 (should fail with 403)
        self.verify_service_token_project_access(token1, project2_name, should_work=False),

        # Test token 2 CANNOT log to project 1 (should fail with 403)
        self.verify_service_token_project_access(token2, project1_name, should_work=False),

    # ============================================
    # PATCH /v1/installations/{installationId}/resources/{resourceId} Tests
    # ============================================

    def test_patch_resource_success(self):
        """Test successful update of resource"""
        # Create installation first
        installation_id, email, name, user_claims = get_details_from_user_claims()

        put_body = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_patch_resource_success",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": email,
                    "name": "Test User",
                },
            },
        }

        put_response = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id}",
            claims=user_claims,
            auth_type="user",
            body=put_body,
            headers={"Idempotency-Key": "unique-idempotency-key-patch-resource-success"},
        )
        self.assertEqual(put_response.status_code, 200)

        # Get the org that was created
        org = self.get_org_by_installation_id(installation_id)
        self.assertIsNotNone(org)

        # Create resource first
        resource_body = {
            "productId": "redis",
            "name": "original-resource-name",
            "metadata": {"project_name": "project-name", "region": "us-east-1"},
            "billingPlanId": "pro",
        }

        post_response = self.make_vercel_request(
            "POST",
            f"/installations/{installation_id}/resources",
            claims=user_claims,
            auth_type="user",
            body=resource_body,
        )
        self.assertEqual(post_response.status_code, 201)
        resource_data = post_response.json()
        resource_id = resource_data["id"]
        original_project_id = resource_data["secrets"][1]["value"]  # BRAINTRUST_PROJECT_ID

        # Verify original project exists
        original_project = self.get_project(org["id"], original_project_id)
        self.assertIsNotNone(original_project)

        # Update resource name and metadata
        patch_body = {"name": "updated-trace-name", "metadata": {}}

        patch_response = self.make_vercel_request(
            "PATCH",
            f"/installations/{installation_id}/resources/{resource_id}",
            claims=user_claims,
            auth_type="user",
            body=patch_body,
        )

        # Should return 200 with updated resource
        self.assertEqual(patch_response.status_code, 200)
        patch_data = patch_response.json()

        # Verify resource updates
        self.assertEqual(patch_data["name"], "updated-trace-name")
        self.assertIn("metadata", patch_data)
        self.assertEqual(patch_data["metadata"]["org_id"], org["id"])
        updated_project = self.get_project(org["id"], patch_data["metadata"]["project_id"])
        self.assertEqual(patch_data["metadata"]["project_id"], updated_project["id"])

        # Verify billing plan unchanged (installation-level billing)
        self.assertEqual(patch_data["billingPlan"]["id"], "free")

        # Verify underlying Braintrust project name was updated
        self.assertEqual(updated_project["name"], patch_data["name"])

        # Verify project still has correct resource metadata
        project_metadata = self.get_project_metadata(updated_project["id"])
        self.assertIsNotNone(project_metadata)
        self.assertIn("vercel", project_metadata)
        self.assertEqual(project_metadata["vercel"]["installation_id"], installation_id)

    def test_patch_resource_errors(self):
        """Test PATCH resource with various error cases"""
        # Create installation and resource first
        installation_id, email, name, user_claims = get_details_from_user_claims()

        put_body = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_patch_resource_errors",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": email,
                    "name": "Test User",
                },
            },
        }

        put_response = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id}",
            claims=user_claims,
            auth_type="user",
            body=put_body,
            headers={"Idempotency-Key": "unique-idempotency-key-patch-resource-errors"},
        )
        self.assertEqual(put_response.status_code, 200)

        # Create resource
        resource_body = {
            "productId": "redis",
            "name": "test-redis",
            "metadata": {"project_name": "Test Project"},
            "billingPlanId": "pro",
        }

        post_response = self.make_vercel_request(
            "POST",
            f"/installations/{installation_id}/resources",
            claims=user_claims,
            auth_type="user",
            body=resource_body,
        )
        self.assertEqual(post_response.status_code, 201)
        resource_data = post_response.json()
        resource_id = resource_data["id"]

        patch_body = {"name": "updated-name"}

        # Test 1: Request without auth token
        url = f"{VERCEL_BASE_URL}/installations/{installation_id}/resources/{resource_id}"
        response = self.run_request(
            "patch",
            url,
            headers={"Content-Type": "application/json"},
            json=patch_body,
            expect_error=True,
        )
        self.assertEqual(response.status_code, 401)
        self.assertIn("authorization", response.text.lower())

        # Test 2: Invalid JWT token
        response = self.make_vercel_request(
            "PATCH",
            f"/installations/{installation_id}/resources/{resource_id}",
            claims="invalid_claims_or_jwt_token",
            auth_type="user",
            body=patch_body,
            expect_error=True,
        )
        self.assertEqual(response.status_code, 401)
        self.assertIn("verification failed", response.text.lower())

        # Test 3: System auth instead of user auth (should fail)
        _, system_claims = get_details_from_system_claims(installation_id=installation_id)

        response = self.make_vercel_request(
            "PATCH",
            f"/installations/{installation_id}/resources/{resource_id}",
            claims=system_claims,
            auth_type="system",
            body=patch_body,
            expect_error=True,
        )
        self.assertEqual(response.status_code, 401)

        # Test 4: JWT installation_id doesn't match path parameter
        different_installation_id = f"icfg_{str(uuid4()).replace('-', '')}"
        _, _, _, mismatched_claims = get_details_from_user_claims(installation_id=different_installation_id)

        response = self.make_vercel_request(
            "PATCH",
            f"/installations/{installation_id}/resources/{resource_id}",
            claims=mismatched_claims,
            auth_type="user",
            body=patch_body,
            expect_error=True,
        )
        self.assertEqual(response.status_code, 403)

        # Test 5: Resource not found
        non_existent_resource_id = str(uuid4())

        response = self.make_vercel_request(
            "PATCH",
            f"/installations/{installation_id}/resources/{non_existent_resource_id}",
            claims=user_claims,
            auth_type="user",
            body=patch_body,
            expect_error=True,
        )
        self.assertEqual(response.status_code, 404)
        data = response.json()
        self.assertEqual(data["error"]["code"], "not_found")

    # ============================================
    # GET /v1/installations/{installationId}/resources/{resourceId} Tests
    # ============================================

    def test_get_resource_success(self):
        """Test successful retrieval of resource"""
        # Create installation first
        installation_id, email, name, user_claims = get_details_from_user_claims()

        # Get system claims for the same installation
        _, system_claims = get_details_from_system_claims(installation_id=installation_id)

        idempotency_key = "unique-idempotency-key-get-resource-success"

        put_body = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_get_resource_success",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": email,
                    "name": "Test User",
                },
            },
        }

        put_response = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id}",
            claims=user_claims,
            auth_type="user",
            body=put_body,
            headers={"Idempotency-Key": idempotency_key},
        )
        self.assertEqual(put_response.status_code, 200)

        # Get the org that was created
        org = self.get_org_by_installation_id(installation_id)
        self.assertIsNotNone(org)

        # Create resource
        resource_body = {
            "productId": "traces",
            "name": "test-get-resource",
            "metadata": {},
            "billingPlanId": "free",
        }

        post_response = self.make_vercel_request(
            "POST",
            f"/installations/{installation_id}/resources",
            claims=user_claims,
            auth_type="user",
            body=resource_body,
        )
        self.assertEqual(post_response.status_code, 201)
        post_data = post_response.json()
        resource_id = post_data["id"]

        # Now test GET with system claims
        get_response = self.make_vercel_request(
            "GET",
            f"/installations/{installation_id}/resources/{resource_id}",
            claims=system_claims,
            auth_type="system",
            body={},  # GET requests don't need a body
        )

        self.assertEqual(get_response.status_code, 200)
        get_data = get_response.json()

        # Verify resource structure matches POST response
        self.assertEqual(get_data["id"], resource_id)
        self.assertEqual(get_data["name"], "test-get-resource")
        self.assertEqual(get_data["productId"], "traces")
        self.assertEqual(get_data["status"], "ready")

        # Verify metadata includes bt resource ids
        self.assertIn("metadata", get_data)
        self.assertEqual(get_data["metadata"]["org_id"], org["id"])
        self.assertEqual(
            get_data["metadata"]["project_id"], self.get_project(org["id"], get_data["metadata"]["project_id"])["id"]
        )

        # Verify billing plan
        self.assertEqual(get_data["billingPlan"]["id"], "free")

        # Verify no secrets in response
        self.assertNotIn("secrets", get_data)

    def test_get_resource_errors(self):
        """Test GET resource with various error cases"""
        # Create installation and resource first
        installation_id, email, name, user_claims = get_details_from_user_claims()

        # Get system claims for the same installation
        _, system_claims = get_details_from_system_claims(installation_id=installation_id)

        put_body = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_get_resource_errors",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": email,
                    "name": "Test User",
                },
            },
        }

        put_response = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id}",
            claims=user_claims,
            auth_type="user",
            body=put_body,
            headers={"Idempotency-Key": "unique-idempotency-key-get-resource-errors"},
        )
        self.assertEqual(put_response.status_code, 200)

        # Create resource
        resource_body = {
            "productId": "traces",
            "name": "test-get-resource-errors",
            "metadata": {},
            "billingPlanId": "free",
        }

        post_response = self.make_vercel_request(
            "POST",
            f"/installations/{installation_id}/resources",
            claims=user_claims,
            auth_type="user",
            body=resource_body,
        )
        self.assertEqual(post_response.status_code, 201)
        resource_data = post_response.json()
        resource_id = resource_data["id"]

        # Test 1: Request without auth token
        url = f"{VERCEL_BASE_URL}/installations/{installation_id}/resources/{resource_id}"
        response = self.run_request(
            "get",
            url,
            headers={"Content-Type": "application/json"},
            json={},
            expect_error=True,
        )
        self.assertEqual(response.status_code, 401)
        self.assertIn("authorization", response.text.lower())

        # Test 2: Invalid JWT token
        response = self.make_vercel_request(
            "GET",
            f"/installations/{installation_id}/resources/{resource_id}",
            claims="invalid_claims_or_jwt_token",
            auth_type="system",
            body={},
            expect_error=True,
        )
        self.assertEqual(response.status_code, 401)
        self.assertIn("verification failed", response.text.lower())

        # Test 3: Use user auth instead of system auth (should fail)
        response = self.make_vercel_request(
            "GET",
            f"/installations/{installation_id}/resources/{resource_id}",
            claims=user_claims,
            auth_type="user",
            body={},
            expect_error=True,
        )
        # Will fail with 401 (user auth not allowed for GET)
        self.assertEqual(response.status_code, 401)

        # Test 4: Resource not found
        non_existent_resource_id = str(uuid4())

        response = self.make_vercel_request(
            "GET",
            f"/installations/{installation_id}/resources/{non_existent_resource_id}",
            claims=system_claims,
            auth_type="system",
            body={},
            expect_error=True,
        )
        self.assertEqual(response.status_code, 404)
        data = response.json()
        self.assertEqual(data["error"]["code"], "not_found")

        # Test 5: JWT installation_id doesn't match path parameter
        different_installation_id = f"icfg_{str(uuid4()).replace('-', '')}"
        _, mismatched_system_claims = get_details_from_system_claims(installation_id=different_installation_id)

        response = self.make_vercel_request(
            "GET",
            f"/installations/{installation_id}/resources/{resource_id}",  # Using original installation_id
            claims=mismatched_system_claims,  # But claims have different installation_id
            auth_type="system",
            body={},
            expect_error=True,
        )
        self.assertEqual(response.status_code, 403)
        data = response.json()
        self.assertEqual(data["error"]["code"], "forbidden")
        self.assertIn("Access denied", data["error"]["message"])

    # ============================================
    # DELETE /v1/installations/{installationId}/resources/{resourceId} Tests
    # ============================================

    def test_delete_resource_success(self):
        """Test successful deletion of resource

        NOTE: Resource deletion marks the resource as deleted via metadata.deleted = true
        rather than physically deleting it. This behavior may change in the future.
        """
        # Create installation first
        installation_id, email, name, user_claims = get_details_from_user_claims()

        idempotency_key = "unique-idempotency-key-delete-resource-success"

        put_body = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_delete_resource_success",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": email,
                    "name": "Test User",
                },
            },
        }

        put_response = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id}",
            claims=user_claims,
            auth_type="user",
            body=put_body,
            headers={"Idempotency-Key": idempotency_key},
        )
        self.assertEqual(put_response.status_code, 200)

        # Get the org that was created
        org = self.get_org_by_installation_id(installation_id)
        self.assertIsNotNone(org)

        # Create resource
        resource_body = {
            "productId": "traces",
            "name": "test-delete-resource",
            "metadata": {},
            "billingPlanId": "free",
        }

        post_response = self.make_vercel_request(
            "POST",
            f"/installations/{installation_id}/resources",
            claims=user_claims,
            auth_type="user",
            body=resource_body,
        )
        self.assertEqual(post_response.status_code, 201)
        post_data = post_response.json()
        resource_id = post_data["id"]
        project_id = post_data["secrets"][1]["value"]  # BRAINTRUST_PROJECT_ID

        # Verify project exists before deletion
        project = self.get_project(org["id"], project_id)
        self.assertIsNotNone(project, "Project should exist before deletion")

        # Save api key to verify deletion
        secrets = {secret["name"]: secret["value"] for secret in post_data["secrets"]}
        api_key = secrets["BRAINTRUST_API_KEY"]

        # Delete the resource
        delete_response = self.make_vercel_request(
            "DELETE",
            f"/installations/{installation_id}/resources/{resource_id}",
            claims=user_claims,
            auth_type="user",
            body={},  # DELETE requests don't need a body
        )

        # Should return 204 No Content
        self.assertEqual(delete_response.status_code, 204)
        self.assertEqual(delete_response.text, "")

        # Verify project still exists but is marked as deleted in metadata
        project_after_delete = self.get_project(org["id"], project_id)
        self.assertIsNotNone(project_after_delete, "Project should still exist after soft delete")
        self.assertEqual(project_after_delete["id"], project["id"], "Same project ID")

        # Check project metadata for deletion marker
        project_metadata = self.get_project_metadata(project["id"])
        self.assertIsNotNone(project_metadata)
        self.assertIn("vercel", project_metadata)

        deleted_at = project_metadata.get("vercel", {}).get("deleted_at")
        self.assertIsNotNone(deleted_at, "Project metadata should have vercel.deleted_at timestamp")
        self.assertIsInstance(deleted_at, str, "deleted_at should be a timestamp string")

        # Verify service token was deleted
        service_token_row = self.get_service_token(installation_id, api_key)
        self.assertIsNone(service_token_row, "Service token should be deleted")

        # Verify service account was removed from organization
        project_name = project["name"]
        service_account = self.get_service_account(org["id"], f"bt::sp::custom::vercel-account-{project_name}-%")
        self.assertIsNone(service_account, "Service account should be removed from organization")

        # Verify subsequent GET returns 404 (deleted resource should not be accessible)
        _, system_claims = get_details_from_system_claims(installation_id=installation_id)

        get_response = self.make_vercel_request(
            "GET",
            f"/installations/{installation_id}/resources/{resource_id}",
            claims=system_claims,
            auth_type="system",
            body={},
            expect_error=True,
        )
        self.assertEqual(get_response.status_code, 404)
        data = get_response.json()
        self.assertEqual(data["error"]["code"], "not_found")

        # Test idempotent deletion: Delete again, should return 404 (already deleted)
        delete_response2 = self.make_vercel_request(
            "DELETE",
            f"/installations/{installation_id}/resources/{resource_id}",
            claims=user_claims,
            auth_type="user",
            body={},
            expect_error=True,
        )
        self.assertEqual(delete_response2.status_code, 404)
        data2 = delete_response2.json()
        self.assertEqual(data2["error"]["code"], "not_found")

    def test_delete_resource_errors(self):
        """Test DELETE resource with various error cases"""
        # Create installation and resource first
        installation_id, email, name, user_claims = get_details_from_user_claims()

        put_body = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_delete_resource_errors",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": email,
                    "name": "Test User",
                },
            },
        }

        put_response = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id}",
            claims=user_claims,
            auth_type="user",
            body=put_body,
            headers={"Idempotency-Key": "unique-idempotency-key-delete-resource-errors"},
        )
        self.assertEqual(put_response.status_code, 200)

        # Create resource
        resource_body = {
            "productId": "traces",
            "name": "test-delete-resource-errors",
            "metadata": {},
            "billingPlanId": "free",
        }

        post_response = self.make_vercel_request(
            "POST",
            f"/installations/{installation_id}/resources",
            claims=user_claims,
            auth_type="user",
            body=resource_body,
        )
        self.assertEqual(post_response.status_code, 201)
        resource_data = post_response.json()
        resource_id = resource_data["id"]

        # Test 1: Request without auth token
        url = f"{VERCEL_BASE_URL}/installations/{installation_id}/resources/{resource_id}"
        response = self.run_request(
            "delete",
            url,
            headers={"Content-Type": "application/json"},
            json={},
            expect_error=True,
        )
        self.assertEqual(response.status_code, 401)
        self.assertIn("authorization", response.text.lower())

        # Test 2: Invalid JWT token
        response = self.make_vercel_request(
            "DELETE",
            f"/installations/{installation_id}/resources/{resource_id}",
            claims="invalid_claims_or_jwt_token",
            auth_type="user",
            body={},
            expect_error=True,
        )
        self.assertEqual(response.status_code, 401)
        self.assertIn("verification failed", response.text.lower())

        # Test 3: Use system auth instead of user auth (should fail)
        _, system_claims = get_details_from_system_claims(installation_id=installation_id)

        response = self.make_vercel_request(
            "DELETE",
            f"/installations/{installation_id}/resources/{resource_id}",
            claims=system_claims,
            auth_type="system",
            body={},
            expect_error=True,
        )
        # Will fail with 401 (system auth not allowed for DELETE)
        self.assertEqual(response.status_code, 401)

        # Test 4: JWT installation_id doesn't match path parameter
        different_installation_id = f"icfg_{str(uuid4()).replace('-', '')}"
        _, _, _, mismatched_claims = get_details_from_user_claims(installation_id=different_installation_id)

        response = self.make_vercel_request(
            "DELETE",
            f"/installations/{installation_id}/resources/{resource_id}",
            claims=mismatched_claims,
            auth_type="user",
            body={},
            expect_error=True,
        )
        self.assertEqual(response.status_code, 403)
        data = response.json()
        self.assertEqual(data["error"]["code"], "forbidden")
        self.assertIn("Access denied", data["error"]["message"])

        # Test 5: Resource not found (should return 404 since resource lookup happens first)
        non_existent_resource_id = str(uuid4())

        response = self.make_vercel_request(
            "DELETE",
            f"/installations/{installation_id}/resources/{non_existent_resource_id}",
            claims=user_claims,
            auth_type="user",
            body={},
            expect_error=True,
        )
        # Non-existent resource returns 404 (resource lookup fails before delete logic)
        self.assertEqual(response.status_code, 404)
        data = response.json()
        self.assertEqual(data["error"]["code"], "not_found")

    def test_delete_installation_cascade(self):
        """Test that cascade delete only affects Vercel-related resources and leaves regular resources intact"""
        # Create installation first
        installation_id, email, name, user_claims = get_details_from_user_claims()
        idempotency_key = "unique-idempotency-key-cascade-selective"

        put_body = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_cascade_selective",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": email,
                    "name": "Test User",
                },
            },
        }

        put_response = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id}",
            claims=user_claims,
            auth_type="user",
            body=put_body,
            headers={"Idempotency-Key": idempotency_key},
        )
        self.assertEqual(put_response.status_code, 200)

        # Get the org and user that was created
        org = self.get_org_by_installation_id(installation_id)
        self.assertIsNotNone(org)
        user = self.get_user(email)
        self.assertIsNotNone(user)

        # Create some Vercel resources through provision endpoint
        vercel_resource1_body = {
            "productId": "traces",
            "name": "vercel-project-1",
            "metadata": {},
            "billingPlanId": "free",
        }

        vercel_resource1_response = self.make_vercel_request(
            "POST",
            f"/installations/{installation_id}/resources",
            claims=user_claims,
            auth_type="user",
            body=vercel_resource1_body,
        )
        self.assertEqual(vercel_resource1_response.status_code, 201)
        vercel_resource1_data = vercel_resource1_response.json()
        vercel_project1_id = vercel_resource1_data["metadata"]["project_id"]
        vercel_api_key1 = vercel_resource1_data["secrets"][0]["value"]  # BRAINTRUST_API_KEY

        vercel_resource2_body = {
            "productId": "traces",
            "name": "vercel-project-2",
            "metadata": {},
            "billingPlanId": "free",
        }

        vercel_resource2_response = self.make_vercel_request(
            "POST",
            f"/installations/{installation_id}/resources",
            claims=user_claims,
            auth_type="user",
            body=vercel_resource2_body,
        )
        self.assertEqual(vercel_resource2_response.status_code, 201)
        vercel_resource2_data = vercel_resource2_response.json()
        vercel_project2_id = vercel_resource2_data["metadata"]["project_id"]
        vercel_api_key2 = vercel_resource2_data["secrets"][0]["value"]  # BRAINTRUST_API_KEY

        # Create some regular (non-Vercel) projects using base test helpers
        regular_project1_response = self.registerProject({"project_name": "regular-project-1"})
        regular_project1_id = regular_project1_response["project"]["id"]

        regular_project2_response = self.registerProject({"project_name": "regular-project-2"})
        regular_project2_id = regular_project2_response["project"]["id"]

        # Create regular API keys using base test helpers
        regular_api_key1 = self.createUserOrgApiKey(user["id"], org["id"])
        regular_api_key2 = self.createUserOrgApiKey(user["id"], org["id"])

        # Verify initial state - Vercel resources exist
        self.assertIsNotNone(self.get_service_token(installation_id, vercel_api_key1))
        self.assertIsNotNone(self.get_service_token(installation_id, vercel_api_key2))

        # Verify service accounts exist
        service_account1 = self.get_service_account(org["id"], "bt::sp::custom::vercel-account-vercel-project-1-%")
        self.assertIsNotNone(service_account1, "Service account 1 should exist before deletion")
        service_account2 = self.get_service_account(org["id"], "bt::sp::custom::vercel-account-vercel-project-2-%")
        self.assertIsNotNone(service_account2, "Service account 2 should exist before deletion")

        vercel_project1_metadata = self.get_project_metadata(vercel_project1_id)
        self.assertIsNotNone(vercel_project1_metadata)
        self.assertIn("vercel", vercel_project1_metadata)
        self.assertEqual(vercel_project1_metadata["vercel"]["installation_id"], installation_id)

        vercel_project2_metadata = self.get_project_metadata(vercel_project2_id)
        self.assertIsNotNone(vercel_project2_metadata)
        self.assertIn("vercel", vercel_project2_metadata)
        self.assertEqual(vercel_project2_metadata["vercel"]["installation_id"], installation_id)

        # Verify initial state - Regular resources exist and have no Vercel metadata
        regular_project1_metadata = self.get_project_metadata(regular_project1_id)
        self.assertIsNone(regular_project1_metadata.get("vercel") if regular_project1_metadata else None)

        regular_project2_metadata = self.get_project_metadata(regular_project2_id)
        self.assertIsNone(regular_project2_metadata.get("vercel") if regular_project2_metadata else None)

        # Verify regular API keys exist using database query
        with self.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    "SELECT COUNT(*) FROM api_keys WHERE org_id = %s",
                    [org["id"]],
                )
                regular_api_keys_count_before = cursor.fetchone()[0]
                self.assertGreaterEqual(
                    regular_api_keys_count_before, 4
                )  # At least our 4 total API keys (2 Vercel + 2 regular)

        # Delete the installation with cascade deletion enabled
        delete_body = {"cascadeResourceDeletion": True}

        delete_response = self.make_vercel_request(
            "DELETE",
            f"/installations/{installation_id}",
            claims=user_claims,
            auth_type="user",
            body=delete_body,
        )

        # Should return 200 with finalized: true (free plan)
        self.assertEqual(delete_response.status_code, 200)
        delete_data = delete_response.json()
        self.assertEqual(delete_data["finalized"], True)

        # Verify cascade deletion behavior - Vercel service tokens should be deleted
        self.assertIsNone(self.get_service_token(installation_id, vercel_api_key1))
        self.assertIsNone(self.get_service_token(installation_id, vercel_api_key2))

        # Verify service accounts are removed
        service_account1_after = self.get_service_account(
            org["id"], "bt::sp::custom::vercel-account-vercel-project-1-%"
        )
        self.assertIsNone(service_account1_after, "Service account 1 should be removed after cascade deletion")
        service_account2_after = self.get_service_account(
            org["id"], "bt::sp::custom::vercel-account-vercel-project-2-%"
        )
        self.assertIsNone(service_account2_after, "Service account 2 should be removed after cascade deletion")

        # Verify Vercel projects are marked as deleted (have deleted_at timestamp)
        vercel_project1_metadata_after = self.get_project_metadata(vercel_project1_id)
        self.assertIsNotNone(vercel_project1_metadata_after)
        self.assertIsNotNone(
            vercel_project1_metadata_after.get("vercel", {}).get("deleted_at"),
            "Vercel project 1 should be marked as deleted",
        )

        vercel_project2_metadata_after = self.get_project_metadata(vercel_project2_id)
        self.assertIsNotNone(vercel_project2_metadata_after)
        self.assertIsNotNone(
            vercel_project2_metadata_after.get("vercel", {}).get("deleted_at"),
            "Vercel project 2 should be marked as deleted",
        )

        # Verify regular projects are unchanged (no deletion markers)
        regular_project1_metadata_after = self.get_project_metadata(regular_project1_id)
        self.assertIsNone(
            (
                regular_project1_metadata_after.get("vercel", {}).get("deleted_at")
                if regular_project1_metadata_after
                else None
            ),
            "Regular project 1 should NOT be marked as deleted",
        )

        regular_project2_metadata_after = self.get_project_metadata(regular_project2_id)
        self.assertIsNone(
            (
                regular_project2_metadata_after.get("vercel", {}).get("deleted_at")
                if regular_project2_metadata_after
                else None
            ),
            "Regular project 2 should NOT be marked as deleted",
        )

        # Verify regular API keys are unchanged (should be 2 fewer now since Vercel ones were deleted)
        with self.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    "SELECT COUNT(*) FROM api_keys WHERE org_id = %s",
                    [org["id"]],
                )
                regular_api_keys_count_after = cursor.fetchone()[0]
                self.assertEqual(
                    regular_api_keys_count_after,
                    regular_api_keys_count_before - 2,
                    "Should have 2 fewer API keys (the Vercel ones were deleted)",
                )

        # Verify that the user and org still exist but org is marked as deleted
        user_after = self.get_user(email)
        self.assertIsNotNone(user_after)
        self.assertEqual(user_after["vercel_user_ids"], user["vercel_user_ids"])

        # Verify org no longer contains the installation_id
        org_after = self.get_org_by_installation_id(installation_id)
        self.assertIsNone(org_after, "Organization should not be found after uninstall")

    # ============================================
    # Billing Plans Endpoints Tests
    # ============================================
    def test_billing_plans(self):
        """Test that all 3 plan endpoints return the same billing plans"""
        # Get user claims for creating the installation
        installation_id, email, name, user_claims = get_details_from_user_claims()

        # Get system claims for the same installation
        _, system_claims = get_details_from_system_claims(installation_id=installation_id)

        idempotency_key = "unique-idempotency-key-billing-plans-test"

        # First, create an installation using PUT
        put_body = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_billing_plans",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": email,
                    "name": "Test User",
                },
            },
        }

        put_response = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id}",
            claims=user_claims,
            auth_type="user",
            body=put_body,
            headers={"Idempotency-Key": idempotency_key},
        )
        self.assertEqual(put_response.status_code, 200)

        # Test 1: Get billing plan from installation endpoint
        get_installation_response = self.make_vercel_request(
            "GET",
            f"/installations/{installation_id}",
            claims=system_claims,
            auth_type="system",
            body={},
        )
        self.assertEqual(get_installation_response.status_code, 200)
        installation_data = get_installation_response.json()

        # Extract billing plan from installation (currently returns a single plan)
        installation_billing_plan = installation_data.get("billingPlan")

        # Test 2: Get billing plans from resource plans endpoint
        resource_id = "test_resource_id"
        resource_plans_response = self.make_vercel_request(
            "GET",
            f"/installations/{installation_id}/resources/{resource_id}/plans",
            claims=user_claims,
            auth_type="user",
            body={},
        )
        self.assertEqual(resource_plans_response.status_code, 200)
        resource_plans_data = resource_plans_response.json()

        # Test 3: Get billing plans from product plans endpoint
        product_slug = "braintrust"  # or whatever product slug is appropriate
        product_plans_response = self.make_vercel_request(
            "GET",
            f"/products/{product_slug}/plans",
            claims=system_claims,
            auth_type="system",
            body={},
        )
        self.assertEqual(product_plans_response.status_code, 200)
        product_plans_data = product_plans_response.json()

        # Verify both new endpoints return the same plans structure
        self.assertIn("plans", resource_plans_data)
        self.assertIn("plans", product_plans_data)

        # Verify the plans arrays are identical
        self.assertEqual(resource_plans_data["plans"], product_plans_data["plans"])

        # Verify the plans contain expected data
        plans = resource_plans_data["plans"]
        self.assertIsInstance(plans, list)
        self.assertGreaterEqual(len(plans), 2)  # Should have at least free and pro plans

        # Check for expected plan IDs
        plan_ids = [plan["id"] for plan in plans]
        self.assertIn("free", plan_ids)
        self.assertIn("pro", plan_ids)

        # Verify structure of each plan
        for plan in plans:
            self.assertIn("id", plan)
            self.assertIn("scope", plan)
            self.assertIn("name", plan)
            self.assertIn("cost", plan)
            self.assertIn("description", plan)
            self.assertIn("type", plan)
            self.assertIn("paymentMethodRequired", plan)
            self.assertIn("details", plan)
            self.assertIn("requiredPolicies", plan)

            # Verify scope is always "installation"
            self.assertEqual(plan["scope"], "installation")

            # Verify type is subscription
            self.assertEqual(plan["type"], "subscription")

        # Verify specific plan details
        free_plan = next(p for p in plans if p["id"] == "free")
        self.assertEqual(free_plan["name"], "Free")
        self.assertEqual(free_plan["cost"], "Free")
        self.assertFalse(free_plan["paymentMethodRequired"])

        pro_plan = next(p for p in plans if p["id"] == "pro")
        self.assertEqual(pro_plan["name"], "Pro")
        self.assertEqual(pro_plan["cost"], "$250/mo")
        self.assertTrue(pro_plan["paymentMethodRequired"])

        # If the installation endpoint returns a billingPlan, verify it matches one from the plans array
        if installation_billing_plan:
            matching_plan = next((p for p in plans if p["id"] == installation_billing_plan["id"]), None)
            self.assertIsNotNone(
                matching_plan, f"Installation billing plan {installation_billing_plan['id']} not found in plans array"
            )
            # The installation endpoint might return a subset of fields, so just check the ID matches
            self.assertEqual(installation_billing_plan["id"], matching_plan["id"])

    def test_user_multiple_installations_same_account(self):
        """Test that a user can have multiple installations with different user_ids on the same account"""
        account_id = f"team_{str(uuid4())}"
        email = generate_vercel_user_email()
        account_name = generate_vercel_account_name()

        # First installation with user_id_1
        installation_id_1 = f"icfg_{str(uuid4()).replace('-', '')}"
        user_id_1 = f"user_{str(uuid4())}"
        claims_1 = generate_user_claims(
            account_id=account_id,
            installation_id=installation_id_1,
            user_id=user_id_1,
            user_email=email,
        )

        put_body_1 = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_1",
                "token_type": "Bearer",
            },
            "account": {
                "name": account_name,
                "url": "https://example.com",
                "contact": {
                    "email": email,
                    "name": "Test User",
                },
            },
        }

        # Create first installation
        put_response_1 = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id_1}",
            claims=claims_1,
            auth_type="user",
            body=put_body_1,
        )
        self.assertEqual(put_response_1.status_code, 200)

        # Create a resource in the first installation
        resource_body_1 = {
            "productId": "traces",
            "name": "test-resource-installation-1",
            "metadata": {},
            "billingPlanId": "free",
        }

        post_response_1 = self.make_vercel_request(
            "POST",
            f"/installations/{installation_id_1}/resources",
            claims=claims_1,
            auth_type="user",
            body=resource_body_1,
        )
        self.assertEqual(post_response_1.status_code, 201)
        resource_id_1 = post_response_1.json()["id"]

        # Second installation with user_id_2 but same email and account
        installation_id_2 = f"icfg_{str(uuid4()).replace('-', '')}"
        user_id_2 = f"user_{str(uuid4())}"
        claims_2 = generate_user_claims(
            account_id=account_id,
            installation_id=installation_id_2,
            user_id=user_id_2,
            user_email=email,
        )

        put_body_2 = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_2",
                "token_type": "Bearer",
            },
            "account": {
                "name": account_name,
                "url": "https://example.com",
                "contact": {
                    "email": email,
                    "name": "Test User",
                },
            },
        }

        # Create second installation - should work with different user_id
        put_response_2 = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id_2}",
            claims=claims_2,
            auth_type="user",
            body=put_body_2,
        )
        self.assertEqual(put_response_2.status_code, 200)

        # Create a resource in the second installation
        resource_body_2 = {
            "productId": "traces",
            "name": "test-resource-installation-2",
            "metadata": {},
            "billingPlanId": "free",
        }

        post_response_2 = self.make_vercel_request(
            "POST",
            f"/installations/{installation_id_2}/resources",
            claims=claims_2,
            auth_type="user",
            body=resource_body_2,
        )
        self.assertEqual(post_response_2.status_code, 201)
        resource_id_2 = post_response_2.json()["id"]

        # User with user_id_1 can update resource from installation 1 (PATCH requires user auth)
        patch_response_1 = self.make_vercel_request(
            "PATCH",
            f"/installations/{installation_id_1}/resources/{resource_id_1}",
            claims=claims_1,
            auth_type="user",
            body={"name": "updated-resource-installation-1"},
        )
        self.assertEqual(patch_response_1.status_code, 200)

        # User with user_id_2 can update resource from installation 2
        patch_response_2 = self.make_vercel_request(
            "PATCH",
            f"/installations/{installation_id_2}/resources/{resource_id_2}",
            claims=claims_2,
            auth_type="user",
            body={"name": "updated-resource-installation-2"},
        )
        self.assertEqual(patch_response_2.status_code, 200)

        # Verify user metadata contains both user_ids as an array
        user = self.get_user(email)
        self.assertIsNotNone(user)
        self.assertIn("vercel_user_ids", user)
        # Verify the user_ids array contains exactly the expected values
        self.assertEqual(set(user["vercel_user_ids"]), {user_id_1, user_id_2})

        # Both resources should be accessible since they're in the same org
        # (both installations share the same account_id and org)

    def test_user_multiple_installations_different_accounts(self):
        """Test that a user can have multiple installations with different user_ids on different accounts"""
        email = generate_vercel_user_email()

        # First installation on account X
        account_id_x = f"team_{str(uuid4())}"
        installation_id_x = f"icfg_{str(uuid4()).replace('-', '')}"
        user_id_x = f"user_{str(uuid4())}"
        claims_x = generate_user_claims(
            account_id=account_id_x,
            installation_id=installation_id_x,
            user_id=user_id_x,
            user_email=email,
        )

        put_body_x = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_x",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": email,
                    "name": "Test User",
                },
            },
        }

        # Create installation on account X
        put_response_x = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id_x}",
            claims=claims_x,
            auth_type="user",
            body=put_body_x,
        )
        self.assertEqual(put_response_x.status_code, 200)

        # Create resource on account X
        resource_body_x = {
            "productId": "traces",
            "name": "test-resource-account-x",
            "metadata": {},
            "billingPlanId": "free",
        }

        post_response_x = self.make_vercel_request(
            "POST",
            f"/installations/{installation_id_x}/resources",
            claims=claims_x,
            auth_type="user",
            body=resource_body_x,
        )
        self.assertEqual(post_response_x.status_code, 201)
        resource_id_x = post_response_x.json()["id"]

        # Second installation on account Y
        account_id_y = f"team_{str(uuid4())}"
        installation_id_y = f"icfg_{str(uuid4()).replace('-', '')}"
        user_id_y = f"user_{str(uuid4())}"
        claims_y = generate_user_claims(
            account_id=account_id_y,
            installation_id=installation_id_y,
            user_id=user_id_y,
            user_email=email,
        )

        put_body_y = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_y",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": email,
                    "name": claims_y["user_name"],
                },
            },
        }

        # Create installation on account Y
        put_response_y = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id_y}",
            claims=claims_y,
            auth_type="user",
            body=put_body_y,
        )
        self.assertEqual(put_response_y.status_code, 200)

        # Create resource on account Y
        resource_body_y = {
            "productId": "traces",
            "name": "test-resource-account-y",
            "metadata": {},
            "billingPlanId": "free",
        }

        post_response_y = self.make_vercel_request(
            "POST",
            f"/installations/{installation_id_y}/resources",
            claims=claims_y,
            auth_type="user",
            body=resource_body_y,
        )
        self.assertEqual(post_response_y.status_code, 201)
        resource_id_y = post_response_y.json()["id"]

        # User with user_id_x can update resource from account X
        patch_response_x = self.make_vercel_request(
            "PATCH",
            f"/installations/{installation_id_x}/resources/{resource_id_x}",
            claims=claims_x,
            auth_type="user",
            body={"name": "updated-resource-account-x"},
        )
        self.assertEqual(patch_response_x.status_code, 200)

        # User with user_id_y can update resource from account Y
        patch_response_y = self.make_vercel_request(
            "PATCH",
            f"/installations/{installation_id_y}/resources/{resource_id_y}",
            claims=claims_y,
            auth_type="user",
            body={"name": "updated-resource-account-y"},
        )
        self.assertEqual(patch_response_y.status_code, 200)

        # User with user_id_x CANNOT update resource from account Y (different org)
        patch_response_cross = self.make_vercel_request(
            "PATCH",
            f"/installations/{installation_id_y}/resources/{resource_id_y}",
            claims=claims_x,
            auth_type="user",
            body={"name": "should-fail"},
            expect_error=True,
        )
        # Should fail because user_id_x is not associated with installation_id_y
        self.assertEqual(patch_response_cross.status_code, 403)

        # Verify user metadata contains both user_ids as an array
        user = self.get_user(email)
        self.assertIsNotNone(user)
        self.assertIn("vercel_user_ids", user)
        # Verify the user_ids array contains exactly the expected values
        self.assertEqual(set(user["vercel_user_ids"]), {user_id_x, user_id_y})

    def test_user_role_permissions(self):
        """Test that non-GET requests with user_role=USER are rejected with 403"""
        # Create installation first with ADMIN role to set up test data
        installation_id_admin, email_admin, _, admin_claims = get_details_from_user_claims()

        # Create an installation for testing
        put_body = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_user_role_test",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": email_admin,
                    "name": "Test Admin User",
                },
            },
        }

        # Create installation with ADMIN role
        put_response = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id_admin}",
            claims=admin_claims,
            auth_type="user",
            body=put_body,
        )
        self.assertEqual(put_response.status_code, 200)

        # Create a resource to test PATCH and DELETE on resources
        resource_body = {
            "productId": "traces",
            "name": "test-user-role-resource",
            "metadata": {},
            "billingPlanId": "free",
        }

        post_response = self.make_vercel_request(
            "POST",
            f"/installations/{installation_id_admin}/resources",
            claims=admin_claims,
            auth_type="user",
            body=resource_body,
        )
        self.assertEqual(post_response.status_code, 201)
        resource_id = post_response.json()["id"]

        # Now create claims with user_role=USER for testing
        installation_id_user, email_user, _, user_claims_base = get_details_from_user_claims()
        user_claims = {**user_claims_base, "user_role": "USER"}

        # Test 1: PUT /installations/{installationId} with user_role=USER should return 403
        put_body_user = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": "test_access_token_user_role_user",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": email_user,
                    "name": "Test Regular User",
                },
            },
        }

        response = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id_user}",
            claims=user_claims,
            auth_type="user",
            body=put_body_user,
            expect_error=True,
        )
        self.assertEqual(response.status_code, 403)
        error_data = response.json()
        self.assertEqual(error_data["error"]["code"], "forbidden")
        self.assertIn("Access denied to this operation", error_data["error"]["message"])
        # Verify no user was created
        self.assertIsNone(self.get_user(email_user))

        # Test 2: PATCH /installations/{installationId} with user_role=USER should return 403
        patch_body = {
            "installationId": installation_id_admin,
            "billingPlanId": "pro",
        }

        # Update user claims to use the admin installation ID (to test on existing installation)
        user_claims_for_admin_install = {**user_claims, "installation_id": installation_id_admin}

        response = self.make_vercel_request(
            "PATCH",
            f"/installations/{installation_id_admin}",
            claims=user_claims_for_admin_install,
            auth_type="user",
            body=patch_body,
            expect_error=True,
        )
        self.assertEqual(response.status_code, 403)
        error_data = response.json()
        self.assertEqual(error_data["error"]["code"], "forbidden")
        self.assertIn("Access denied to this operation", error_data["error"]["message"])

        # Test 3: DELETE /installations/{installationId} with user_role=USER should return 403
        delete_body = {
            "installationId": installation_id_admin,
            "cascadeResourceDeletion": False,
        }

        response = self.make_vercel_request(
            "DELETE",
            f"/installations/{installation_id_admin}",
            claims=user_claims_for_admin_install,
            auth_type="user",
            body=delete_body,
            expect_error=True,
        )
        self.assertEqual(response.status_code, 403)
        error_data = response.json()
        self.assertEqual(error_data["error"]["code"], "forbidden")
        self.assertIn("Access denied to this operation", error_data["error"]["message"])

        # Test 4: POST /installations/{installationId}/resources with user_role=USER should return 403
        resource_body_user = {
            "productId": "traces",
            "name": "test-user-role-resource-2",
            "metadata": {},
            "billingPlanId": "free",
        }

        response = self.make_vercel_request(
            "POST",
            f"/installations/{installation_id_admin}/resources",
            claims=user_claims_for_admin_install,
            auth_type="user",
            body=resource_body_user,
            expect_error=True,
        )
        self.assertEqual(response.status_code, 403)
        error_data = response.json()
        self.assertEqual(error_data["error"]["code"], "forbidden")
        self.assertIn("Access denied to this operation", error_data["error"]["message"])

        # Test 5: PATCH /installations/{installationId}/resources/{resourceId} with user_role=USER should return 403
        patch_resource_body = {
            "billingPlanId": "pro",
        }

        response = self.make_vercel_request(
            "PATCH",
            f"/installations/{installation_id_admin}/resources/{resource_id}",
            claims=user_claims_for_admin_install,
            auth_type="user",
            body=patch_resource_body,
            expect_error=True,
        )
        self.assertEqual(response.status_code, 403)
        error_data = response.json()
        self.assertEqual(error_data["error"]["code"], "forbidden")
        self.assertIn("Access denied to this operation", error_data["error"]["message"])

        # Test 6: DELETE /installations/{installationId}/resources/{resourceId} with user_role=USER should return 403
        response = self.make_vercel_request(
            "DELETE",
            f"/installations/{installation_id_admin}/resources/{resource_id}",
            claims=user_claims_for_admin_install,
            auth_type="user",
            body={},
            expect_error=True,
        )
        self.assertEqual(response.status_code, 403)
        error_data = response.json()
        self.assertEqual(error_data["error"]["code"], "forbidden")
        self.assertIn("Access denied to this operation", error_data["error"]["message"])

        # Test 7: Positive test - GET requests with user_role=USER should still work
        # Note: GET on installations uses system auth, so we test GET on a user endpoint that exists
        # Actually, looking at the code, there are no GET endpoints with user auth
        # The GET endpoints use system auth, so we can't test this scenario
        # However, we can verify that the installation is still intact after all the failed attempts

        # Verify the installation still exists and wasn't deleted
        _, system_claims = get_details_from_system_claims(installation_id=installation_id_admin)
        get_response = self.make_vercel_request(
            "GET",
            f"/installations/{installation_id_admin}",
            claims=system_claims,
            auth_type="system",
            body={},
        )
        self.assertEqual(get_response.status_code, 200)

        # Verify the resource still exists
        get_resource_response = self.make_vercel_request(
            "GET",
            f"/installations/{installation_id_admin}/resources/{resource_id}",
            claims=system_claims,
            auth_type="system",
            body={},
        )
        self.assertEqual(get_resource_response.status_code, 200)

    def test_user_email_change_basic(self):
        """Test that user email changes result in rejection"""
        # Create initial installation with user
        installation_id, original_email, user_name, claims = get_details_from_user_claims()
        account_name = generate_vercel_account_name()

        put_body = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": f"test_access_token_{str(uuid4())}",
                "token_type": "Bearer",
            },
            "account": {
                "name": account_name,
                "url": "https://example.com",
                "contact": {
                    "email": original_email,
                    "name": user_name,
                },
            },
        }

        response = self.make_vercel_request(
            "PUT", f"/installations/{installation_id}", claims=claims, auth_type="user", body=put_body
        )
        self.assertEqual(response.status_code, 200)

        # Verify original user was created
        original_user = self.get_user(original_email)
        self.assertIsNotNone(original_user)
        self.assertIn(claims["user_id"], original_user["vercel_user_ids"])

        # Get the org that was created
        org = self.get_org_by_installation_id(installation_id)
        self.assertEqual(org["name"], account_name)

        # Now simulate email change - same user_id but different email
        new_email = generate_vercel_user_email()
        changed_claims = {**claims, "user_email": new_email}

        # Try to create a resource with the new email - should get 403
        resource_body = {
            "name": f"test-project-{str(uuid4())[:8]}",
            "productId": "traces",
            "metadata": {},
            "billingPlanId": "free",
        }
        post_response = self.make_vercel_request(
            "POST",
            f"/installations/{installation_id}/resources",
            claims=changed_claims,
            auth_type="user",
            body=resource_body,
            expect_error=True,
        )
        self.assertEqual(post_response.status_code, 403)
        error_data = post_response.json()
        self.assertEqual(error_data["error"]["code"], "user_email_changed")
        self.assertIn("email", error_data["error"]["user"]["message"].lower())

    def test_user_email_change_graceful_error(self):
        """Test that email changes result in graceful 403 errors"""
        # Create initial installation with user
        installation_id, original_email, user_name, claims = get_details_from_user_claims()
        account_name = generate_vercel_account_name()

        put_body = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": f"test_access_token_{str(uuid4())}",
                "token_type": "Bearer",
            },
            "account": {
                "name": account_name,
                "url": "https://example.com",
                "contact": {
                    "email": original_email,
                    "name": user_name,
                },
            },
        }

        response = self.make_vercel_request(
            "PUT", f"/installations/{installation_id}", claims=claims, auth_type="user", body=put_body
        )
        self.assertEqual(response.status_code, 200)

        # Create a resource with original email
        resource_body = {
            "name": f"test-project-{str(uuid4())[:8]}",
            "productId": "traces",
            "metadata": {},
            "billingPlanId": "free",
        }
        post_response = self.make_vercel_request(
            "POST",
            f"/installations/{installation_id}/resources",
            claims=claims,
            auth_type="user",
            body=resource_body,
        )
        self.assertEqual(post_response.status_code, 201)
        resource_id = post_response.json()["id"]

        # Now simulate email change - same user_id but different email
        new_email = generate_vercel_user_email()
        changed_claims = {**claims, "user_email": new_email}

        # Try to update the resource with changed email - should get 403
        update_body = {"name": f"updated-project-{str(uuid4())[:8]}"}
        update_response = self.make_vercel_request(
            "PATCH",
            f"/installations/{installation_id}/resources/{resource_id}",
            claims=changed_claims,
            auth_type="user",
            body=update_body,
            expect_error=True,
        )
        self.assertEqual(update_response.status_code, 403)
        error_data = update_response.json()
        self.assertEqual(error_data["error"]["code"], "user_email_changed")
        self.assertIn("email", error_data["error"]["user"]["message"].lower())

        # Try to delete the resource with changed email - should get 403
        delete_response = self.make_vercel_request(
            "DELETE",
            f"/installations/{installation_id}/resources/{resource_id}",
            claims=changed_claims,
            auth_type="user",
            body={},
            expect_error=True,
        )
        self.assertEqual(delete_response.status_code, 403)
        error_data = delete_response.json()
        self.assertEqual(error_data["error"]["code"], "user_email_changed")

    def test_user_email_change_multiple_installations(self):
        """Test that email changes result in rejection for multiple installations"""
        # Create first installation
        installation_id_1, original_email, user_name, claims_1 = get_details_from_user_claims()
        account_name_1 = generate_vercel_account_name()

        put_body_1 = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": f"test_access_token_{str(uuid4())}",
                "token_type": "Bearer",
            },
            "account": {
                "name": account_name_1,
                "url": "https://example.com",
                "contact": {
                    "email": original_email,
                    "name": user_name,
                },
            },
        }

        response_1 = self.make_vercel_request(
            "PUT", f"/installations/{installation_id_1}", claims=claims_1, auth_type="user", body=put_body_1
        )
        self.assertEqual(response_1.status_code, 200)

        # Create second installation with same email but different user_id
        installation_id_2 = f"icfg_{str(uuid4()).replace('-', '')}"
        user_id_2 = f"user_{str(uuid4())}"
        claims_2 = {**claims_1, "installation_id": installation_id_2, "user_id": user_id_2}
        account_name_2 = generate_vercel_account_name()

        put_body_2 = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": f"test_access_token_{str(uuid4())}",
                "token_type": "Bearer",
            },
            "account": {
                "name": account_name_2,
                "url": "https://example.com",
                "contact": {
                    "email": original_email,
                    "name": user_name,
                },
            },
        }

        response_2 = self.make_vercel_request(
            "PUT", f"/installations/{installation_id_2}", claims=claims_2, auth_type="user", body=put_body_2
        )
        self.assertEqual(response_2.status_code, 200)

        # Verify user has both user_ids
        original_user = self.get_user(original_email)
        self.assertIsNotNone(original_user)
        self.assertIn(claims_1["user_id"], original_user["vercel_user_ids"])
        self.assertIn(user_id_2, original_user["vercel_user_ids"])

        # Now change email and try to access through first installation - should get 403
        new_email = generate_vercel_user_email()
        changed_claims_1 = {**claims_1, "user_email": new_email}

        resource_body = {
            "name": f"test-project-{str(uuid4())[:8]}",
            "productId": "traces",
            "metadata": {},
            "billingPlanId": "free",
        }
        post_response = self.make_vercel_request(
            "POST",
            f"/installations/{installation_id_1}/resources",
            claims=changed_claims_1,
            auth_type="user",
            body=resource_body,
            expect_error=True,
        )
        self.assertEqual(post_response.status_code, 403)
        error_data = post_response.json()
        self.assertEqual(error_data["error"]["code"], "user_email_changed")
        self.assertIn("email", error_data["error"]["user"]["message"].lower())

        # Try to access through second installation with new email - should also get 403
        changed_claims_2 = {**claims_2, "user_email": new_email}
        resource_body_2 = {
            "name": f"test-project-{str(uuid4())[:8]}",
            "productId": "traces",
            "metadata": {},
            "billingPlanId": "free",
        }
        post_response_2 = self.make_vercel_request(
            "POST",
            f"/installations/{installation_id_2}/resources",
            claims=changed_claims_2,
            auth_type="user",
            body=resource_body_2,
            expect_error=True,
        )
        self.assertEqual(post_response_2.status_code, 403)
        error_data_2 = post_response_2.json()
        self.assertEqual(error_data_2["error"]["code"], "user_email_changed")
        self.assertIn("email", error_data_2["error"]["user"]["message"].lower())

    def _generate_signature(self, payload: str) -> str:
        """Generate HMAC-SHA1 signature for webhook payload."""
        return hmac.new(self.webhook_secret.encode(), payload.encode(), hashlib.sha1).hexdigest()

    def _send_webhook(
        self,
        event: Dict[str, Any],
        signature: Optional[str] = None,
        raw_body: Optional[str] = None,
        expect_error: bool = False,
    ) -> requests.Response:
        """Send webhook request to the endpoint."""
        if raw_body is None:
            raw_body = json.dumps(event)

        if signature is None:
            signature = self._generate_signature(raw_body)

        headers = {
            "Content-Type": "application/json",
            "x-vercel-signature": signature,
        }

        return self.run_request(
            "post",
            self.webhook_url,
            data=raw_body,
            headers=headers,
            expect_error=expect_error,
        )

    # ============================================
    # General Webhook Handling Tests
    # ============================================

    def test_webhook_signature_verification_valid(self):
        """Test that valid signatures are accepted."""
        event = {
            "id": str(uuid4()),
            "type": "integration-configuration.removed",
            "createdAt": int(time.time() * 1000),
            "payload": {"configuration": {"id": "cfg_test123"}},
        }

        response = self._send_webhook(event)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["success"], True)

    def test_webhook_signature_verification_invalid(self):
        """Test that invalid signatures are rejected."""
        event = {
            "id": str(uuid4()),
            "type": "integration-configuration.removed",
            "createdAt": int(time.time() * 1000),
            "payload": {"configuration": {"id": "cfg_test123"}},
        }

        # Send with invalid signature
        response = self._send_webhook(event, signature="invalid_signature", expect_error=True)
        self.assertEqual(response.status_code, 401)
        self.assertEqual(response.json()["error"], "Invalid signature")

    def test_webhook_missing_signature(self):
        """Test that requests without signatures are rejected."""
        event = {"id": str(uuid4()), "type": "test.event", "createdAt": int(time.time() * 1000), "payload": {}}

        headers = {
            "Content-Type": "application/json",
        }

        response = self.run_request(
            "post",
            self.webhook_url,
            json=event,
            headers=headers,
            expect_error=True,
        )

        self.assertEqual(response.status_code, 401)
        self.assertEqual(response.json()["error"], "Invalid signature")

    def test_webhook_malformed_payload(self):
        """Test that malformed JSON is rejected."""
        malformed_json = '{"invalid": json,}'
        signature = self._generate_signature(malformed_json)

        headers = {
            "Content-Type": "application/json",
            "x-vercel-signature": signature,
        }

        response = self.run_request(
            "post",
            self.webhook_url,
            data=malformed_json,
            headers=headers,
            expect_error=True,
        )

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.json()["error"], "Invalid JSON")

    def test_webhook_unknown_event(self):
        """Test that unknown events are handled gracefully."""
        event = {
            "id": str(uuid4()),
            "type": "unknown.event.type",
            "createdAt": int(time.time() * 1000),
            "payload": {"test": "data"},
        }

        response = self._send_webhook(event, expect_error=True)
        # Unknown events should return 400 based on the updated route logic
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.json()["error"], "Unknown webhook event")

    def test_webhook_invalid_event_format(self):
        """Test that events with invalid format are rejected."""
        # Missing required fields
        event = {
            "type": "test.event",
            # Missing id and createdAt
            "payload": {},
        }

        response = self._send_webhook(event, expect_error=True)
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.json()["error"], "Invalid webhook event format")

    # ============================================
    # Webhook Tests for marketplace.member.changed
    # ============================================

    def _create_installation(self):
        installation_id, owner_email, owner_name, owner_claims = get_details_from_user_claims()

        put_body = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": f"test_access_token_{installation_id}",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": owner_email,
                    "name": owner_name,
                },
            },
        }

        put_response = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id}",
            claims=owner_claims,
            auth_type="user",
            body=put_body,
            headers={"Idempotency-Key": f"idempotency_{installation_id}"},
        )

        # Get org that was created
        org = self.get_org_by_installation_id(installation_id)
        org_id = org["id"]
        return installation_id, org_id, owner_email, owner_claims

    def _add_vercel_user_to_org(self, org_id, owner_email, email, vercel_user_id, add_to_owners=False):
        # Get owner's API key to add members
        owner_user = self.get_user(owner_email)
        owner_api_key = self.createUserOrgApiKey(owner_user["id"], org_id)

        # Add the new member (with no group, so they're just a regular member)
        result = self.run_server_action(
            owner_api_key,
            "addMembers",
            dict(orgId=org_id, users=dict(emails=[email], group_names=["Owners" if add_to_owners else "Viewers"])),
        ).json()

        # Get the created user
        new_member = self.get_user(email)
        self.assertIsNotNone(new_member)
        new_member_user_id = new_member["id"]

        # Create the vercel user mapping manually
        with self.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    "INSERT INTO vercel_user_mappings (user_id, vercel_user_id) VALUES (%s, %s)",
                    (new_member_user_id, vercel_user_id),
                )
                conn.commit()

    def test_webhook_marketplace_member_changed_to_admin(self):
        """Test that marketplace.member.changed webhook with ADMIN role adds user to Owners group."""

        installation_id, org_id, owner_email, owner_claims = self._create_installation()

        # Create a resource first
        resource_body = {
            "productId": "traces",
            "name": f"test-traces-{installation_id[:8]}",
            "metadata": {},
            "billingPlanId": "free",
        }

        resource_response = self.make_vercel_request(
            "POST",
            f"/installations/{installation_id}/resources",
            claims=owner_claims,
            body=resource_body,
            auth_type="user",
        )
        self.assertEqual(resource_response.status_code, 201)

        # Create a second user using addMembers and add them to no group initially
        new_member_email = generate_vercel_user_email()
        new_member_vercel_id = f"user_{str(uuid4())}"
        self._add_vercel_user_to_org(org_id, owner_email, new_member_email, new_member_vercel_id)

        # Verify new member is NOT in Owners group initially
        self.assertEqual(self.get_user_groups(org_id, new_member_email), ["Viewers"])

        # Send webhook to make them admin
        event = {
            "id": str(uuid4()),
            "type": "marketplace.member.changed",
            "createdAt": int(time.time() * 1000),
            "payload": {
                "configuration": {"id": f"cfg_{uuid4()}"},
                "installationId": installation_id,
                "memberId": new_member_vercel_id,
                "role": "ADMIN",
            },
        }

        response = self._send_webhook(event)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["success"], True)

        # Verify the new member is now in the Owners group
        self.assertEqual(self.get_user_groups(org_id, new_member_email), ["Owners"])

        # Verify member is in the org
        members = self.get_org_members(org_id)
        member_emails = [m["email"] for m in members]
        self.assertIn(new_member_email, member_emails)

    def test_webhook_marketplace_member_changed_to_user(self):
        """Test that marketplace.member.changed webhook with USER role moves user to Viewers group."""

        # Create installation with first owner
        installation_id, owner1_email, owner1_name, owner1_claims = get_details_from_user_claims()

        put_body = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": f"test_access_token_{installation_id}",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": owner1_email,
                    "name": owner1_name,
                },
            },
        }

        put_response = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id}",
            claims=owner1_claims,
            auth_type="user",
            body=put_body,
            headers={"Idempotency-Key": f"idempotency_{installation_id}"},
        )
        self.assertEqual(put_response.status_code, 200)

        # Get org that was created
        org = self.get_org_by_installation_id(installation_id)
        self.assertIsNotNone(org)
        org_id = org["id"]

        # Create a resource
        resource_body = {
            "productId": "traces",
            "name": f"test-traces-{installation_id[:8]}",
            "metadata": {},
            "billingPlanId": "free",
        }

        resource_response = self.make_vercel_request(
            "POST",
            f"/installations/{installation_id}/resources",
            claims=owner1_claims,
            body=resource_body,
            auth_type="user",
        )
        self.assertEqual(resource_response.status_code, 201)

        # Create second owner using addMembers
        owner2_email = generate_vercel_user_email()
        owner2_vercel_id = f"user_{str(uuid4())}"
        self._add_vercel_user_to_org(org_id, owner1_email, owner2_email, owner2_vercel_id, add_to_owners=True)

        # Both should be owners initially
        self.assertEqual(self.get_user_groups(org_id, owner1_email), ["Owners"])
        self.assertEqual(self.get_user_groups(org_id, owner2_email), ["Owners"])

        # Change owner2 to USER role via webhook
        event = {
            "id": str(uuid4()),
            "type": "marketplace.member.changed",
            "createdAt": int(time.time() * 1000),
            "payload": {
                "configuration": {"id": f"cfg_{uuid4()}"},
                "installationId": installation_id,
                "memberId": owner2_vercel_id,
                "role": "USER",
            },
        }

        response = self._send_webhook(event)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["success"], True)

        # Verify owner2 is now in Viewers group
        self.assertEqual(self.get_user_groups(org_id, owner2_email), ["Viewers"])

        # owner1 should still be an owner
        self.assertEqual(self.get_user_groups(org_id, owner1_email), ["Owners"])

    def test_webhook_marketplace_member_changed_to_user_last_owner_rejected(self):
        """Test that webhook rejects demoting the last owner to USER role."""

        # Create installation with only one owner
        installation_id, owner_email, owner_name, owner_claims = get_details_from_user_claims()

        put_body = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": f"test_access_token_{installation_id}",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": owner_email,
                    "name": owner_name,
                },
            },
        }

        put_response = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id}",
            claims=owner_claims,
            auth_type="user",
            body=put_body,
            headers={"Idempotency-Key": f"idempotency_{installation_id}"},
        )
        self.assertEqual(put_response.status_code, 200)

        # Get org that was created
        org = self.get_org_by_installation_id(installation_id)
        self.assertIsNotNone(org)
        org_id = org["id"]

        # Owner should be the only owner
        self.assertEqual(self.get_user_groups(org_id, owner_email), ["Owners"])

        # Try to change the only owner to USER role
        event = {
            "id": str(uuid4()),
            "type": "marketplace.member.changed",
            "createdAt": int(time.time() * 1000),
            "payload": {
                "configuration": {"id": f"cfg_{uuid4()}"},
                "installationId": installation_id,
                "memberId": owner_claims["user_id"],
                "role": "USER",
            },
        }

        response = self._send_webhook(event, expect_error=True)
        self.assertEqual(response.status_code, 400)
        self.assertIn("error", response.json())
        self.assertIn("Cannot remove last owner from installation", response.json()["error"]["message"])

        # Verify user is still an owner
        self.assertEqual(self.get_user_groups(org_id, owner_email), ["Owners"])

    def test_webhook_marketplace_member_changed_to_none(self):
        """Test that marketplace.member.changed webhook with NONE role removes user from org."""

        # Create installation with owner
        installation_id, owner_email, owner_name, owner_claims = get_details_from_user_claims()

        put_body = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": f"test_access_token_{installation_id}",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": owner_email,
                    "name": owner_name,
                },
            },
        }

        put_response = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id}",
            claims=owner_claims,
            auth_type="user",
            body=put_body,
            headers={"Idempotency-Key": f"idempotency_{installation_id}"},
        )
        self.assertEqual(put_response.status_code, 200)

        # Get org that was created
        org = self.get_org_by_installation_id(installation_id)
        self.assertIsNotNone(org)
        org_id = org["id"]

        # Create a resource
        resource_body = {
            "productId": "traces",
            "name": f"test-traces-{installation_id[:8]}",
            "metadata": {},
            "billingPlanId": "free",
        }

        resource_response = self.make_vercel_request(
            "POST",
            f"/installations/{installation_id}/resources",
            claims=owner_claims,
            body=resource_body,
            auth_type="user",
        )
        self.assertEqual(resource_response.status_code, 201)

        # Create a regular member using addMembers
        member_email = generate_vercel_user_email()
        member_vercel_id = f"user_{str(uuid4())}"
        self._add_vercel_user_to_org(org_id, owner_email, member_email, member_vercel_id)

        # Verify both users are in the org
        members_before = self.get_org_members(org_id)
        member_emails_before = [m["email"] for m in members_before]
        self.assertIn(owner_email, member_emails_before)
        self.assertIn(member_email, member_emails_before)

        # Remove the regular member via webhook
        event = {
            "id": str(uuid4()),
            "type": "marketplace.member.changed",
            "createdAt": int(time.time() * 1000),
            "payload": {
                "configuration": {"id": f"cfg_{uuid4()}"},
                "installationId": installation_id,
                "memberId": member_vercel_id,
                "role": "NONE",
            },
        }

        response = self._send_webhook(event)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["success"], True)

        # Verify the member is completely removed from the org
        members_after = self.get_org_members(org_id)
        member_emails_after = [m["email"] for m in members_after]
        self.assertNotIn(member_email, member_emails_after)

        # Owner should still be in the org
        self.assertIn(owner_email, member_emails_after)
        self.assertTrue(self.check_user_is_org_owner(owner_email, org_id))

    def test_webhook_marketplace_member_changed_to_none_last_owner_rejected(self):
        """Test that webhook rejects removing the last owner from org."""

        # Create installation with only one owner
        installation_id, owner_email, owner_name, owner_claims = get_details_from_user_claims()

        put_body = {
            "scopes": ["read", "write"],
            "acceptedPolicies": {
                "eula": datetime.now(timezone.utc).isoformat(),
                "privacy": datetime.now(timezone.utc).isoformat(),
            },
            "credentials": {
                "access_token": f"test_access_token_{installation_id}",
                "token_type": "Bearer",
            },
            "account": {
                "name": generate_vercel_account_name(),
                "url": "https://example.com",
                "contact": {
                    "email": owner_email,
                    "name": owner_name,
                },
            },
        }

        put_response = self.make_vercel_request(
            "PUT",
            f"/installations/{installation_id}",
            claims=owner_claims,
            auth_type="user",
            body=put_body,
            headers={"Idempotency-Key": f"idempotency_{installation_id}"},
        )
        self.assertEqual(put_response.status_code, 200)

        # Get org that was created
        org = self.get_org_by_installation_id(installation_id)
        self.assertIsNotNone(org)
        org_id = org["id"]

        # Owner should be the only owner
        self.assertEqual(self.get_user_groups(org_id, owner_email), ["Owners"])

        # Try to remove the only owner
        event = {
            "id": str(uuid4()),
            "type": "marketplace.member.changed",
            "createdAt": int(time.time() * 1000),
            "payload": {
                "configuration": {"id": f"cfg_{uuid4()}"},
                "installationId": installation_id,
                "memberId": owner_claims["user_id"],
                "role": "NONE",
            },
        }

        response = self._send_webhook(event, expect_error=True)
        self.assertEqual(response.status_code, 400)
        self.assertIn("error", response.json())
        self.assertIn("Cannot remove last owner from installation", response.json()["error"]["message"])

        # Verify user is still in the org and still an owner
        members = self.get_org_members(org_id)
        member_emails = [m["email"] for m in members]
        self.assertIn(owner_email, member_emails)
        self.assertEqual(self.get_user_groups(org_id, owner_email), ["Owners"])
